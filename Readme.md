# Monoova Payments for WooCommerce

**Contributors:** Monoova  
**Tags:** woocommerce, payments, gateway, monoova, payid, npp, credit card, direct debit  
**Requires at least:** 6.1  
**Tested up to:** 8.0.0  
**Requires PHP:** 7.2  
**Stable tag:** 1.0.6
**License:** GPLv2 or later  
**License URI:** https://www.gnu.org/licenses/gpl-2.0.html  

Accept secure payments using Monoova's Card, PayID, direct bank transfers, Apple Pay, and Google Pay.

## Description

This plugin provides a seamless integration between WooCommerce and Monoova, allowing you to accept a variety of payment methods directly on your store. It leverages Monoova's robust APIs for secure and reliable payment processing. The plugin supports both traditional checkout and the modern WooCommerce block-based checkout experience.

### Key Features:

*   Accept major credit and debit cards.
*   Offer PayID and direct bank transfers for Australian customers.
*   Support for express checkout options like Credit/Debit cards, Apple Pay and Google Pay.
*   Seamless integration with WooCommerce Blocks for a modern checkout experience.
*   Real-time transaction status updates for PayID payments.
*   Centralized dashboard in WP-Admin to view transaction history and refund information.
*   Secure webhook handling for payment status synchronization.
*   Customizable checkout experience.

## Installation

### Prerequisites

*   WordPress 6.0 or higher
*   WooCommerce 6.1 or higher
*   PHP 7.2 or higher
*   A Monoova account (mAccount, API Key)

### Setup and Installation

1.  **Download the plugin:** Download the latest version of the plugin as a ZIP file.
2.  **Install the plugin:**
    *   In your WordPress admin dashboard, navigate to `Plugins` > `Add New`.
    *   Click `Upload Plugin` and choose the ZIP file you downloaded.
    *   Click `Install Now` and then `Activate Plugin`.
3.  **Configure the plugin:**
    *   Navigate to `WooCommerce` > `Settings` > `Payments`.
    *   You will see different Monoova payment methods (Card, PayID, etc.).
    *   Click `Manage` on the Monoova Payments option.
    *   Enter your Monoova API credentials and configure the settings as needed.

## For Developers

### Dependencies and Build Process

This project uses `npm` to manage JavaScript dependencies and `@wordpress/scripts` for building assets.

1.  **Install Dependencies:**
    Navigate to the plugin's root directory in your terminal and run:
    ```bash
    npm install
    ```

2.  **Build for Production:**
    To compile and minify the JavaScript and CSS assets for production, run:
    ```bash
    npm run build
    ```

3.  **Development Mode:**
    To watch for changes in asset files and automatically rebuild them during development, run:
    ```bash
    npm run start
    ```

### Source Code Structure

The plugin is organized into the following structure:

```
/
|-- assets/                 # Frontend assets
|   |-- css/                # Compiled CSS files
|   |-- images/             # Plugin images
|   |-- js/                 # JavaScript files
|       |-- build/          # Compiled JS files
|       |-- src/            # Source JS files
|           |-- admin/      # Admin-specific JS
|           |-- blocks/     # WooCommerce blocks integration JS

|
|-- includes/               # Backend PHP classes
|   |-- admin/              # Admin-related classes and views
|   |   |-- views/          # Admin dashboard and settings page templates
|   |-- api/                # API communication classes
|   |-- blocks/             # WooCommerce blocks integration classes
|   |-- class-monoova-*.php # Core gateway and handler classes
|
|-- templates/              # Frontend templates
|   |-- checkout/           # Checkout-related templates
|
|-- monoova-payments-for-woocommerce.php # Main plugin file
|-- package.json            # NPM dependencies and scripts
|-- template-primer-redirect.php # Redirect page template for Monoova checkout SDK
|-- webpack.config.js       # Webpack configuration
```

## Architecture

The plugin is built on a modular architecture, separating concerns for maintainability and extensibility.

*   **Main Plugin File (`monoova-payments-for-woocommerce.php`):** The entry point of the plugin. It handles initialization, hooks, and loading of all other files.
*   **Gateway Classes (`includes/class-monoova-*-gateway.php`):** Each payment method (Card, PayID, Unified) is implemented as a separate WooCommerce payment gateway class. These classes handle payment processing logic, settings configuration, and integration with the WooCommerce checkout flow.
*   **API Layer (`includes/api/class-monoova-api.php`):** A dedicated class that abstracts all communication with the Monoova APIs. This provides a single point of contact for making API requests.
*   **Block Integrations (`includes/blocks/` & `assets/js/src/blocks/`):** These files handle the integration with the WooCommerce block-based checkout. Each payment method has a PHP class to register the payment method type and a corresponding JavaScript file to render the payment method interface on the checkout block.
*   **Webhook Handler (`includes/class-monoova-webhook-handler.php`):** A class responsible for processing incoming webhooks from Monoova to update order statuses in real-time.
*   **Admin Interface (`includes/admin/`):** Manages the plugin's admin-facing features, including the settings pages and the reporting dashboard.
*   **Frontend Scripts:** JavaScript files in `assets/js/src/` handle client-side interactions, such as the Primer SDK integration for card payments and AJAX polling for PayID payments.

### Technologies and Tools

*   **Backend:** PHP
*   **Frontend:** JavaScript (ES6+), React.js, SCSS
*   **Build Tools:** Webpack, Babel (via `@wordpress/scripts`)
*   **Dependencies:**
    *   `@wordpress/scripts`: For building and bundling assets.
    *   `@wordpress/element`: WordPress abstraction for React.
    *   `@wordpress/data`: For state management in the block editor.
    *   `jquery`: For legacy parts of WooCommerce and AJAX.
*   **External Services:**
    *   **Monoova Card SDK:** The card payment flow is integrated with the Monoova SDK for a secure and unified checkout experience.

## Payment Flows

### Admin Settings Flow

1.  The admin navigates to `WooCommerce` > `Settings` > `Payments`.
2.  They select one of the Monoova payment gateways to manage.
3.  The settings page, rendered by `includes/admin/class-monoova-admin-settings-handler.php`, allows the admin to enable/disable the gateway, set titles, descriptions, and enter API credentials.
4.  Upon saving, the settings are stored in the WordPress options table.

### Card Payment Flow (Block-based Checkout)

1.  On the block-based checkout page, the `monoova-card-block.js` script is loaded.
2.  This script registers a new payment method with WooCommerce Blocks.
3.  When the user selects the Monoova Card payment method, the component rendered by the script displays the necessary card input fields.
4.  The payment processing is handled via a redirect to a page using the `template-primer-redirect.php` template.
5.  On this redirect page, `primer-payment-handler.js` initializes the Monoova SDK with a client token fetched from the server.
6.  Primer's Universal Checkout is displayed, handling the card details securely.
7.  Upon completion, Primer notifies our script, which then finalizes the payment and redirects the user to the order confirmation page.

### Express Checkout (Card Payment / Apple Pay / Google Pay)

Express checkout is available through the Card payment gateway and is facilitated by Monoova SDK.

1.  If enabled in the admin settings, the Primer Universal Checkout will automatically show Apple Pay or Google Pay buttons if the user's device and browser support them.
2.  The payment process is similar to the standard card flow, but the user authorizes the payment through their device instead of entering card details manually.

### PayID/Bank Transfer Flow (Block-based Checkout)

1.  The `monoova-payid-block.js` script registers the PayID payment method for the block-based checkout.
2.  When the user selects PayID and places the order, the server generates a unique PayID and payment instructions.
3.  The user is redirected to the "Order Received" (thank you) page, where the payment instructions are displayed via the `templates/checkout/monoova-payid-instructions.php` template.
4.  The `monoova-payid-thankyou.js` script starts AJAX polling in the background on the thank you page.
5.  This script periodically checks the order status with the server.
6.  Once the user completes the payment and Monoova notifies the server (usually via webhook), the order status is updated. The AJAX poll will then detect the change and update the status on the thank you page in real-time, informing the user that the payment has been received.

## Webhook Handling

1.  The plugin exposes a webhook endpoint at `/?wc-api/monoova_webhook`. The full URL is displayed in the Monoova dashboard in WP-Admin.
2.  This URL must be configured in your Monoova merchant dashboard.
3.  When a payment event occurs (e.g., a PayID payment is completed), Monoova sends a notification to this endpoint.
4.  The `Monoova_Webhook_Handler` class processes the incoming request, verifies it, and updates the corresponding WooCommerce order status accordingly. This ensures that order statuses are always synchronized with the payment status.

## Reporting Dashboard

The plugin includes a dashboard page under the main `Monoova` menu in the WordPress admin area. This dashboard is rendered by `includes/admin/views/html-admin-dashboard.php`.

It provides merchants with a quick overview of their sales and transactions processed through Monoova, including:
*   Recent transactions list.
*   Statistics on different payment methods.
*   Information about refunds.
*   Easy access to webhook setup URL and API information.
