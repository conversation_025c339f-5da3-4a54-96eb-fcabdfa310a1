{"version": 3, "file": "monoova-card-express-block.js", "mappings": ";;;;;;;;;;AAAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;UCAA;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;;WCtBA;WACA;WACA;WACA;WACA;WACA,iCAAiC,WAAW;WAC5C;WACA;;;;;WCPA;WACA;WACA;WACA;WACA,yCAAyC,wCAAwC;WACjF;WACA;WACA;;;;;WCPA;;;;;WCAA;WACA;WACA;WACA,uDAAuD,iBAAiB;WACxE;WACA,gDAAgD,aAAa;WAC7D;;;;;;;;;;;;;;;;;;;;;;;;;ACNA;AACA;AACA;AAC4E;AAClB;AACP;AACd;AACqC;AAC9B;AACiB;;AAE7D;AACA;AACA;AACA,IAAI,OAAOA,sFAA4B,KAAK,WAAW,EAAE;EACrDS,OAAO,CAACC,IAAI,CAAC,yHAAyH,CAAC;AAC3I,CAAC,MAAM,IAAI,CAACC,MAAM,CAACC,EAAE,IAAI,CAACD,MAAM,CAACC,EAAE,CAACC,OAAO,EAAE;EACzCJ,OAAO,CAACK,KAAK,CAAC,4CAA4C,CAAC;AAC/D,CAAC,MAAM;EACH,IAAI,OAAOV,6DAAa,KAAK,WAAW,IAAI,OAAOC,wDAAQ,KAAK,WAAW,IAAI,OAAOC,2DAAW,KAAK,WAAW,EAAE;IAC/GG,OAAO,CAACK,KAAK,CAAC,wCAAwC,CAAC;;IAEvD;IACA,MAAMC,SAAS,GAAGJ,MAAM,CAACC,EAAE,CAACC,OAAO;IACnC,IAAIE,SAAS,IAAIA,SAAS,CAACX,aAAa,IAAIW,SAAS,CAACV,QAAQ,IAAIU,SAAS,CAACT,WAAW,EAAE;MACrF,MAAMU,eAAe,GAAGD,SAAS,CAACX,aAAa;MAC/C,MAAMa,UAAU,GAAGF,SAAS,CAACV,QAAQ;MACrC,MAAMa,aAAa,GAAGH,SAAS,CAACT,WAAW;;MAE3C;MACAa,yBAAyB,CAACH,eAAe,EAAEC,UAAU,EAAEC,aAAa,CAAC;IACzE,CAAC,MAAM;MACHT,OAAO,CAACK,KAAK,CAAC,wCAAwC,CAAC;IAC3D;EACJ,CAAC,MAAM;IACH;IACAK,yBAAyB,CAAC,CAAC;EAC/B;AACJ;;AAEA;AACA;AACA;AACA,SAASA,yBAAyBA,CAAA,EAAG;EAEjC;EACA,IAAIC,QAAQ,GAAG,CAAC,CAAC;EACjB,IAAI;IACA;IACA,IAAI,OAAOlB,6DAAU,KAAK,WAAW,EAAE;MACnC;MACAkB,QAAQ,GAAGlB,iEAAU,CAAC,2BAA2B,EAAE,CAAC,CAAC,CAAC;IAC1D,CAAC,MAAM;MACH,MAAM,IAAImB,KAAK,CAAC,mCAAmC,CAAC;IACxD;EACJ,CAAC,CAAC,OAAOP,KAAK,EAAE;IACZL,OAAO,CAACK,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;IAClE;IACAM,QAAQ,GAAGT,MAAM,CAACW,kCAAkC,IAAI,CAAC,CAAC;EAC9D;EAEA,IAAI,CAACF,QAAQ,IAAI,OAAOA,QAAQ,KAAK,QAAQ,IAAIG,MAAM,CAACC,IAAI,CAACJ,QAAQ,CAAC,CAACK,MAAM,KAAK,CAAC,EAAE;IACjFhB,OAAO,CAACC,IAAI,CAAC,kEAAkE,CAAC;IAChFU,QAAQ,GAAG;MACPM,KAAK,EAAE,0BAA0B;MACjCC,WAAW,EAAE,yDAAyD;MACtEC,QAAQ,EAAE,CAAC,UAAU,CAAC;MACtBC,KAAK,EAAE,CAAC,QAAQ,EAAE,cAAc,CAAC;MACjCC,YAAY,EAAE,KAAK;MACnBC,cAAc,EAAE,KAAK;MACrBC,YAAY,EAAE;QACVC,MAAM,EAAE,EAAE;QACVC,YAAY,EAAE,CAAC;QACfC,KAAK,EAAE;MACX,CAAC;MACDC,IAAI,EAAE;QACFC,kBAAkB,EAAE,kBAAkB;QACtCC,UAAU,EAAE,uBAAuB;QACnCC,aAAa,EAAE,oEAAoE;QACnFC,cAAc,EAAE;MACpB;IACJ,CAAC;EACL;EAEA,MAAMC,YAAY,GAAGtC,mDAAE,CAAC,0BAA0B,EAAE,kCAAkC,CAAC;EACvF,MAAMuB,KAAK,GAAGzB,wEAAc,CAACmB,QAAQ,CAACM,KAAK,CAAC,IAAIe,YAAY;;EAE5D;AACJ;AACA;EACI,MAAMC,cAAc,GAAGA,CAAC;IACpBC,OAAO;IACPC,OAAO;IACPC,gBAAgB;IAChBC,OAAO;IACPC,YAAY;IACZC,QAAQ;IACRC,YAAY;IACZC,iBAAiB;IACjBC,mBAAmB;IACnBC,cAAc;IACdC,aAAa;IACb,GAAGC;EACP,CAAC,KAAK;IACF;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGnD,4DAAQ,CAAC,KAAK,CAAC;IACvD,MAAM,CAACoD,WAAW,EAAEC,cAAc,CAAC,GAAGrD,4DAAQ,CAAC,IAAI,CAAC;IAEpD,MAAM;MAAEsD;IAAQ,CAAC,GAAGpD,0DAAS,CAAEqD,MAAM,IAAK;MACtC,MAAMC,KAAK,GAAGD,MAAM,CAACpD,uEAAkB,CAAC;MACxC,OAAO;QACHmD,OAAO,EAAEE,KAAK,CAACC,UAAU,CAAC;MAC9B,CAAC;IACL,CAAC,CAAC;;IAEF;IACA,MAAMC,cAAc,GAAGjB,OAAO,EAAEiB,cAAc,IAAI,CAAC,CAAC;IACpD,MAAMC,eAAe,GAAGjB,YAAY,EAAEiB,eAAe,IAAI,CAAC,CAAC;IAC3D,MAAMC,SAAS,GAAGnB,OAAO,EAAEmB,SAAS,IAAI,CAAC,CAAC;;IAE1C;IACA,MAAMC,aAAa,GAAIC,OAAO,IAAK;MAC/B,OAAO;QACHC,UAAU,EAAED,OAAO,CAACC,UAAU,IAAI,EAAE;QACpCC,SAAS,EAAEF,OAAO,CAACE,SAAS,IAAI,EAAE;QAClCC,OAAO,EAAEH,OAAO,CAACG,OAAO,IAAI,EAAE;QAC9BC,SAAS,EAAEJ,OAAO,CAACI,SAAS,IAAI,EAAE;QAClCC,SAAS,EAAEL,OAAO,CAACK,SAAS,IAAI,EAAE;QAClCC,IAAI,EAAEN,OAAO,CAACM,IAAI,IAAI,EAAE;QACxBC,KAAK,EAAEP,OAAO,CAACO,KAAK,IAAI,EAAE;QAC1BC,QAAQ,EAAER,OAAO,CAACQ,QAAQ,IAAI,EAAE;QAChCC,OAAO,EAAET,OAAO,CAACS,OAAO,IAAI,EAAE;QAC9BC,KAAK,EAAEV,OAAO,CAACU,KAAK,IAAI,EAAE;QAC1BC,KAAK,EAAEX,OAAO,CAACW,KAAK,IAAI;MAC5B,CAAC;IACL,CAAC;IACD;IACA,MAAMC,WAAW,GAAG;MAChB9C,MAAM,EAAEY,gBAAgB,EAAEZ,MAAM,IAAIb,QAAQ,CAACY,YAAY,EAAEC,MAAM,IAAI,EAAE;MACvEC,YAAY,EAAEW,gBAAgB,EAAEX,YAAY,IAAId,QAAQ,CAACY,YAAY,EAAEE,YAAY,IAAI,CAAC;MACxF8C,eAAe,EAAE5D,QAAQ,CAACY,YAAY,EAAEG,KAAK,KAAK,SAAS,GAAG,SAAS,GAAG,MAAM;MAChFA,KAAK,EAAE,MAAM;MACb8C,MAAM,EAAE,MAAM;MACdC,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE,KAAK;MACjBC,MAAM,EAAE,SAAS;MACjBC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,QAAQ;MACxBC,GAAG,EAAE,KAAK;MACVC,OAAO,EAAE,QAAQ;MACjBC,SAAS,EAAE,YAAY;MACvBC,UAAU,EAAE;IAChB,CAAC;;IAED;IACA,MAAMC,oBAAoB,GAAGvF,+DAAW,CAAC,YAAY;MACjD,IAAIiD,YAAY,EAAE;MAElB,IAAI;QACAC,eAAe,CAAC,IAAI,CAAC;;QAErB;QACAb,OAAO,CAAC,CAAC;;QAET;QACA,MAAMmD,cAAc,GAAGC,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,QAAQ,CAAC,sBAAsB,CAAC,IAC1DvF,MAAM,CAACwF,QAAQ,CAACC,QAAQ,CAACC,QAAQ,CAAC,YAAY,CAAC,IAC/CN,QAAQ,CAACO,aAAa,CAAC,uBAAuB,CAAC,KAAK,IAAI;;QAG7E;QACA,MAAMC,WAAW,GAAGzD,OAAO,EAAEiB,cAAc,IAAI,CAAC,CAAC;QACjD,MAAMyC,YAAY,GAAGzD,YAAY,EAAEiB,eAAe,IAAIuC,WAAW,CAAC,CAAC;;QAEnE;QACA;QACA,MAAME,QAAQ,GAAG,MAAMC,KAAK,CAACtF,QAAQ,CAACuF,QAAQ,EAAE;UAC5CC,MAAM,EAAE,MAAM;UACdZ,IAAI,EAAE,IAAIa,eAAe,CAAC;YACtBC,MAAM,EAAE1F,QAAQ,CAAC2F,4BAA4B,IAAI,0BAA0B;YAC3EC,KAAK,EAAE5F,QAAQ,CAAC6F,sBAAsB;YACtCtD,OAAO,EAAEA,OAAO;YAChBI,cAAc,EAAEmD,IAAI,CAACC,SAAS,CAACjD,aAAa,CAACqC,WAAW,CAAC,CAAC;YAC1DvC,eAAe,EAAEkD,IAAI,CAACC,SAAS,CAACjD,aAAa,CAACsC,YAAY,CAAC,CAAC;YAC5DY,cAAc,EAAE,EAAE;YAAE;YACpBC,QAAQ,EAAE,MAAM;YAAE;YAClBvB,cAAc,EAAEA,cAAc,CAAC;UACnC,CAAC;QACL,CAAC,CAAC;QAEF,IAAI,CAACW,QAAQ,CAACa,EAAE,EAAE;UACd;UACA9D,eAAe,CAAC,KAAK,CAAC;UACtBZ,OAAO,CAAC,CAAC;UACT,MAAM,IAAIvB,KAAK,CAAC,6BAA6B,CAAC;QAElD;QAEA,MAAMkG,MAAM,GAAG,MAAMd,QAAQ,CAACe,IAAI,CAAC,CAAC;QAEpC,IAAI,CAACD,MAAM,CAACE,OAAO,EAAE;UACjB;UACAjE,eAAe,CAAC,KAAK,CAAC;UACtBZ,OAAO,CAAC,CAAC;UACT,MAAM,IAAIvB,KAAK,CAACkG,MAAM,CAACG,IAAI,EAAEC,OAAO,IAAIvG,QAAQ,CAACgB,IAAI,EAAEG,aAAa,IAAI,gBAAgB,CAAC;QAC7F;;QAEA;QACAmB,cAAc,CAAC6D,MAAM,CAACG,IAAI,CAAC;;QAE3B;QACA,MAAME,+BAA+B,CAACL,MAAM,CAACG,IAAI,CAAC;MAEtD,CAAC,CAAC,OAAO5G,KAAK,EAAE;QACZL,OAAO,CAACK,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C0C,eAAe,CAAC,KAAK,CAAC;;QAEtB;QACAZ,OAAO,CAAC,CAAC;;QAET;QACAiF,KAAK,CAAC/G,KAAK,CAAC6G,OAAO,IAAIvG,QAAQ,CAACgB,IAAI,EAAEG,aAAa,IAAI,gBAAgB,CAAC;MAC5E;IACJ,CAAC,EAAE,CAACgB,YAAY,EAAEZ,OAAO,EAAEC,OAAO,EAAEI,QAAQ,EAAEF,OAAO,EAAEC,YAAY,CAAC,CAAC;;IAErE;IACA,MAAM6E,+BAA+B,GAAG,MAAOE,YAAY,IAAK;MAC5D,IAAI;QACA;QACA,IAAI,CAACnH,MAAM,CAACoH,MAAM,EAAE;UAChB,MAAMC,aAAa,CAAC,CAAC;QACzB;;QAEA;QACA,IAAI,CAACF,YAAY,CAACG,WAAW,IAAI,CAACH,YAAY,CAACnE,OAAO,EAAE;UACpD,MAAM,IAAItC,KAAK,CAAC,2CAA2C,CAAC;QAChE;;QAEA;QACA,MAAM6G,MAAM,GAAGnC,QAAQ,CAAC3F,aAAa,CAAC,QAAQ,CAAC;QAC/C8H,MAAM,CAACC,EAAE,GAAG,gCAAgC;;QAE5C;QACA,MAAMC,WAAW,GAAGrC,QAAQ,CAAC3F,aAAa,CAAC,OAAO,CAAC;QACnDgI,WAAW,CAACC,WAAW,GAAG;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;QACDtC,QAAQ,CAACuC,IAAI,CAACC,WAAW,CAACH,WAAW,CAAC;QAEtC,MAAMI,WAAW,GAAGA,CAAA,KAAM;UACtB,IAAIzC,QAAQ,CAACC,IAAI,CAACE,QAAQ,CAACgC,MAAM,CAAC,EAAE;YAChCA,MAAM,CAACO,KAAK,CAAC,CAAC;YACd1C,QAAQ,CAACuC,IAAI,CAACI,WAAW,CAACN,WAAW,CAAC;YACtCrC,QAAQ,CAACC,IAAI,CAAC0C,WAAW,CAACR,MAAM,CAAC;UACrC;UACA1E,eAAe,CAAC,KAAK,CAAC;UACtBZ,OAAO,CAAC,CAAC;QACb,CAAC;QAED,MAAM+F,WAAW,GAAG5C,QAAQ,CAAC3F,aAAa,CAAC,QAAQ,CAAC;QACpDuI,WAAW,CAACC,SAAS,GAAG,GAAG;QAC3BD,WAAW,CAAC9G,KAAK,CAACgH,OAAO,GAAG;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;QACDF,WAAW,CAACG,OAAO,GAAG,MAAM;UACxBN,WAAW,CAAC,CAAC;QACjB,CAAC;QAED,MAAMO,eAAe,GAAGhD,QAAQ,CAAC3F,aAAa,CAAC,KAAK,CAAC;QACrD2I,eAAe,CAACZ,EAAE,GAAG,yBAAyB;QAC9CY,eAAe,CAAClH,KAAK,CAACmH,UAAU,GAAG,MAAM;QAEzCd,MAAM,CAACK,WAAW,CAACI,WAAW,CAAC;QAC/BT,MAAM,CAACK,WAAW,CAACQ,eAAe,CAAC;QACnChD,QAAQ,CAACC,IAAI,CAACuC,WAAW,CAACL,MAAM,CAAC;QAEjCA,MAAM,CAACe,SAAS,CAAC,CAAC;;QAElB;QACA,MAAMC,aAAa,GAAG;UAClBC,SAAS,EAAE,0BAA0B;UACrCC,2BAA2B,EAAE,KAAK;UAClCvH,KAAK,EAAE;YACHwH,UAAU,EAAE;cACRC,UAAU,EAAElI,QAAQ,CAACmI,kBAAkB,EAAEC,WAAW,EAAEC,WAAW,IAAI,8BAA8B;cACnGvE,QAAQ,EAAE9D,QAAQ,CAACmI,kBAAkB,EAAEC,WAAW,EAAEE,SAAS,IAAI,MAAM;cACvEvE,UAAU,EAAE/D,QAAQ,CAACmI,kBAAkB,EAAEC,WAAW,EAAEG,WAAW,IAAI,QAAQ;cAC7ExH,KAAK,EAAEf,QAAQ,CAACmI,kBAAkB,EAAEC,WAAW,EAAErH,KAAK,IAAI;YAC9D,CAAC;YACDyH,KAAK,EAAE;cACHC,IAAI,EAAE;gBACFP,UAAU,EAAElI,QAAQ,CAACmI,kBAAkB,EAAEK,KAAK,EAAEH,WAAW,IAAI,8BAA8B;gBAC7FvE,QAAQ,EAAE9D,QAAQ,CAACmI,kBAAkB,EAAEK,KAAK,EAAEF,SAAS,IAAI,MAAM;gBACjEvE,UAAU,EAAE/D,QAAQ,CAACmI,kBAAkB,EAAEK,KAAK,EAAED,WAAW,IAAI,QAAQ;gBACvEG,UAAU,EAAE1I,QAAQ,CAACmI,kBAAkB,EAAEK,KAAK,EAAEG,gBAAgB,IAAI,SAAS;gBAC7EC,WAAW,EAAE5I,QAAQ,CAACmI,kBAAkB,EAAEK,KAAK,EAAEK,YAAY,IAAI,SAAS;gBAC1E/H,YAAY,EAAEd,QAAQ,CAACmI,kBAAkB,EAAEK,KAAK,EAAEM,aAAa,IAAI,KAAK;gBACxE/H,KAAK,EAAEf,QAAQ,CAACmI,kBAAkB,EAAEK,KAAK,EAAEO,UAAU,IAAI;cAC7D;YACJ,CAAC;YACDC,YAAY,EAAE;cACVP,IAAI,EAAE;gBACF1H,KAAK,EAAEf,QAAQ,CAACmI,kBAAkB,EAAEc,aAAa,EAAEF,UAAU,IAAI,SAAS;gBAC1EL,UAAU,EAAE1I,QAAQ,CAACmI,kBAAkB,EAAEc,aAAa,EAAEP,UAAU,IAAI,SAAS;gBAC/E5H,YAAY,EAAEd,QAAQ,CAACmI,kBAAkB,EAAEc,aAAa,EAAEH,aAAa,IAAI,MAAM;gBACjFF,WAAW,EAAE5I,QAAQ,CAACmI,kBAAkB,EAAEc,aAAa,EAAEJ,YAAY,IAAI,SAAS;gBAClFX,UAAU,EAAElI,QAAQ,CAACmI,kBAAkB,EAAEc,aAAa,EAAEZ,WAAW,IAAI,8BAA8B;gBACrGvE,QAAQ,EAAE9D,QAAQ,CAACmI,kBAAkB,EAAEc,aAAa,EAAEX,SAAS,IAAI,MAAM;gBACzEvE,UAAU,EAAE/D,QAAQ,CAACmI,kBAAkB,EAAEc,aAAa,EAAEV,WAAW,IAAI,MAAM;gBAC7EW,SAAS,EAAE;cACf,CAAC;cACDC,QAAQ,EAAE;gBACNpI,KAAK,EAAE,SAAS;gBAChB2H,UAAU,EAAE;cAChB;YACJ,CAAC;YACDU,aAAa,EAAE;cACXrI,KAAK,EAAEf,QAAQ,CAACmI,kBAAkB,EAAEc,aAAa,EAAEP,UAAU,IAAI;YACrE;UACJ,CAAC;UACDW,YAAY,EAAE;YACVF,QAAQ,EAAE;UACd,CAAC;UACDG,aAAa,EAAE,KAAK;UAAE;UACtBC,kBAAkB,EAAE,MAAOjD,IAAI,IAAK;YAChCc,WAAW,CAAC,CAAC;YACb;YACA,MAAMoC,eAAe,GAAGlD,IAAI,EAAEmD,OAAO,EAAE1C,EAAE;YAEzC,IAAI,CAACyC,eAAe,EAAE;cAClB,MAAM,IAAIvJ,KAAK,CAAC,qCAAqC,CAAC;YAC1D;YACA;YACA,MAAMyJ,4BAA4B,CAACpD,IAAI,EAAEI,YAAY,CAAC;UAC1D,CAAC;UACDiD,cAAc,EAAEA,CAACjK,KAAK,EAAE;YAAE+J;UAAQ,CAAC,KAAK;YACpCpK,OAAO,CAACK,KAAK,CAAC,iCAAiC,EAAEA,KAAK,EAAE+J,OAAO,CAAC;YAEhErC,WAAW,CAAC,CAAC;YACb,IAAIiC,YAAY,GAAGrJ,QAAQ,CAACgB,IAAI,EAAEG,aAAa,IAAI,gBAAgB;YACnE,IAAIzB,KAAK,IAAIA,KAAK,CAAC6G,OAAO,EAAE;cACxB8C,YAAY,GAAG3J,KAAK,CAAC6G,OAAO;YAChC,CAAC,MAAM,IAAIkD,OAAO,IAAIA,OAAO,CAACG,SAAS,IAAIH,OAAO,CAACG,SAAS,CAACrD,OAAO,EAAE;cAClE8C,YAAY,GAAGI,OAAO,CAACG,SAAS,CAACrD,OAAO;YAC5C;YACAE,KAAK,CAAC4C,YAAY,CAAC;UACvB,CAAC;UACDQ,gBAAgB,EAAEA,CAAA,KAAM;YACpBxK,OAAO,CAACyK,GAAG,CAAC,mCAAmC,CAAC;YAChD1C,WAAW,CAAC,CAAC;UACjB,CAAC;UACD2C,qBAAqB,EAAGzD,IAAI,IAAK;YAC7BjH,OAAO,CAACyK,GAAG,CAAC,+CAA+C,EAAExD,IAAI,CAAC;YAClEc,WAAW,CAAC,CAAC;YACbX,KAAK,CAACzG,QAAQ,CAACgB,IAAI,EAAEgJ,WAAW,IAAI,8BAA8B,CAAC;UACvE,CAAC;UACDC,eAAe,EAAG3D,IAAI,IAAK;YACvBjH,OAAO,CAACyK,GAAG,CAAC,yCAAyC,EAAExD,IAAI,CAAC;YAC5Dc,WAAW,CAAC,CAAC;YACbX,KAAK,CAACzG,QAAQ,CAACgB,IAAI,EAAEkJ,cAAc,IAAI,gBAAgB,CAAC;UAC5D;QACJ,CAAC;;QAED;QACA,MAAM3K,MAAM,CAACoH,MAAM,CAACwD,qBAAqB,CAACzD,YAAY,CAACG,WAAW,EAAEiB,aAAa,CAAC;MAEtF,CAAC,CAAC,OAAOpI,KAAK,EAAE;QACZL,OAAO,CAACK,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;QACrE0C,eAAe,CAAC,KAAK,CAAC;QACtBZ,OAAO,CAAC,CAAC;QACTiF,KAAK,CAACzG,QAAQ,CAACgB,IAAI,EAAEG,aAAa,IAAI,gBAAgB,CAAC;MAC3D;IACJ,CAAC;;IAED;IACA,MAAMuI,4BAA4B,GAAG,MAAAA,CAAOU,UAAU,EAAE1D,YAAY,KAAK;MACrE,IAAI;QACA,MAAM8C,eAAe,GAAGY,UAAU,EAAEX,OAAO,EAAE1C,EAAE;QAC/C,MAAMsD,oBAAoB,GAAG3D,YAAY,EAAE4D,gCAAgC,IAAI,EAAE;QACjF,MAAM/H,OAAO,GAAGmE,YAAY,EAAEnE,OAAO;QAErC,IAAI,CAACA,OAAO,EAAE;UACV,MAAM,IAAItC,KAAK,CAAC,wBAAwB,CAAC;QAC7C;;QAEA;QACA,MAAMoF,QAAQ,GAAG,MAAMC,KAAK,CAACtF,QAAQ,CAACuF,QAAQ,EAAE;UAC5CC,MAAM,EAAE,MAAM;UACdZ,IAAI,EAAE,IAAIa,eAAe,CAAC;YACtBC,MAAM,EAAE1F,QAAQ,CAACuK,4BAA4B,IAAI,mCAAmC;YACpF3E,KAAK,EAAE5F,QAAQ,CAAC6F,sBAAsB;YACtCtD,OAAO,EAAEA,OAAO;YAChBiH,eAAe,EAAEA,eAAe;YAChCgB,SAAS,EAAEH;UACf,CAAC;QACL,CAAC,CAAC;QAEF,MAAMlE,MAAM,GAAG,MAAMd,QAAQ,CAACe,IAAI,CAAC,CAAC;QAEpC,IAAID,MAAM,CAACE,OAAO,EAAE;UAChB;UACA9G,MAAM,CAACwF,QAAQ,CAAC0F,IAAI,GAAGtE,MAAM,CAACG,IAAI,CAACoE,YAAY;QACnD,CAAC,MAAM;UACH,MAAM,IAAIzK,KAAK,CAACkG,MAAM,CAACG,IAAI,EAAEC,OAAO,IAAI,2BAA2B,CAAC;QACxE;MAEJ,CAAC,CAAC,OAAO7G,KAAK,EAAE;QACZL,OAAO,CAACK,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;QACzD0C,eAAe,CAAC,KAAK,CAAC;QACtBZ,OAAO,CAAC,CAAC;QACTiF,KAAK,CAAC/G,KAAK,CAAC6G,OAAO,IAAIvG,QAAQ,CAACgB,IAAI,EAAEG,aAAa,IAAI,2BAA2B,CAAC;MACvF;IACJ,CAAC;;IAED;IACA,MAAMyF,aAAa,GAAGA,CAAA,KAAM;MACxB,OAAO,IAAI+D,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACpC,IAAItL,MAAM,CAACoH,MAAM,EAAE;UACfiE,OAAO,CAAC,CAAC;UACT;QACJ;;QAEA;QACA;QACA;;QAEA;QACA,MAAME,IAAI,GAAGnG,QAAQ,CAAC3F,aAAa,CAAC,MAAM,CAAC;QAC3C8L,IAAI,CAACC,GAAG,GAAG,YAAY;QACvBD,IAAI,CAACL,IAAI,GAAG,gDAAgD;QAC5D9F,QAAQ,CAACuC,IAAI,CAACC,WAAW,CAAC2D,IAAI,CAAC;QAE/B,MAAME,MAAM,GAAGrG,QAAQ,CAAC3F,aAAa,CAAC,QAAQ,CAAC;QAC/CgM,MAAM,CAACC,GAAG,GAAG,iDAAiD;QAC9DD,MAAM,CAACE,WAAW,GAAG,WAAW;QAEhCF,MAAM,CAACG,MAAM,GAAGP,OAAO;QACvBI,MAAM,CAACI,OAAO,GAAGP,MAAM;QACvBlG,QAAQ,CAACuC,IAAI,CAACC,WAAW,CAAC6D,MAAM,CAAC;MACrC,CAAC,CAAC;IACN,CAAC;IAED,OAAOhM,iEAAa,CAAC,QAAQ,EAAE;MAC3ByB,KAAK,EAAEkD,WAAW;MAClBpC,OAAO,EAAEkD,oBAAoB;MAC7B0E,QAAQ,EAAEhH,YAAY;MACtBkJ,SAAS,EAAE;IACf,CAAC,EAAE;IACC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACArM,iEAAa,CAAC,MAAM,EAAE;MAClBsM,GAAG,EAAE;IACT,CAAC,EAAEnJ,YAAY,GACRnC,QAAQ,CAACgB,IAAI,EAAEE,UAAU,IAAI,eAAe,GAC5ClB,QAAQ,CAACgB,IAAI,EAAEC,kBAAkB,IAAI,kBAC5C,CAAC,CACJ,CAAC;EACN,CAAC;;EAED;AACJ;AACA;EACI,MAAMsK,WAAW,GAAGA,CAAC;IAAE9J;EAAiB,CAAC,KAAK;IAC1C,MAAMkC,WAAW,GAAG;MAChB9C,MAAM,EAAEY,gBAAgB,EAAEZ,MAAM,IAAIb,QAAQ,CAACY,YAAY,EAAEC,MAAM,IAAI,EAAE;MACvEC,YAAY,EAAEW,gBAAgB,EAAEX,YAAY,IAAId,QAAQ,CAACY,YAAY,EAAEE,YAAY,IAAI,CAAC;MACxF8C,eAAe,EAAE,MAAM;MACvB7C,KAAK,EAAE,MAAM;MACb8C,MAAM,EAAE,MAAM;MACdC,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE,KAAK;MACjBC,MAAM,EAAE,aAAa;MACrBC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,QAAQ;MACxBC,GAAG,EAAE,KAAK;MACVC,OAAO,EAAE,QAAQ;MACjBC,SAAS,EAAE;IACf,CAAC;IAED,OAAOvF,iEAAa,CAAC,QAAQ,EAAE;MAC3ByB,KAAK,EAAEkD,WAAW;MAClBwF,QAAQ,EAAE,IAAI;MACdkC,SAAS,EAAE;IACf,CAAC,EAAE,CACCrL,QAAQ,CAACwL,KAAK,GAAG,CAAC,CAAC,IAAIxM,iEAAa,CAAC,KAAK,EAAE;MACxCsM,GAAG,EAAE,MAAM;MACXL,GAAG,EAAEjL,QAAQ,CAACwL,KAAK,CAAC,CAAC,CAAC;MACtBC,GAAG,EAAE,SAAS;MACdhL,KAAK,EAAE;QACHI,MAAM,EAAE,MAAM;QACdoD,KAAK,EAAE;MACX;IACJ,CAAC,CAAC,EACFjF,iEAAa,CAAC,MAAM,EAAE;MAClBsM,GAAG,EAAE;IACT,CAAC,EAAEtL,QAAQ,CAACgB,IAAI,EAAEC,kBAAkB,IAAI,kBAAkB,CAAC,CAC9D,CAAC;EACN,CAAC;;EAED;AACJ;AACA;AACA;EACI,MAAMyK,cAAc,GAAGA,CAAC;IACpBC,IAAI;IACJC,UAAU;IACVC,iBAAiB;IACjBjJ,eAAe;IACfD,cAAc;IACdmJ,uBAAuB;IACvBC;EACJ,CAAC,KAAK;IACF;IACA,MAAMrH,cAAc,GAAGC,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,QAAQ,CAAC,sBAAsB,CAAC,IACzDvF,MAAM,CAACwF,QAAQ,CAACC,QAAQ,CAACC,QAAQ,CAAC,YAAY,CAAC,IAC/CN,QAAQ,CAACO,aAAa,CAAC,uBAAuB,CAAC,KAAK,IAAI,IACxDP,QAAQ,CAACO,aAAa,CAAC,gCAAgC,CAAC,KAAK,IAAI;;IAEvF;IACA,MAAM8G,UAAU,GAAGrH,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,QAAQ,CAAC,kBAAkB,CAAC,IACrDvF,MAAM,CAACwF,QAAQ,CAACC,QAAQ,CAACC,QAAQ,CAAC,QAAQ,CAAC,IAC3CN,QAAQ,CAACO,aAAa,CAAC,mBAAmB,CAAC,KAAK,IAAI,IACpDP,QAAQ,CAACO,aAAa,CAAC,4BAA4B,CAAC,KAAK,IAAI;;IAE/E;IACA,OAAO8G,UAAU,IAAI,CAACtH,cAAc;EACxC,CAAC;;EAED;AACJ;AACA;EACI,MAAMuH,kBAAkB,GAAG;IACvBC,IAAI,EAAE,sBAAsB;IAC5B5L,KAAK,EAAEA,KAAK;IACZC,WAAW,EAAE1B,wEAAc,CAACmB,QAAQ,CAACO,WAAW,IAAI,EAAE,CAAC;IACvD4L,SAAS,EAAEnM,QAAQ,CAACmM,SAAS,IAAI,cAAc;IAAE;IACjDC,OAAO,EAAEpN,iEAAa,CAACsC,cAAc,CAAC;IACtC+K,IAAI,EAAErN,iEAAa,CAACuM,WAAW,CAAC;IAChCG,cAAc,EAAEA,cAAc;IAC9BY,eAAe,EAAE,cAAc;IAAE;IACjC9L,QAAQ,EAAE;MACN+L,QAAQ,EAAEvM,QAAQ,EAAEQ,QAAQ,IAAI,CAAC,UAAU,CAAC;MAC5CC,KAAK,EAAET,QAAQ,EAAES,KAAK,IAAI,CAAC,QAAQ,EAAE,cAAc;IACvD;EACJ,CAAC;;EAED;EACA,IAAI;IACA7B,0FAA4B,CAACqN,kBAAkB,CAAC;EACpD,CAAC,CAAC,OAAOvM,KAAK,EAAE;IACZL,OAAO,CAACK,KAAK,CAAC,yDAAyD,EAAEA,KAAK,CAAC;EACnF;AACJ,CAAC,CAAC,4C", "sources": ["webpack://monoova-payments-for-woocommerce/external var [\"wc\",\"wcBlocksData\"]", "webpack://monoova-payments-for-woocommerce/external var [\"wc\",\"wcBlocksRegistry\"]", "webpack://monoova-payments-for-woocommerce/external var [\"wc\",\"wcSettings\"]", "webpack://monoova-payments-for-woocommerce/external window [\"wp\",\"data\"]", "webpack://monoova-payments-for-woocommerce/external window [\"wp\",\"element\"]", "webpack://monoova-payments-for-woocommerce/external window [\"wp\",\"htmlEntities\"]", "webpack://monoova-payments-for-woocommerce/external window [\"wp\",\"i18n\"]", "webpack://monoova-payments-for-woocommerce/webpack/bootstrap", "webpack://monoova-payments-for-woocommerce/webpack/runtime/compat get default export", "webpack://monoova-payments-for-woocommerce/webpack/runtime/define property getters", "webpack://monoova-payments-for-woocommerce/webpack/runtime/hasOwnProperty shorthand", "webpack://monoova-payments-for-woocommerce/webpack/runtime/make namespace object", "webpack://monoova-payments-for-woocommerce/./assets/js/src/blocks/monoova-card-express-block.js"], "sourcesContent": ["module.exports = wc.wcBlocksData;", "module.exports = wc.wcBlocksRegistry;", "module.exports = wc.wcSettings;", "module.exports = window[\"wp\"][\"data\"];", "module.exports = window[\"wp\"][\"element\"];", "module.exports = window[\"wp\"][\"htmlEntities\"];", "module.exports = window[\"wp\"][\"i18n\"];", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "/**\n * External dependencies\n */\nimport { registerExpressPaymentMethod } from '@woocommerce/blocks-registry';\nimport { decodeEntities } from '@wordpress/html-entities';\nimport { getSetting } from '@woocommerce/settings';\nimport { __ } from '@wordpress/i18n';\nimport { createElement, useState, useCallback } from '@wordpress/element';\nimport { useSelect } from '@wordpress/data';\nimport { CHECKOUT_STORE_KEY } from '@woocommerce/block-data';\n\n/**\n * Defensive check for WooCommerce availability\n */\nif (typeof registerExpressPaymentMethod === 'undefined') {\n    console.warn('WooCommerce Blocks registerExpressPaymentMethod is not available. This may be due to edit mode or missing dependencies.');\n} else if (!window.wp || !window.wp.element) {\n    console.error('WordPress element library is not available');\n} else {\n    if (typeof createElement === 'undefined' || typeof useState === 'undefined' || typeof useCallback === 'undefined') {\n        console.error('Required React hooks are not available');\n        \n        // Try fallback to window.wp.element\n        const wpElement = window.wp.element;\n        if (wpElement && wpElement.createElement && wpElement.useState && wpElement.useCallback) {\n            const wpCreateElement = wpElement.createElement;\n            const wpUseState = wpElement.useState;\n            const wpUseCallback = wpElement.useCallback;\n            \n            // Proceed with main logic using fallback functions\n            initializeExpressCheckout(wpCreateElement, wpUseState, wpUseCallback);\n        } else {\n            console.error('Fallback wp.element also not available');\n        }\n    } else {\n        // Proceed with main logic using imported functions\n        initializeExpressCheckout();\n    }\n}\n\n/**\n * Initialize Express Checkout with provided React functions\n */\nfunction initializeExpressCheckout() {\n    \n    // Try to get settings from WooCommerce settings registry first, then fallback to global variable\n    let settings = {};\n    try {\n        // Check if getSetting is available before using it\n        if (typeof getSetting !== 'undefined') {\n            // The key should match the payment method name\n            settings = getSetting('monoova_card_express_data', {});\n        } else {\n            throw new Error('getSetting function not available');\n        }\n    } catch (error) {\n        console.error('getSetting not available, trying fallback:', error);\n        // Fallback to global variable if getSetting is not available (e.g., in edit mode)\n        settings = window.monoova_card_express_blocks_params || {};\n    }\n\n    if (!settings || typeof settings !== 'object' || Object.keys(settings).length === 0) {\n        console.warn(\"Monoova Card Express settings not found or empty, using defaults\");\n        settings = {\n            title: 'Monoova Express Checkout',\n            description: 'Pay quickly and securely with Monoova Express Checkout.',\n            supports: ['products'],\n            style: ['height', 'borderRadius'],\n            is_available: false,\n            user_logged_in: false,\n            button_style: {\n                height: 48,\n                borderRadius: 4,\n                color: 'primary'\n            },\n            i18n: {\n                express_pay_button: 'Pay with Monoova',\n                processing: 'Processing payment...',\n                generic_error: 'An error occurred while processing your payment. Please try again.',\n                login_required: 'Please log in to use express checkout.'\n            }\n        };\n    }\n\n    const defaultTitle = __('Monoova Express Checkout', 'monoova-payments-for-woocommerce');\n    const title = decodeEntities(settings.title) || defaultTitle;\n\n    /**\n     * Express Payment Content component\n     */\n    const ExpressContent = ({ \n        onClick, \n        onClose, \n        buttonAttributes,\n        billing,\n        shippingData,\n        cartData,\n        emitResponse,\n        eventRegistration,\n        activePaymentMethod,\n        checkoutStatus,\n        paymentStatus,\n        ...rest\n    }) => {\n        /* \n            {\n                \"cartData\": {\n                    \"cartItems\": [\n                        {\n                            \"key\": \"ea5d2f1c4608232e07d3aa3d998e5135\",\n                            \"id\": 64,\n                            \"type\": \"simple\",\n                            \"quantity\": 1,\n                            \"quantity_limits\": {\n                                \"minimum\": 1,\n                                \"maximum\": 9999,\n                                \"multiple_of\": 1,\n                                \"editable\": true\n                            },\n                            \"name\": \"Product 1\",\n                            \"short_description\": \"\",\n                            \"description\": \"<p>Product 1 description</p>\",\n                            \"sku\": \"\",\n                            \"low_stock_remaining\": null,\n                            \"backorders_allowed\": false,\n                            \"show_backorder_badge\": false,\n                            \"sold_individually\": false,\n                            \"permalink\": \"http://localhost:8881/?product=product-1\",\n                            \"images\": [],\n                            \"variation\": [],\n                            \"item_data\": [],\n                            \"prices\": {\n                                \"price\": \"100\",\n                                \"regular_price\": \"100\",\n                                \"sale_price\": \"100\",\n                                \"price_range\": null,\n                                \"currency_code\": \"AUD\",\n                                \"currency_symbol\": \"$\",\n                                \"currency_minor_unit\": 2,\n                                \"currency_decimal_separator\": \".\",\n                                \"currency_thousand_separator\": \",\",\n                                \"currency_prefix\": \"$\",\n                                \"currency_suffix\": \"\",\n                                \"raw_prices\": {\n                                    \"precision\": 6,\n                                    \"price\": \"1000000\",\n                                    \"regular_price\": \"1000000\",\n                                    \"sale_price\": \"1000000\"\n                                }\n                            },\n                            \"totals\": {\n                                \"line_subtotal\": \"100\",\n                                \"line_subtotal_tax\": \"0\",\n                                \"line_total\": \"100\",\n                                \"line_total_tax\": \"0\",\n                                \"currency_code\": \"AUD\",\n                                \"currency_symbol\": \"$\",\n                                \"currency_minor_unit\": 2,\n                                \"currency_decimal_separator\": \".\",\n                                \"currency_thousand_separator\": \",\",\n                                \"currency_prefix\": \"$\",\n                                \"currency_suffix\": \"\"\n                            },\n                            \"catalog_visibility\": \"visible\",\n                            \"extensions\": {}\n                        }\n                    ],\n                    \"cartFees\": [],\n                    \"extensions\": {}\n                },\n                \"emitResponse\": {\n                    \"noticeContexts\": {\n                        \"CART\": \"wc/cart\",\n                        \"CHECKOUT\": \"wc/checkout\",\n                        \"PAYMENTS\": \"wc/checkout/payments\",\n                        \"EXPRESS_PAYMENTS\": \"wc/checkout/express-payments\",\n                        \"CONTACT_INFORMATION\": \"wc/checkout/contact-information\",\n                        \"SHIPPING_ADDRESS\": \"wc/checkout/shipping-address\",\n                        \"BILLING_ADDRESS\": \"wc/checkout/billing-address\",\n                        \"SHIPPING_METHODS\": \"wc/checkout/shipping-methods\",\n                        \"CHECKOUT_ACTIONS\": \"wc/checkout/checkout-actions\",\n                        \"ORDER_INFORMATION\": \"wc/checkout/order-information\"\n                    },\n                    \"responseTypes\": {\n                        \"SUCCESS\": \"success\",\n                        \"FAIL\": \"failure\",\n                        \"ERROR\": \"error\"\n                    }\n                },\n                \"eventRegistration\": {},\n                \"activePaymentMethod\": \"monoova_card\",\n                \"billing\": {\n                    \"appliedCoupons\": [],\n                    \"billingAddress\": {\n                        \"first_name\": \"Nhan\",\n                        \"last_name\": \"Nguyen\",\n                        \"company\": \"\",\n                        \"address_1\": \"North Sydney\",\n                        \"address_2\": \"\",\n                        \"city\": \"North Sydney\",\n                        \"state\": \"NSW\",\n                        \"postcode\": \"2060\",\n                        \"country\": \"AU\",\n                        \"email\": \"<EMAIL>\",\n                        \"phone\": \"\"\n                    },\n                    \"billingData\": {\n                        \"first_name\": \"Nhan\",\n                        \"last_name\": \"Nguyen\",\n                        \"company\": \"\",\n                        \"address_1\": \"North Sydney\",\n                        \"address_2\": \"\",\n                        \"city\": \"North Sydney\",\n                        \"state\": \"NSW\",\n                        \"postcode\": \"2060\",\n                        \"country\": \"AU\",\n                        \"email\": \"<EMAIL>\",\n                        \"phone\": \"\"\n                    },\n                    \"cartTotal\": {\n                        \"label\": \"Total\",\n                        \"value\": 100\n                    },\n                    \"cartTotalItems\": [\n                        {\n                            \"key\": \"total_items\",\n                            \"label\": \"Subtotal:\",\n                            \"value\": 100,\n                            \"valueWithTax\": 100\n                        },\n                        {\n                            \"key\": \"total_fees\",\n                            \"label\": \"Fees:\",\n                            \"value\": 0,\n                            \"valueWithTax\": 0\n                        },\n                        {\n                            \"key\": \"total_discount\",\n                            \"label\": \"Discount:\",\n                            \"value\": 0,\n                            \"valueWithTax\": 0\n                        },\n                        {\n                            \"key\": \"total_tax\",\n                            \"label\": \"Taxes:\",\n                            \"value\": 0,\n                            \"valueWithTax\": 0\n                        }\n                    ],\n                    \"currency\": {\n                        \"code\": \"AUD\",\n                        \"symbol\": \"$\",\n                        \"thousandSeparator\": \",\",\n                        \"decimalSeparator\": \".\",\n                        \"minorUnit\": 2,\n                        \"prefix\": \"$\",\n                        \"suffix\": \"\"\n                    },\n                    \"customerId\": 0,\n                    \"displayPricesIncludingTax\": false\n                },\n                \"checkoutStatus\": {\n                    \"isCalculating\": false,\n                    \"isComplete\": false,\n                    \"isIdle\": true,\n                    \"isProcessing\": false\n                },\n                \"components\": {},\n                \"paymentStatus\": {\n                    \"isPristine\": true,\n                    \"isIdle\": true,\n                    \"isStarted\": false,\n                    \"isProcessing\": false,\n                    \"isFinished\": false,\n                    \"hasError\": false,\n                    \"hasFailed\": false,\n                    \"isSuccessful\": false,\n                    \"isReady\": false,\n                    \"isDoingExpressPayment\": false\n                },\n                \"shippingData\": {\n                    \"isSelectingRate\": false,\n                    \"needsShipping\": false,\n                    \"selectedRates\": {},\n                    \"shippingAddress\": {\n                        \"first_name\": \"Nhan\",\n                        \"last_name\": \"Nguyen\",\n                        \"company\": \"\",\n                        \"address_1\": \"North Sydney\",\n                        \"address_2\": \"\",\n                        \"city\": \"North Sydney\",\n                        \"state\": \"NSW\",\n                        \"postcode\": \"2060\",\n                        \"country\": \"AU\",\n                        \"phone\": \"\"\n                    },\n                    \"shippingRates\": [],\n                    \"shippingRatesLoading\": false\n                },\n                \"shippingStatus\": {\n                    \"shippingErrorStatus\": {\n                        \"isPristine\": true,\n                        \"isValid\": true,\n                        \"hasInvalidAddress\": false,\n                        \"hasError\": false\n                    },\n                    \"shippingErrorTypes\": {\n                        \"NONE\": \"none\",\n                        \"INVALID_ADDRESS\": \"invalid_address\",\n                        \"UNKNOWN\": \"unknown_error\"\n                    }\n                },\n                \"shouldSavePayment\": false\n            }\n        \n        */\n        const [isProcessing, setIsProcessing] = useState(false);\n        const [paymentData, setPaymentData] = useState(null);\n\n        const { orderId } = useSelect((select) => {\n            const store = select(CHECKOUT_STORE_KEY);\n            return {\n                orderId: store.getOrderId(),\n            };\n        });\n        \n        // Extract address data from the correct props structure\n        const billingAddress = billing?.billingAddress || {};\n        const shippingAddress = shippingData?.shippingAddress || {};\n        const cartTotal = billing?.cartTotal || {};\n        \n        // Helper function to format address for backend\n        const formatAddress = (address) => {\n            return {\n                first_name: address.first_name || '',\n                last_name: address.last_name || '',\n                company: address.company || '',\n                address_1: address.address_1 || '',\n                address_2: address.address_2 || '',\n                city: address.city || '',\n                state: address.state || '',\n                postcode: address.postcode || '',\n                country: address.country || '',\n                email: address.email || '',\n                phone: address.phone || ''\n            };\n        };\n        // Apply button attributes (height, borderRadius) from block settings\n        const buttonStyle = {\n            height: buttonAttributes?.height || settings.button_style?.height || 48,\n            borderRadius: buttonAttributes?.borderRadius || settings.button_style?.borderRadius || 4,\n            backgroundColor: settings.button_style?.color === 'primary' ? '#2271b1' : '#000',\n            color: '#fff',\n            border: 'none',\n            fontSize: '16px',\n            fontWeight: '600',\n            cursor: 'pointer',\n            width: '100%',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            gap: '8px',\n            padding: '0 16px',\n            boxSizing: 'border-box',\n            transition: 'all 0.2s ease'\n        };\n\n        // Handle express payment button click\n        const handleExpressPayment = useCallback(async () => {\n            if (isProcessing) return;\n\n            try {\n                setIsProcessing(true);\n                \n                // Signal to checkout that express payment method is taking over\n                onClick();\n\n                // Detect if we're on checkout page to prevent order duplication\n                const isCheckoutPage = document.body.classList.contains('woocommerce-checkout') || \n                                     window.location.pathname.includes('/checkout/') ||\n                                     document.querySelector('.woocommerce-checkout') !== null;\n                \n\n                // Extract billing and shipping addresses from the correct props structure\n                const billingAddr = billing?.billingAddress || {};\n                const shippingAddr = shippingData?.shippingAddress || billingAddr; // Fallback to billing if no shipping\n                \n                // CREATE ORDER: This is where the order gets created and we get the order ID\n                // Unlike regular checkout, express checkout creates the order before payment processing\n                const response = await fetch(settings.ajax_url, {\n                    method: 'POST',\n                    body: new URLSearchParams({\n                        action: settings.ajax_express_checkout_action || 'monoova_express_checkout',\n                        nonce: settings.express_checkout_nonce,\n                        orderId: orderId,\n                        billingAddress: JSON.stringify(formatAddress(billingAddr)),\n                        shippingAddress: JSON.stringify(formatAddress(shippingAddr)),\n                        shippingOption: '', // Will be selected during checkout\n                        cardType: 'visa', // Default to visa, can be made configurable\n                        isCheckoutPage: isCheckoutPage // Pass page context to server\n                    })\n                });\n\n                if (!response.ok) {\n                    // hide popup\n                    setIsProcessing(false);\n                    onClose();\n                    throw new Error('Network response was not ok');\n\n                }\n\n                const result = await response.json();\n\n                if (!result.success) {\n                    // hide popup\n                    setIsProcessing(false);\n                    onClose();\n                    throw new Error(result.data?.message || settings.i18n?.generic_error || 'Payment failed');\n                }\n\n                // Store payment data for completion\n                setPaymentData(result.data);\n\n                // Initialize Primer with the client token returned from the order creation\n                await initializePrimerExpressCheckout(result.data);\n\n            } catch (error) {\n                console.error('Express payment error:', error);\n                setIsProcessing(false);\n                \n                // Return control to checkout on error\n                onClose();\n                \n                // Show error message\n                alert(error.message || settings.i18n?.generic_error || 'Payment failed');\n            }\n        }, [isProcessing, onClick, onClose, cartData, billing, shippingData]);\n\n        // Initialize Primer for express checkout\n        const initializePrimerExpressCheckout = async (checkoutData) => {\n            try {\n                // Load Primer SDK if not already loaded\n                if (!window.Primer) {\n                    await loadPrimerSDK();\n                }\n\n                // checkoutData now contains orderId, clientToken, clientTransactionUniqueReference from ajax_express_checkout\n                if (!checkoutData.clientToken || !checkoutData.orderId) {\n                    throw new Error('Missing required payment data from server');\n                }\n\n                // Create the dialog element\n                const dialog = document.createElement('dialog');\n                dialog.id = 'primer-express-checkout-dialog';\n\n                // Create a style element for the dialog and its backdrop\n                const dialogStyle = document.createElement('style');\n                dialogStyle.textContent = `\n                    #primer-express-checkout-dialog {\n                        border: none;\n                        border-radius: 8px;\n                        padding: 20px;\n                        width: 100%;\n                        max-width: 500px;\n                        box-shadow: 0 5px 15px rgba(0,0,0,0.3);\n                        min-height: 300px;\n                        max-height: 90vh;\n                        overflow-x: hidden;\n                    }\n                    #primer-express-checkout-dialog::backdrop {\n                        background: rgba(0, 0, 0, 0.8);\n                    }\n                    /* only hide #primer-checkout-other-payment-methods (inside #primer-express-checkout) if it doesn't include class PrimerCheckout--enter and PrimerCheckout--entered */\n                    /* Hide other payment methods if not in enter state */\n                    #primer-express-checkout #primer-checkout-other-payment-methods:not(.PrimerCheckout--enter) {\n                        display: none;\n                    }\n                `;\n                document.head.appendChild(dialogStyle);\n\n                const closeDialog = () => {\n                    if (document.body.contains(dialog)) {\n                        dialog.close();\n                        document.head.removeChild(dialogStyle);\n                        document.body.removeChild(dialog);\n                    }\n                    setIsProcessing(false);\n                    onClose();\n                };\n\n                const closeButton = document.createElement('button');\n                closeButton.innerHTML = '×';\n                closeButton.style.cssText = `\n                    position: absolute;\n                    top: 10px;\n                    right: 15px;\n                    background: none;\n                    border: none;\n                    font-size: 24px;\n                    cursor: pointer;\n                    color: #666;\n                    z-index: 1;\n                `;\n                closeButton.onclick = () => {\n                    closeDialog();\n                };\n\n                const primerContainer = document.createElement('div');\n                primerContainer.id = 'primer-express-checkout';\n                primerContainer.style.paddingTop = '20px';\n\n                dialog.appendChild(closeButton);\n                dialog.appendChild(primerContainer);\n                document.body.appendChild(dialog);\n\n                dialog.showModal();\n\n                // Configure Primer options similar to primer-payment-handler.js\n                const primerOptions = {\n                    container: '#primer-express-checkout',\n                    clientSessionCachingEnabled: false,\n                    style: {\n                        inputLabel: {\n                            fontFamily: settings.checkout_ui_styles?.input_label?.font_family || 'Helvetica, Arial, sans-serif',\n                            fontSize: settings.checkout_ui_styles?.input_label?.font_size || '14px',\n                            fontWeight: settings.checkout_ui_styles?.input_label?.font_weight || 'normal',\n                            color: settings.checkout_ui_styles?.input_label?.color || '#000000',\n                        },\n                        input: {\n                            base: {\n                                fontFamily: settings.checkout_ui_styles?.input?.font_family || 'Helvetica, Arial, sans-serif',\n                                fontSize: settings.checkout_ui_styles?.input?.font_size || '14px',\n                                fontWeight: settings.checkout_ui_styles?.input?.font_weight || 'normal',\n                                background: settings.checkout_ui_styles?.input?.background_color || '#FAFAFA',\n                                borderColor: settings.checkout_ui_styles?.input?.border_color || '#E8E8E8',\n                                borderRadius: settings.checkout_ui_styles?.input?.border_radius || '8px',\n                                color: settings.checkout_ui_styles?.input?.text_color || '#000000',\n                            },\n                        },\n                        submitButton: {\n                            base: {\n                                color: settings.checkout_ui_styles?.submit_button?.text_color || '#000000',\n                                background: settings.checkout_ui_styles?.submit_button?.background || '#2ab5c4',\n                                borderRadius: settings.checkout_ui_styles?.submit_button?.border_radius || '10px',\n                                borderColor: settings.checkout_ui_styles?.submit_button?.border_color || '#2ab5c4',\n                                fontFamily: settings.checkout_ui_styles?.submit_button?.font_family || 'Helvetica, Arial, sans-serif',\n                                fontSize: settings.checkout_ui_styles?.submit_button?.font_size || '17px',\n                                fontWeight: settings.checkout_ui_styles?.submit_button?.font_weight || 'bold',\n                                boxShadow: 'none'\n                            },\n                            disabled: {\n                                color: '#9b9b9b',\n                                background: '#e1deda'\n                            }\n                        },\n                        loadingScreen: {\n                            color: settings.checkout_ui_styles?.submit_button?.background || '#2ab5c4'\n                        }\n                    },\n                    errorMessage: {\n                        disabled: true\n                    },\n                    successScreen: false, // Disable success screen since we handle redirects manually\n                    onCheckoutComplete: async (data) => {\n                        closeDialog();\n                        // Extract payment information\n                        const primerPaymentId = data?.payment?.id;\n                        \n                        if (!primerPaymentId) {\n                            throw new Error('Payment ID not received from Primer');\n                        }\n                        // Complete the order on the server using the new flow\n                        await handleExpressPaymentComplete(data, checkoutData);\n                    },\n                    onCheckoutFail: (error, { payment }) => {\n                        console.error('Primer express checkout failed:', error, payment);\n\n                        closeDialog();\n                        let errorMessage = settings.i18n?.generic_error || 'Payment failed';\n                        if (error && error.message) {\n                            errorMessage = error.message;\n                        } else if (payment && payment.processor && payment.processor.message) {\n                            errorMessage = payment.processor.message;\n                        }\n                        alert(errorMessage);\n                    },\n                    onCheckoutCancel: () => {\n                        console.log('Primer express checkout cancelled');\n                        closeDialog();\n                    },\n                    onAuthorizationFailed: (data) => {\n                        console.log('Primer express checkout authorization failed:', data);\n                        closeDialog();\n                        alert(settings.i18n?.auth_failed || 'Payment authorization failed');\n                    },\n                    onPaymentFailed: (data) => {\n                        console.log('Primer express checkout payment failed:', data);\n                        closeDialog();\n                        alert(settings.i18n?.payment_failed || 'Payment failed');\n                    }\n                };\n\n                // Initialize Primer Universal Checkout with the client token from express checkout\n                await window.Primer.showUniversalCheckout(checkoutData.clientToken, primerOptions);\n\n            } catch (error) {\n                console.error('Primer express checkout initialization error:', error);\n                setIsProcessing(false);\n                onClose();\n                alert(settings.i18n?.generic_error || 'Payment failed');\n            }\n        };\n\n        // Handle express payment completion\n        const handleExpressPaymentComplete = async (primerData, checkoutData) => {\n            try {\n                const primerPaymentId = primerData?.payment?.id;\n                const clientTransactionRef = checkoutData?.clientTransactionUniqueReference || '';\n                const orderId = checkoutData?.orderId;\n\n                if (!orderId) {\n                    throw new Error('Order ID not available');\n                }\n\n                // Complete the order on the server using the same flow as normal checkout\n                const response = await fetch(settings.ajax_url, {\n                    method: 'POST',\n                    body: new URLSearchParams({\n                        action: settings.ajax_complete_express_action || 'monoova_complete_express_checkout',\n                        nonce: settings.express_checkout_nonce,\n                        orderId: orderId,\n                        primerPaymentId: primerPaymentId,\n                        clientRef: clientTransactionRef\n                    })\n                });\n\n                const result = await response.json();\n\n                if (result.success) {\n                    // Redirect to success page\n                    window.location.href = result.data.redirect_url;\n                } else {\n                    throw new Error(result.data?.message || 'Payment completion failed');\n                }\n\n            } catch (error) {\n                console.error('Express payment completion error:', error);\n                setIsProcessing(false);\n                onClose();\n                alert(error.message || settings.i18n?.generic_error || 'Payment completion failed');\n            }\n        };\n\n        // Load Primer SDK dynamically\n        const loadPrimerSDK = () => {\n            return new Promise((resolve, reject) => {\n                if (window.Primer) {\n                    resolve();\n                    return;\n                }\n\n                // need to load 2 CDNs below:\n                // <link rel=\"stylesheet\" href=\"https://sdk.primer.io/web/v2.54.5/Checkout.css\" />\n                //<script src=\"https://sdk.primer.io/web/v2.54.5/Primer.min.js\" integrity=\"sha384-yUCj6Q8h0Q6zFc35iT7v7pFoqlgpBD/xVr5SQxOgAnu2Cq286mf7IAyrFBJ8OsIa\" crossorigin=\"anonymous\"></script>\n\n                // load css\n                const link = document.createElement('link');\n                link.rel = 'stylesheet';\n                link.href = 'https://sdk.primer.io/web/v2.54.5/Checkout.css';\n                document.head.appendChild(link);\n\n                const script = document.createElement('script');\n                script.src = 'https://sdk.primer.io/web/v2.54.5/Primer.min.js';\n                script.crossOrigin = 'anonymous';\n\n                script.onload = resolve;\n                script.onerror = reject;\n                document.head.appendChild(script);\n            });\n        };\n\n        return createElement('button', {\n            style: buttonStyle,\n            onClick: handleExpressPayment,\n            disabled: isProcessing,\n            className: 'monoova-express-pay-button'\n        }, [\n            // Monoova logo (optional)\n            // settings.icons?.[0] && createElement('img', {\n            //     key: 'logo',\n            //     src: settings.icons[0],\n            //     alt: 'Monoova',\n            //     style: {\n            //         height: '20px',\n            //         width: 'auto'\n            //     }\n            // }),\n            // Button text\n            createElement('span', {\n                key: 'text'\n            }, isProcessing \n                ? (settings.i18n?.processing || 'Processing...')\n                : (settings.i18n?.express_pay_button || 'Pay with Monoova')\n            )\n        ]);\n    };\n\n    /**\n     * Express Payment Edit component (for block editor)\n     */\n    const ExpressEdit = ({ buttonAttributes }) => {\n        const buttonStyle = {\n            height: buttonAttributes?.height || settings.button_style?.height || 48,\n            borderRadius: buttonAttributes?.borderRadius || settings.button_style?.borderRadius || 4,\n            backgroundColor: '#ccc',\n            color: '#666',\n            border: 'none',\n            fontSize: '16px',\n            fontWeight: '600',\n            cursor: 'not-allowed',\n            width: '100%',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            gap: '8px',\n            padding: '0 16px',\n            boxSizing: 'border-box'\n        };\n\n        return createElement('button', {\n            style: buttonStyle,\n            disabled: true,\n            className: 'monoova-express-pay-button monoova-express-pay-button--preview'\n        }, [\n            settings.icons?.[0] && createElement('img', {\n                key: 'logo',\n                src: settings.icons[0],\n                alt: 'Monoova',\n                style: {\n                    height: '20px',\n                    width: 'auto'\n                }\n            }),\n            createElement('span', {\n                key: 'text'\n            }, settings.i18n?.express_pay_button || 'Pay with Monoova')\n        ]);\n    };\n\n    /**\n     * Can make payment check\n     * Only show express checkout on cart page, hide on checkout page\n     */\n    const canMakePayment = ({\n        cart,\n        cartTotals,\n        cartNeedsShipping,\n        shippingAddress,\n        billingAddress,\n        selectedShippingMethods,\n        paymentRequirements\n    }) => {\n        // Check if we're on the checkout page\n        const isCheckoutPage = document.body.classList.contains('woocommerce-checkout') || \n                              window.location.pathname.includes('/checkout/') ||\n                              document.querySelector('.woocommerce-checkout') !== null ||\n                              document.querySelector('.wp-block-woocommerce-checkout') !== null;\n        \n        // Check if we're on the cart page\n        const isCartPage = document.body.classList.contains('woocommerce-cart') || \n                          window.location.pathname.includes('/cart/') ||\n                          document.querySelector('.woocommerce-cart') !== null ||\n                          document.querySelector('.wp-block-woocommerce-cart') !== null;\n        \n        // Only show express checkout on cart page, not on checkout page\n        return isCartPage && !isCheckoutPage;\n    };\n\n    /**\n     * Monoova Card Express payment method config object.\n     */\n    const MonoovaCardExpress = {\n        name: \"monoova_card_express\",\n        title: title,\n        description: decodeEntities(settings.description || ''),\n        gatewayId: settings.gatewayId || 'monoova_card', // Points to main gateway for settings\n        content: createElement(ExpressContent),\n        edit: createElement(ExpressEdit),\n        canMakePayment: canMakePayment,\n        paymentMethodId: 'monoova_card', // Use the main gateway for processing\n        supports: {\n            features: settings?.supports ?? ['products'],\n            style: settings?.style ?? ['height', 'borderRadius']\n        }\n    };\n\n    // Register the express payment method\n    try {\n        registerExpressPaymentMethod(MonoovaCardExpress);\n    } catch (error) {\n        console.error('Failed to register Monoova Card Express payment method:', error);\n    }\n} // End of initializeExpressCheckout function\n"], "names": ["registerExpressPaymentMethod", "decodeEntities", "getSetting", "__", "createElement", "useState", "useCallback", "useSelect", "CHECKOUT_STORE_KEY", "console", "warn", "window", "wp", "element", "error", "wpElement", "wpCreateElement", "wpUseState", "wpUseCallback", "initializeExpressCheckout", "settings", "Error", "monoova_card_express_blocks_params", "Object", "keys", "length", "title", "description", "supports", "style", "is_available", "user_logged_in", "button_style", "height", "borderRadius", "color", "i18n", "express_pay_button", "processing", "generic_error", "login_required", "defaultTitle", "ExpressContent", "onClick", "onClose", "buttonAttributes", "billing", "shippingData", "cartData", "emitResponse", "eventRegistration", "activePaymentMethod", "checkoutStatus", "paymentStatus", "rest", "isProcessing", "setIsProcessing", "paymentData", "setPaymentData", "orderId", "select", "store", "getOrderId", "billing<PERSON><PERSON>ress", "shippingAddress", "cartTotal", "formatAddress", "address", "first_name", "last_name", "company", "address_1", "address_2", "city", "state", "postcode", "country", "email", "phone", "buttonStyle", "backgroundColor", "border", "fontSize", "fontWeight", "cursor", "width", "display", "alignItems", "justifyContent", "gap", "padding", "boxSizing", "transition", "handleExpressPayment", "isCheckoutPage", "document", "body", "classList", "contains", "location", "pathname", "includes", "querySelector", "billingAddr", "shippingAddr", "response", "fetch", "ajax_url", "method", "URLSearchParams", "action", "ajax_express_checkout_action", "nonce", "express_checkout_nonce", "JSON", "stringify", "shippingOption", "cardType", "ok", "result", "json", "success", "data", "message", "initializePrimerExpressCheckout", "alert", "checkoutData", "Primer", "loadPrimerSDK", "clientToken", "dialog", "id", "dialogStyle", "textContent", "head", "append<PERSON><PERSON><PERSON>", "closeDialog", "close", "<PERSON><PERSON><PERSON><PERSON>", "closeButton", "innerHTML", "cssText", "onclick", "primerContainer", "paddingTop", "showModal", "primerOptions", "container", "clientSessionCachingEnabled", "inputLabel", "fontFamily", "checkout_ui_styles", "input_label", "font_family", "font_size", "font_weight", "input", "base", "background", "background_color", "borderColor", "border_color", "border_radius", "text_color", "submitButton", "submit_button", "boxShadow", "disabled", "loadingScreen", "errorMessage", "successScreen", "onCheckoutComplete", "primerPaymentId", "payment", "handleExpressPaymentComplete", "onCheckoutFail", "processor", "onCheckoutCancel", "log", "onAuthorizationFailed", "auth_failed", "onPaymentFailed", "payment_failed", "showUniversalCheckout", "primerData", "clientTransactionRef", "clientTransactionUniqueReference", "ajax_complete_express_action", "clientRef", "href", "redirect_url", "Promise", "resolve", "reject", "link", "rel", "script", "src", "crossOrigin", "onload", "onerror", "className", "key", "ExpressEdit", "icons", "alt", "canMakePayment", "cart", "cartTotals", "cartNeedsShipping", "selectedShippingMethods", "paymentRequirements", "isCartPage", "MonoovaCardExpress", "name", "gatewayId", "content", "edit", "paymentMethodId", "features"], "sourceRoot": ""}