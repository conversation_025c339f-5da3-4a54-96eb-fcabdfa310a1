{"version": 3, "file": "monoova-card-block.js", "mappings": ";;;;;;;;;;;;;;AAAO,MAAMA,uBAAuB,GAAGA,CAAC;EACpCC,WAAW;EACXC,SAAS,GAAG,KAAK;EACjBC,aAAa,GAAG,KAAK;EACrBC,YAAY,GAAG;AACnB,CAAC,KAAK;EACF,oBACIC,KAAA,CAAAC,aAAA;IAAKC,SAAS,EAAC,oBAAoB;IAACC,KAAK,EAAE;MAAEC,SAAS,EAAE;IAAO;EAAE,GAC5DP,SAAS,IAAI,CAACC,aAAa,iBACxBE,KAAA,CAAAC,aAAA;IAAKC,SAAS,EAAC,0BAA0B;IAACC,KAAK,EAAE;MAC7CE,OAAO,EAAE,MAAM;MACfC,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE;IACX;EAAE,GAAC,yBAEE,CACR,eAEDP,KAAA,CAAAC,aAAA;IAAKO,GAAG,EAAET,YAAa;IAACG,SAAS,EAAC;EAAyB,gBACvDF,KAAA,CAAAC,aAAA;IAAKQ,EAAE,EAAEb;EAAY,CAAM,CAC1B,CACJ,CAAC;AAEd,CAAC;;;;;;;;;;;;;;;;;ACvBsD;;AAEvD;AACA,MAAMgB,qBAAqB,CAAC;EACxBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,UAAU,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC3B,IAAI,CAACC,gBAAgB,GAAG,IAAIC,GAAG,CAAC,CAAC;IACjC,IAAI,CAACnB,aAAa,GAAG,KAAK;EAC9B;;EAEA;EACAoB,oBAAoBA,CAACC,eAAe,EAAEvB,WAAW,EAAE;IAC/C,MAAMwB,YAAY,GAAG,GAAGD,eAAe,IAAIvB,WAAW,EAAE;IAExD,IAAI,CAAC,IAAI,CAACkB,UAAU,CAACO,GAAG,CAACD,YAAY,CAAC,EAAE;MACpC;MACA,MAAME,SAAS,GAAGC,QAAQ,CAACtB,aAAa,CAAC,KAAK,CAAC;MAC/CqB,SAAS,CAACb,EAAE,GAAGb,WAAW;MAC1B0B,SAAS,CAACpB,SAAS,GAAG,sCAAsC;MAC5DoB,SAAS,CAACnB,KAAK,CAACqB,OAAO,GAAG;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;;MAED;MACAD,QAAQ,CAACE,IAAI,CAACC,WAAW,CAACJ,SAAS,CAAC;MAEpC,IAAI,CAACR,UAAU,CAACa,GAAG,CAACP,YAAY,EAAE;QAC9BQ,OAAO,EAAEN,SAAS;QAClBxB,aAAa,EAAE,KAAK;QACpB+B,SAAS,EAAE,KAAK;QAChBC,OAAO,EAAE,IAAI;QAAE;QACfC,WAAW,EAAE,IAAI,CAAC;MACtB,CAAC,CAAC;IACN;IAEA,OAAO,IAAI,CAACjB,UAAU,CAACkB,GAAG,CAACZ,YAAY,CAAC;EAC5C;;EAEA;EACAa,aAAaA,CAACd,eAAe,EAAEvB,WAAW,EAAEsC,aAAa,EAAE;IACvD,MAAMd,YAAY,GAAG,GAAGD,eAAe,IAAIvB,WAAW,EAAE;IACxD,MAAMuC,aAAa,GAAG,IAAI,CAACrB,UAAU,CAACkB,GAAG,CAACZ,YAAY,CAAC;IAEvD,IAAIe,aAAa,IAAIA,aAAa,CAACP,OAAO,EAAE;MACxC;MACA,IAAI,CAACQ,iBAAiB,CAAC,CAAC;;MAExB;MACA,IAAIF,aAAa,EAAE;QACfA,aAAa,CAACR,WAAW,CAACS,aAAa,CAACP,OAAO,CAAC;QAChDO,aAAa,CAACP,OAAO,CAACzB,KAAK,CAACqB,OAAO,GAAG;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;QACDW,aAAa,CAACN,SAAS,GAAG,IAAI;QAC9B,IAAI,CAACb,gBAAgB,CAACqB,GAAG,CAACjB,YAAY,CAAC;MAC3C;IACJ;EACJ;;EAEA;EACAkB,aAAaA,CAACnB,eAAe,EAAEvB,WAAW,EAAE;IACxC,MAAMwB,YAAY,GAAG,GAAGD,eAAe,IAAIvB,WAAW,EAAE;IACxD,MAAMuC,aAAa,GAAG,IAAI,CAACrB,UAAU,CAACkB,GAAG,CAACZ,YAAY,CAAC;IAEvD,IAAIe,aAAa,IAAIA,aAAa,CAACP,OAAO,EAAE;MACxC;MACAL,QAAQ,CAACE,IAAI,CAACC,WAAW,CAACS,aAAa,CAACP,OAAO,CAAC;MAChDO,aAAa,CAACP,OAAO,CAACzB,KAAK,CAACqB,OAAO,GAAG;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;MACDW,aAAa,CAACN,SAAS,GAAG,KAAK;MAC/B,IAAI,CAACb,gBAAgB,CAACuB,MAAM,CAACnB,YAAY,CAAC;IAC9C;EACJ;;EAEA;EACAgB,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACtB,UAAU,CAAC0B,OAAO,CAAC,CAACL,aAAa,EAAEf,YAAY,KAAK;MACrD,IAAIe,aAAa,CAACN,SAAS,EAAE;QACzB,IAAI,CAACS,aAAa,CAAClB,YAAY,CAACqB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAErB,YAAY,CAACqB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9E;IACJ,CAAC,CAAC;EACN;;EAEA;EACAC,cAAcA,CAACvB,eAAe,EAAEvB,WAAW,EAAE;IACzC,MAAMwB,YAAY,GAAG,GAAGD,eAAe,IAAIvB,WAAW,EAAE;IACxD,MAAMuC,aAAa,GAAG,IAAI,CAACrB,UAAU,CAACkB,GAAG,CAACZ,YAAY,CAAC;IAEvD,IAAIe,aAAa,EAAE;MACfA,aAAa,CAACL,OAAO,GAAG,IAAI;MAC5BK,aAAa,CAACJ,WAAW,GAAG,IAAI;MAChCI,aAAa,CAACQ,YAAY,GAAG,IAAI,CAAC,CAAC;MACnCR,aAAa,CAACrC,aAAa,GAAG,KAAK,CAAC,CAAC;IACzC;EACJ;;EAEA;EACA8C,uBAAuBA,CAACzB,eAAe,EAAEvB,WAAW,EAAE;IAClD,MAAMwB,YAAY,GAAG,GAAGD,eAAe,IAAIvB,WAAW,EAAE;IACxD,MAAMuC,aAAa,GAAG,IAAI,CAACrB,UAAU,CAACkB,GAAG,CAACZ,YAAY,CAAC;IACvD,IAAIe,aAAa,EAAE;MACfA,aAAa,CAACrC,aAAa,GAAG,IAAI;IACtC;EACJ;;EAEA;EACA+C,sBAAsBA,CAAC1B,eAAe,EAAEvB,WAAW,EAAE;IACjD,MAAMwB,YAAY,GAAG,GAAGD,eAAe,IAAIvB,WAAW,EAAE;IACxD,MAAMuC,aAAa,GAAG,IAAI,CAACrB,UAAU,CAACkB,GAAG,CAACZ,YAAY,CAAC;IACvD,OAAOe,aAAa,GAAGA,aAAa,CAACrC,aAAa,GAAG,KAAK;EAC9D;;EAEA;EACAgD,YAAYA,CAAC3B,eAAe,EAAEvB,WAAW,EAAEkC,OAAO,EAAEC,WAAW,EAAEY,YAAY,GAAG,IAAI,EAAE;IAClF,MAAMvB,YAAY,GAAG,GAAGD,eAAe,IAAIvB,WAAW,EAAE;IACxD,MAAMuC,aAAa,GAAG,IAAI,CAACrB,UAAU,CAACkB,GAAG,CAACZ,YAAY,CAAC;IACvD,IAAIe,aAAa,EAAE;MACfA,aAAa,CAACL,OAAO,GAAGA,OAAO;MAC/BK,aAAa,CAACJ,WAAW,GAAGA,WAAW;MACvCI,aAAa,CAACQ,YAAY,GAAGA,YAAY,CAAC,CAAC;IAC/C,CAAC,MAAM;MACHI,OAAO,CAACC,IAAI,CAAC,qCAAqC5B,YAAY,mCAAmC,CAAC;IACtG;EACJ;;EAEA;EACA6B,YAAYA,CAAC9B,eAAe,EAAEvB,WAAW,EAAE;IACvC,MAAMwB,YAAY,GAAG,GAAGD,eAAe,IAAIvB,WAAW,EAAE;IACxD,MAAMuC,aAAa,GAAG,IAAI,CAACrB,UAAU,CAACkB,GAAG,CAACZ,YAAY,CAAC;IACvD,MAAM8B,MAAM,GAAGf,aAAa,GAAG;MAC3BL,OAAO,EAAEK,aAAa,CAACL,OAAO;MAC9BC,WAAW,EAAEI,aAAa,CAACJ,WAAW;MACtCY,YAAY,EAAER,aAAa,CAACQ,YAAY,CAAC;IAC7C,CAAC,GAAG;MAAEb,OAAO,EAAE,IAAI;MAAEC,WAAW,EAAE,IAAI;MAAEY,YAAY,EAAE;IAAK,CAAC;IAE5D,OAAOO,MAAM;EACjB;;EAEA;EACAC,mBAAmBA,CAAChC,eAAe,EAAEvB,WAAW,EAAE;IAC9C,MAAMwB,YAAY,GAAG,GAAGD,eAAe,IAAIvB,WAAW,EAAE;IACxD,MAAMuC,aAAa,GAAG,IAAI,CAACrB,UAAU,CAACkB,GAAG,CAACZ,YAAY,CAAC;IACvD,OAAOe,aAAa,GAAGA,aAAa,CAACP,OAAO,GAAG,IAAI;EACvD;;EAEA;EACAwB,OAAOA,CAAA,EAAG;IACN,IAAI,CAACtC,UAAU,CAAC0B,OAAO,CAAEL,aAAa,IAAK;MACvC,IAAIA,aAAa,CAACP,OAAO,IAAIO,aAAa,CAACP,OAAO,CAACyB,UAAU,EAAE;QAC3DlB,aAAa,CAACP,OAAO,CAACyB,UAAU,CAACC,WAAW,CAACnB,aAAa,CAACP,OAAO,CAAC;MACvE;IACJ,CAAC,CAAC;IACF,IAAI,CAACd,UAAU,CAACyC,KAAK,CAAC,CAAC;IACvB,IAAI,CAACvC,gBAAgB,CAACuC,KAAK,CAAC,CAAC;EACjC;AACJ;;AAEA;AACA,MAAMC,qBAAqB,GAAG,IAAI5C,qBAAqB,CAAC,CAAC;;AAEzD;AACO,MAAM6C,oCAAoC,GAAGA,CAACtC,eAAe,EAAEvB,WAAW,KAAK;EAClF,MAAM8D,SAAS,GAAG/C,0DAAM,CAAC,IAAI,CAAC;EAC9B,MAAMgD,WAAW,GAAGhD,0DAAM,CAAC,KAAK,CAAC;;EAEjC;EACA,MAAMiD,qBAAqB,GAAG,cAAczC,eAAe,IAAIvB,WAAW,EAAE;EAE5Ec,6DAAS,CAAC,MAAM;IACZ;IACA,MAAMyB,aAAa,GAAGqB,qBAAqB,CAACtC,oBAAoB,CAACC,eAAe,EAAEvB,WAAW,CAAC;;IAE9F;IACA,IAAIuC,aAAa,CAACP,OAAO,EAAE;MACvBO,aAAa,CAACP,OAAO,CAACnB,EAAE,GAAGmD,qBAAqB;IACpD;;IAEA;IACA,IAAIF,SAAS,CAACG,OAAO,IAAI,CAACF,WAAW,CAACE,OAAO,EAAE;MAC3CL,qBAAqB,CAACvB,aAAa,CAACd,eAAe,EAAEvB,WAAW,EAAE8D,SAAS,CAACG,OAAO,CAAC;MACpFF,WAAW,CAACE,OAAO,GAAG,IAAI;IAC9B;;IAEA;IACA,OAAO,MAAM;MACT,IAAIF,WAAW,CAACE,OAAO,EAAE;QACrBL,qBAAqB,CAAClB,aAAa,CAACnB,eAAe,EAAEvB,WAAW,CAAC;QACjE+D,WAAW,CAACE,OAAO,GAAG,KAAK;MAC/B;IACJ,CAAC;EACL,CAAC,EAAE,CAAC1C,eAAe,EAAEvB,WAAW,CAAC,CAAC;EAElC,OAAO;IACH8D,SAAS;IACTI,gBAAgB,EAAEN,qBAAqB,CAACL,mBAAmB,CAAChC,eAAe,EAAEvB,WAAW,CAAC;IACzFmE,iBAAiB,EAAEH,qBAAqB;IAAE;IAC1Cf,sBAAsB,EAAEW,qBAAqB,CAACX,sBAAsB,CAAC1B,eAAe,EAAEvB,WAAW,CAAC;IAClGgD,uBAAuB,EAAEA,CAAA,KAAMY,qBAAqB,CAACZ,uBAAuB,CAACzB,eAAe,EAAEvB,WAAW,CAAC;IAC1G;IACAkD,YAAY,EAAEA,CAAChB,OAAO,EAAEC,WAAW,EAAEY,YAAY,GAAG,IAAI,KAAKa,qBAAqB,CAACV,YAAY,CAAC3B,eAAe,EAAEvB,WAAW,EAAEkC,OAAO,EAAEC,WAAW,EAAEY,YAAY,CAAC;IACjKM,YAAY,EAAEA,CAAA,KAAMO,qBAAqB,CAACP,YAAY,CAAC9B,eAAe,EAAEvB,WAAW,CAAC;IACpF8C,cAAc,EAAEA,CAAA,KAAM;MAClBc,qBAAqB,CAACd,cAAc,CAACvB,eAAe,EAAEvB,WAAW,CAAC;IACtE,CAAC;IACDqC,aAAa,EAAEA,CAAA,KAAM;MACjB,IAAIyB,SAAS,CAACG,OAAO,IAAI,CAACF,WAAW,CAACE,OAAO,EAAE;QAC3CL,qBAAqB,CAACvB,aAAa,CAACd,eAAe,EAAEvB,WAAW,EAAE8D,SAAS,CAACG,OAAO,CAAC;QACpFF,WAAW,CAACE,OAAO,GAAG,IAAI;MAC9B;IACJ,CAAC;IACDvB,aAAa,EAAEA,CAAA,KAAM;MACjB,IAAIqB,WAAW,CAACE,OAAO,EAAE;QACrBL,qBAAqB,CAAClB,aAAa,CAACnB,eAAe,EAAEvB,WAAW,CAAC;QACjE+D,WAAW,CAACE,OAAO,GAAG,KAAK;MAC/B;IACJ;EACJ,CAAC;AACL,CAAC;;AAED;AACA,IAAI,OAAOG,MAAM,KAAK,WAAW,EAAE;EAC/BA,MAAM,CAACC,gBAAgB,CAAC,cAAc,EAAE,MAAM;IAC1CT,qBAAqB,CAACJ,OAAO,CAAC,CAAC;EACnC,CAAC,CAAC;AACN;AAEA,iEAAeI,qBAAqB;;;;;;;;;;;;;;;;;ACxP0C;AACgB;AAEvF,MAAMY,+BAA+B,GAAGA,CAAC;EAC5CC,QAAQ;EACRC,OAAO;EACPC,YAAY;EACZzC,OAAO,EAAE0C,eAAe;EAAE;EAC1B5E,WAAW,GAAG,kCAAkC;EAChDuB,eAAe,GAAG,cAAc;EAChCsD,eAAe,GAAG,IAAI,CAAC;AAC3B,CAAC,KAAK;EACF,MAAM,CAAC3E,aAAa,EAAE4E,gBAAgB,CAAC,GAAGR,4DAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACnC,WAAW,EAAE4C,cAAc,CAAC,GAAGT,4DAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACpC,OAAO,EAAE8C,UAAU,CAAC,GAAGV,4DAAQ,CAACM,eAAe,IAAI,IAAI,CAAC,CAAC,CAAC;EACjE,MAAM,CAAC3E,SAAS,EAAEgF,YAAY,CAAC,GAAGX,4DAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACY,UAAU,EAAEC,aAAa,CAAC,GAAGb,4DAAQ,CAAC,IAAI,CAAC;;EAElD;EACA,MAAM;IACFR,SAAS;IACTI,gBAAgB;IAChBC,iBAAiB;IAAE;IACnBlB,sBAAsB;IACtBD,uBAAuB;IACvBE,YAAY;IAAE;IACdG,YAAY;IAAE;IACdhB,aAAa;IACbK;EACJ,CAAC,GAAGmB,2GAAoC,CAACtC,eAAe,EAAEvB,WAAW,CAAC;;EAEtE;EACA,MAAMoF,kBAAkB,GAAGrE,0DAAM,CAAC,KAAK,CAAC;EACxC,MAAMsE,uBAAuB,GAAGtE,0DAAM,CAAC,KAAK,CAAC;;EAE7C;EACA,MAAMuE,0BAA0B,GAAGrC,sBAAsB,IAAIiB,gBAAgB;;EAE7E;EACA,MAAMqB,mBAAmB,GAAGlC,YAAY,CAAC,CAAC;EAC1C,MAAMmC,iBAAiB,GAAGD,mBAAmB,EAAErD,OAAO;EACtD,MAAMuD,qBAAqB,GAAGF,mBAAmB,EAAEpD,WAAW;;EAE9D;EACA,MAAMuD,WAAW,GAAGnB,+DAAW,CAAC,MAAM;IAClCO,gBAAgB,CAAC,KAAK,CAAC;IACvBC,cAAc,CAAC,IAAI,CAAC;IACpBC,UAAU,CAAC,IAAI,CAAC;IAChBC,YAAY,CAAC,KAAK,CAAC;IACnBE,aAAa,CAAC,IAAI,CAAC;;IAEnB;IACAC,kBAAkB,CAACnB,OAAO,GAAG,KAAK;IAClCoB,uBAAuB,CAACpB,OAAO,GAAG,KAAK;;IAEvC;EACJ,CAAC,EAAE,EAAE,CAAC;EACN,MAAM0B,aAAa,GAAGpB,+DAAW,CAAC,MAAM;IACpC,OAAO,IAAIqB,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACpC;MACA,IACI1B,MAAM,CAAC2B,MAAM,IAAIpE,QAAQ,CAACqE,aAAa,CAAC,6DAA6D,CAAC,IACtGrE,QAAQ,CAACqE,aAAa,CAAC,+DAA+D,CAAC,EACzF;QACEH,OAAO,CAAC,CAAC;QACT;MACJ;MACA;MACA;MACA;;MAEA;MACA,MAAMI,IAAI,GAAGtE,QAAQ,CAACtB,aAAa,CAAC,MAAM,CAAC;MAC3C4F,IAAI,CAACC,GAAG,GAAG,YAAY;MACvBD,IAAI,CAACE,IAAI,GAAG,gDAAgD;MAC5DxE,QAAQ,CAACyE,IAAI,CAACtE,WAAW,CAACmE,IAAI,CAAC;MAE/B,MAAMI,MAAM,GAAG1E,QAAQ,CAACtB,aAAa,CAAC,QAAQ,CAAC;MAC/CgG,MAAM,CAACC,GAAG,GAAG,iDAAiD;MAC9DD,MAAM,CAACE,WAAW,GAAG,WAAW;MAEhCF,MAAM,CAACG,MAAM,GAAGX,OAAO;MACvBQ,MAAM,CAACI,OAAO,GAAGX,MAAM;MACvBnE,QAAQ,CAACyE,IAAI,CAACtE,WAAW,CAACuE,MAAM,CAAC;IACrC,CAAC,CAAC;EACN,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMK,sBAAsB,GAAGnC,+DAAW,CAAC,YAAY;IACnD;IACA,IAAIa,kBAAkB,CAACnB,OAAO,EAAE;MAC5B,OAAO,IAAI;IACf;;IAEA;IACA,IAAI9B,WAAW,IAAIsD,qBAAqB,EAAE;MACtC,MAAMkB,aAAa,GAAGxE,WAAW,IAAIsD,qBAAqB;MAC1D,MAAMmB,YAAY,GAAGhC,eAAe,IAAI1C,OAAO,IAAIsD,iBAAiB;;MAEpE;MACA,IAAI,CAACrD,WAAW,IAAIsD,qBAAqB,EAAE;QACvCV,cAAc,CAACU,qBAAqB,CAAC;MACzC;MACA,IAAI,CAACvD,OAAO,IAAI0E,YAAY,EAAE;QAC1B5B,UAAU,CAAC4B,YAAY,CAAC;MAC5B;MAEA,OAAO;QAAEC,KAAK,EAAEF,aAAa;QAAEzE,OAAO,EAAE0E,YAAY;QAAEE,YAAY,EAAE;MAAK,CAAC;IAC9E;;IAEA;IACA,MAAMF,YAAY,GAAGhC,eAAe,IAAI1C,OAAO,IAAIsD,iBAAiB;IAEpE,IAAI,CAACoB,YAAY,EAAE;MACf,MAAM,IAAIG,KAAK,CAAC,4CAA4C,CAAC;IACjE;IAEA,IAAI;MACA3B,kBAAkB,CAACnB,OAAO,GAAG,IAAI;;MAEjC;MACA,MAAM+C,aAAa,GAAIC,OAAO,KAAM;QAChCC,UAAU,EAAED,OAAO,EAAEC,UAAU,IAAI,EAAE;QACrCC,SAAS,EAAEF,OAAO,EAAEE,SAAS,IAAI,EAAE;QACnCC,OAAO,EAAEH,OAAO,EAAEG,OAAO,IAAI,EAAE;QAC/BC,SAAS,EAAEJ,OAAO,EAAEI,SAAS,IAAI,EAAE;QACnCC,SAAS,EAAEL,OAAO,EAAEK,SAAS,IAAI,EAAE;QACnCC,IAAI,EAAEN,OAAO,EAAEM,IAAI,IAAI,EAAE;QACzBC,KAAK,EAAEP,OAAO,EAAEO,KAAK,IAAI,EAAE;QAC3BC,QAAQ,EAAER,OAAO,EAAEQ,QAAQ,IAAI,EAAE;QACjCC,OAAO,EAAET,OAAO,EAAES,OAAO,IAAI,EAAE;QAC/BC,KAAK,EAAEV,OAAO,EAAEU,KAAK,IAAI,EAAE;QAC3BC,KAAK,EAAEX,OAAO,EAAEW,KAAK,IAAI;MAC7B,CAAC,CAAC;;MAEF;MACA,MAAMC,cAAc,GAAGnD,OAAO,EAAEmD,cAAc,IAAI,CAAC,CAAC;;MAEpD;MACA,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAACtD,QAAQ,CAACuD,QAAQ,EAAE;QAC5CC,MAAM,EAAE,MAAM;QACdpG,IAAI,EAAE,IAAIqG,eAAe,CAAC;UACtBC,MAAM,EAAE,0BAA0B;UAAE;UACpCC,QAAQ,EAAE3D,QAAQ,CAAC4D,oBAAoB;UACvCC,QAAQ,EAAE1B,YAAY;UAAE;UACxB2B,cAAc,EAAE,CAAC,CAAC5G,QAAQ,CAACE,IAAI,CAAC2G,SAAS,CAACC,QAAQ,CAAC,sBAAsB,CAAC;UAC1EZ,cAAc,EAAEa,IAAI,CAACC,SAAS,CAAC3B,aAAa,CAACa,cAAc,CAAC,CAAC;UAAE;UAC/De,SAAS,EAAE,MAAM,CAAC;QACtB,CAAC;MACL,CAAC,CAAC;MAEF,MAAMtF,MAAM,GAAG,MAAMwE,QAAQ,CAACe,IAAI,CAAC,CAAC;MAEpC,IAAI,CAACvF,MAAM,CAACwF,OAAO,EAAE;QACjB,MAAM,IAAI/B,KAAK,CAACzD,MAAM,CAACyF,IAAI,EAAEC,OAAO,IAAI,iCAAiC,CAAC;MAC9E;MAEA,MAAM;QAAE7G,WAAW,EAAE0E;MAAM,CAAC,GAAGvD,MAAM,CAACyF,IAAI;MAC1ChE,cAAc,CAAC8B,KAAK,CAAC;MACrB7B,UAAU,CAAC4B,YAAY,CAAC;;MAExB;MACA1D,YAAY,CAAC0D,YAAY,EAAEC,KAAK,CAAC;MAEjC,OAAO;QAAEA,KAAK;QAAE3E,OAAO,EAAE0E,YAAY;QAAEE,YAAY,EAAExD,MAAM,CAACyF;MAAK,CAAC;IAEtE,CAAC,CAAC,OAAOE,KAAK,EAAE;MACZ9F,OAAO,CAAC8F,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,MAAMA,KAAK;IACf,CAAC,SAAS;MACN7D,kBAAkB,CAACnB,OAAO,GAAG,KAAK;IACtC;EACJ,CAAC,EAAE,CAACQ,QAAQ,EAAEC,OAAO,EAAEC,YAAY,EAAExC,WAAW,EAAED,OAAO,EAAE0C,eAAe,EAAEa,qBAAqB,EAAED,iBAAiB,CAAC,CAAC;;EAEtH;EACA,MAAM0D,qBAAqB,GAAG3E,+DAAW,CAAC,OAAOwE,IAAI,EAAEjC,YAAY,KAAK;IACpE,IAAI;MACA,MAAMqC,eAAe,GAAGJ,IAAI,EAAEK,OAAO,EAAEvI,EAAE;MACzC,IAAI,CAACsI,eAAe,EAAE;QAClB,MAAM,IAAIpC,KAAK,CAAC,qCAAqC,CAAC;MAC1D;;MAEA;MACA,MAAMsC,gBAAgB,GAAGhG,YAAY,CAAC,CAAC;MACvC,MAAMiG,cAAc,GAAGpH,OAAO,IAAImH,gBAAgB,EAAEnH,OAAO;MAC3D,MAAMqH,kBAAkB,GAAGpH,WAAW,IAAIkH,gBAAgB,EAAElH,WAAW;MAEvE,IAAI,CAACmH,cAAc,EAAE;QACjB,MAAM,IAAIvC,KAAK,CAAC,+CAA+C,CAAC;MACpE;;MAEA;MACA,MAAMe,QAAQ,GAAG,MAAMC,KAAK,CAACtD,QAAQ,CAACuD,QAAQ,EAAE;QAC5CC,MAAM,EAAE,MAAM;QACdpG,IAAI,EAAE,IAAIqG,eAAe,CAAC;UACtBC,MAAM,EAAE1D,QAAQ,CAAC+E,4BAA4B;UAC7CC,KAAK,EAAEhF,QAAQ,CAACiF,sBAAsB;UACtCxH,OAAO,EAAEoH,cAAc;UACvBH,eAAe,EAAEA,eAAe;UAChCQ,SAAS,EAAE7C,YAAY,EAAE8C,gCAAgC,IAAI,EAAE;UAC/DrB,cAAc,EAAE,CAAC,CAAC5G,QAAQ,CAACE,IAAI,CAAC2G,SAAS,CAACC,QAAQ,CAAC,sBAAsB;QAC7E,CAAC;MACL,CAAC,CAAC;MAEF,MAAMnF,MAAM,GAAG,MAAMwE,QAAQ,CAACe,IAAI,CAAC,CAAC;MAEpC,IAAIvF,MAAM,CAACwF,OAAO,EAAE;QAChB;QACA1E,MAAM,CAACyF,QAAQ,CAAC1D,IAAI,GAAG7C,MAAM,CAACyF,IAAI,CAACe,YAAY;MACnD,CAAC,MAAM;QACH,MAAM,IAAI/C,KAAK,CAACzD,MAAM,CAACyF,IAAI,EAAEC,OAAO,IAAI,2BAA2B,CAAC;MACxE;IAEJ,CAAC,CAAC,OAAOC,KAAK,EAAE;MACZ9F,OAAO,CAAC8F,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDc,KAAK,CAACd,KAAK,CAACD,OAAO,IAAIvE,QAAQ,CAACuF,IAAI,EAAEC,aAAa,IAAI,2BAA2B,CAAC;MACnFvE,WAAW,CAAC,CAAC;IACjB;EACJ,CAAC,EAAE,CAACjB,QAAQ,EAAEvC,OAAO,EAAEC,WAAW,EAAEkB,YAAY,EAAEqC,WAAW,CAAC,CAAC;;EAE/D;EACA,MAAMwE,wBAAwB,GAAG3F,+DAAW,CAAC,OAAOsC,KAAK,EAAEC,YAAY,KAAK;IACxE;IACA,IAAIzB,uBAAuB,CAACpB,OAAO,EAAE;MACjC;IACJ;;IAEA;IACA,IAAIhB,sBAAsB,IAAI/C,aAAa,EAAE;MACzC;IACJ;IAEA,IAAI;MACAmF,uBAAuB,CAACpB,OAAO,GAAG,IAAI;;MAEtC;MACA,MAAM0B,aAAa,CAAC,CAAC;MAErB,MAAM;QAAEI;MAAO,CAAC,GAAG3B,MAAM;MAEzB,IAAI,CAAC2B,MAAM,EAAE;QACT,MAAM,IAAIgB,KAAK,CAAC,uBAAuB,CAAC;MAC5C;MAEA,MAAMoD,aAAa,GAAG;QAClBzI,SAAS,EAAE,IAAIyC,iBAAiB,IAAInE,WAAW,EAAE;QAAE;QACnDoK,2BAA2B,EAAE,IAAI;QACjC7J,KAAK,EAAE;UACH8J,UAAU,EAAE;YACRC,UAAU,EAAE7F,QAAQ,CAAC8F,kBAAkB,EAAEC,WAAW,EAAEC,WAAW,IAAI,8BAA8B;YACnGC,QAAQ,EAAEjG,QAAQ,CAAC8F,kBAAkB,EAAEC,WAAW,EAAEG,SAAS,IAAI,MAAM;YACvEC,UAAU,EAAEnG,QAAQ,CAAC8F,kBAAkB,EAAEC,WAAW,EAAEK,WAAW,IAAI,QAAQ;YAC7ElK,KAAK,EAAE8D,QAAQ,CAAC8F,kBAAkB,EAAEC,WAAW,EAAE7J,KAAK,IAAI;UAC9D,CAAC;UACDmK,KAAK,EAAE;YACHC,IAAI,EAAE;cACFT,UAAU,EAAE7F,QAAQ,CAAC8F,kBAAkB,EAAEO,KAAK,EAAEL,WAAW,IAAI,8BAA8B;cAC7FC,QAAQ,EAAEjG,QAAQ,CAAC8F,kBAAkB,EAAEO,KAAK,EAAEH,SAAS,IAAI,MAAM;cACjEC,UAAU,EAAEnG,QAAQ,CAAC8F,kBAAkB,EAAEO,KAAK,EAAED,WAAW,IAAI,QAAQ;cACvEG,UAAU,EAAEvG,QAAQ,CAAC8F,kBAAkB,EAAEO,KAAK,EAAEG,gBAAgB,IAAI,SAAS;cAC7EC,WAAW,EAAEzG,QAAQ,CAAC8F,kBAAkB,EAAEO,KAAK,EAAEK,YAAY,IAAI,SAAS;cAC1EC,YAAY,EAAE3G,QAAQ,CAAC8F,kBAAkB,EAAEO,KAAK,EAAEO,aAAa,IAAI,KAAK;cACxE1K,KAAK,EAAE8D,QAAQ,CAAC8F,kBAAkB,EAAEO,KAAK,EAAEQ,UAAU,IAAI;YAC7D,CAAC;YACDC,KAAK,EAAE;cACHL,WAAW,EAAE,SAAS;cACtBM,SAAS,EAAE;YACf,CAAC;YACDvC,KAAK,EAAE;cACHiC,WAAW,EAAE,SAAS;cACtBvK,KAAK,EAAE;YACX;UACJ,CAAC;UACD8K,YAAY,EAAE;YACVV,IAAI,EAAE;cACFpK,KAAK,EAAE8D,QAAQ,CAAC8F,kBAAkB,EAAEmB,aAAa,EAAEJ,UAAU,IAAI,SAAS;cAC1EN,UAAU,EAAEvG,QAAQ,CAAC8F,kBAAkB,EAAEmB,aAAa,EAAEV,UAAU,IAAI,SAAS;cAC/EI,YAAY,EAAE3G,QAAQ,CAAC8F,kBAAkB,EAAEmB,aAAa,EAAEL,aAAa,IAAI,MAAM;cACjFH,WAAW,EAAEzG,QAAQ,CAAC8F,kBAAkB,EAAEmB,aAAa,EAAEP,YAAY,IAAI,SAAS;cAClFb,UAAU,EAAE7F,QAAQ,CAAC8F,kBAAkB,EAAEmB,aAAa,EAAEjB,WAAW,IAAI,8BAA8B;cACrGC,QAAQ,EAAEjG,QAAQ,CAAC8F,kBAAkB,EAAEmB,aAAa,EAAEf,SAAS,IAAI,MAAM;cACzEC,UAAU,EAAEnG,QAAQ,CAAC8F,kBAAkB,EAAEmB,aAAa,EAAEb,WAAW,IAAI,MAAM;cAC7EW,SAAS,EAAE;YACf,CAAC;YACDG,QAAQ,EAAE;cACNhL,KAAK,EAAE,SAAS;cAChBqK,UAAU,EAAE;YAChB;UACJ,CAAC;UACDY,aAAa,EAAE;YACXjL,KAAK,EAAE8D,QAAQ,CAAC8F,kBAAkB,EAAEmB,aAAa,EAAEV,UAAU,IAAI;UACrE;QACJ,CAAC;QACDa,YAAY,EAAE;UACVF,QAAQ,EAAE;QACd,CAAC;QACDG,aAAa,EAAE,KAAK;QAAE;QACtBC,kBAAkB,EAAGhD,IAAI,IAAK;UAC1B5F,OAAO,CAAC6I,GAAG,CAAC,2BAA2B,EAAEjD,IAAI,CAAC;UAC9CG,qBAAqB,CAACH,IAAI,EAAEjC,YAAY,CAAC;QAC7C,CAAC;QACDmF,cAAc,EAAEA,CAAChD,KAAK,EAAE;UAAEG;QAAQ,CAAC,KAAK;UACpCjG,OAAO,CAAC8F,KAAK,CAAC,yBAAyB,EAAEA,KAAK,EAAEG,OAAO,CAAC;UAExD,IAAIyC,YAAY,GAAGpH,QAAQ,CAACuF,IAAI,EAAEC,aAAa,IAAI,gBAAgB;UACnE,IAAIhB,KAAK,IAAIA,KAAK,CAACD,OAAO,EAAE;YACxB6C,YAAY,GAAG5C,KAAK,CAACD,OAAO;UAChC,CAAC,MAAM,IAAII,OAAO,IAAIA,OAAO,CAAC8C,SAAS,IAAI9C,OAAO,CAAC8C,SAAS,CAAClD,OAAO,EAAE;YAClE6C,YAAY,GAAGzC,OAAO,CAAC8C,SAAS,CAAClD,OAAO;UAC5C;UAEAe,KAAK,CAAC8B,YAAY,CAAC;UACnBnG,WAAW,CAAC,CAAC;QACjB,CAAC;QACDyG,gBAAgB,EAAEA,CAAA,KAAM;UACpBhJ,OAAO,CAAC6I,GAAG,CAAC,2BAA2B,CAAC;UACxCtG,WAAW,CAAC,CAAC;QACjB;MACJ,CAAC;;MAED;MACA,MAAMK,MAAM,CAACqG,qBAAqB,CAACvF,KAAK,EAAEsD,aAAa,CAAC;;MAExD;MACArF,gBAAgB,CAAC,IAAI,CAAC;MACtB9B,uBAAuB,CAAC,CAAC,CAAC,CAAC;MAC3BiC,YAAY,CAAC,KAAK,CAAC;IACvB,CAAC,CAAC,OAAOgE,KAAK,EAAE;MACZ9F,OAAO,CAAC8F,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3DhE,YAAY,CAAC,KAAK,CAAC;MACnB8E,KAAK,CAACd,KAAK,CAACD,OAAO,IAAIvE,QAAQ,CAACuF,IAAI,EAAEC,aAAa,IAAI,mCAAmC,CAAC;IAC/F,CAAC,SAAS;MACN5E,uBAAuB,CAACpB,OAAO,GAAG,KAAK;IAC3C;EACJ,CAAC,EAAE,CAACQ,QAAQ,EAAEzE,WAAW,EAAE2F,aAAa,EAAEuD,qBAAqB,EAAExD,WAAW,EAAExF,aAAa,EAAEiE,iBAAiB,EAAEnB,uBAAuB,CAAC,CAAC;;EAGzI;EACAlC,6DAAS,CAAC,MAAM;IACZ,MAAMuL,iBAAiB,GAAG,MAAAA,CAAA,KAAY;MAClC;MACA,IAAI/G,0BAA0B,EAAE;QAC5BR,gBAAgB,CAAC,IAAI,CAAC;QACtBG,YAAY,CAAC,KAAK,CAAC;;QAEnB;QACA,IAAIQ,qBAAqB,IAAI,CAACtD,WAAW,EAAE;UACvC4C,cAAc,CAACU,qBAAqB,CAAC;QACzC;QACA,IAAID,iBAAiB,IAAI,CAACtD,OAAO,EAAE;UAC/B8C,UAAU,CAACQ,iBAAiB,CAAC;QACjC;QAEAnD,aAAa,CAAC,CAAC;QACf;MACJ;;MAEA;MACA,IAAInC,aAAa,IAAID,SAAS,IAAImF,kBAAkB,CAACnB,OAAO,IAAIoB,uBAAuB,CAACpB,OAAO,EAAE;QAC7F;MACJ;;MAEA;MACA,IAAI,CAACW,eAAe,EAAE;QAClB;MACJ;;MAEA;MACA,IAAI,CAACC,eAAe,EAAE;QAClB;MACJ;;MAEA;MACA,MAAMyH,iBAAiB,GAAG5H,OAAO,EAAEmD,cAAc,GAC7C,GAAGnD,OAAO,CAACmD,cAAc,CAACF,KAAK,IAAIjD,OAAO,CAACmD,cAAc,CAACX,UAAU,IAAIxC,OAAO,CAACmD,cAAc,CAACV,SAAS,EAAE,GAAG,IAAI;;MAErH;MACA,MAAMoF,cAAc,GAAG7H,OAAO,EAAEmD,cAAc,IAAIlG,QAAQ,CAACE,IAAI,CAAC2G,SAAS,CAACC,QAAQ,CAAC,sBAAsB,CAAC;MAC1G,MAAM+D,kBAAkB,GAAGF,iBAAiB,KAAKpH,UAAU;MAE3D,IAAI,CAACqH,cAAc,IAAI,CAACC,kBAAkB,EAAE;QACxC;MACJ;MAEA,IAAI;QACAvH,YAAY,CAAC,IAAI,CAAC;QAClBE,aAAa,CAACmH,iBAAiB,CAAC,CAAC,CAAC;;QAElC,MAAMhJ,MAAM,GAAG,MAAMoD,sBAAsB,CAAC,CAAC;QAE7C,IAAIpD,MAAM,IAAIA,MAAM,CAACuD,KAAK,EAAE;UACxB,MAAMqD,wBAAwB,CAAC5G,MAAM,CAACuD,KAAK,EAAEvD,MAAM,CAACwD,YAAY,CAAC;QACrE,CAAC,MAAM;UACH3D,OAAO,CAACC,IAAI,CAAC,uCAAuC,CAAC;UACrD6B,YAAY,CAAC,KAAK,CAAC;QACvB;MAEJ,CAAC,CAAC,OAAOgE,KAAK,EAAE;QACZ9F,OAAO,CAAC8F,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;QAC5DhE,YAAY,CAAC,KAAK,CAAC;QACnB8E,KAAK,CAACd,KAAK,CAACD,OAAO,IAAI,oCAAoC,CAAC;MAChE;IACJ,CAAC;;IAED;IACA,MAAMyD,SAAS,GAAGC,UAAU,CAACL,iBAAiB,EAAE,GAAG,CAAC;IAEpD,OAAO,MAAMM,YAAY,CAACF,SAAS,CAAC;EACxC,CAAC,EAAE,CACC7H,eAAe;EAAE;EACjBC,eAAe;EAAE;EACjBS,0BAA0B,EAC1BjD,aAAa,EACbmD,iBAAiB,EACjBC,qBAAqB,CACxB,CAAC,CAAC,CAAC;;EAEJ,OAAO;IACHvF,aAAa,EAAEA,aAAa,IAAIoF,0BAA0B;IAC1DrF,SAAS;IACTkC,WAAW,EAAEA,WAAW,IAAIsD,qBAAqB;IAAE;IACnDvD,OAAO,EAAE0C,eAAe,IAAI1C,OAAO,IAAIsD,iBAAiB;IAAE;IAC1DE,WAAW;IACXvF,YAAY,EAAE2D,SAAS,CAAC;EAC5B,CAAC;AACL,CAAC;;;;;;;;;;ACxaD;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;UCAA;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;;WCtBA;WACA;WACA;WACA;WACA;WACA,iCAAiC,WAAW;WAC5C;WACA;;;;;WCPA;WACA;WACA;WACA;WACA,yCAAyC,wCAAwC;WACjF;WACA;WACA;;;;;WCPA;;;;;WCAA;WACA;WACA;WACA,uDAAuD,iBAAiB;WACxE;WACA,gDAAgD,aAAa;WAC7D;;;;;;;;;;;;;;;;;;;;;;;;;;;ACNqE;AACX;AACP;AACd;AACU;AACH;AACiB;AACS;AACqB;AAC3F;AACA;AACA;AACA;AACA;;AAEA;AACA,IAAI,OAAO8I,+EAAqB,KAAK,WAAW,EAAE;EAC9CzJ,OAAO,CAACC,IAAI,CAAC,kHAAkH,CAAC;AACpI,CAAC,MAAM;EACH;;EAEA;EACA,IAAIqB,QAAQ,GAAG,CAAC,CAAC;EACjB,IAAI;IACA;IACA,IAAI,OAAOqI,6DAAU,KAAK,WAAW,EAAE;MACnC;MACArI,QAAQ,GAAGqI,iEAAU,CAAC,mBAAmB,EAAE,CAAC,CAAC,CAAC;IAClD,CAAC,MAAM;MACH,MAAM,IAAI/F,KAAK,CAAC,mCAAmC,CAAC;IACxD;EACJ,CAAC,CAAC,OAAOkC,KAAK,EAAE;IACZ9F,OAAO,CAAC8F,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;IAClE;IACAxE,QAAQ,GAAGL,MAAM,CAAC8I,0BAA0B,IAAI,CAAC,CAAC;EACtD;EAEA,IAAI,CAACzI,QAAQ,IAAI,OAAOA,QAAQ,KAAK,QAAQ,IAAI0I,MAAM,CAACC,IAAI,CAAC3I,QAAQ,CAAC,CAAC4I,MAAM,KAAK,CAAC,EAAE;IACjFlK,OAAO,CAACC,IAAI,CAAC,0DAA0D,CAAC;IACxEqB,QAAQ,GAAG;MACP6I,KAAK,EAAE,qBAAqB;MAC5BC,WAAW,EAAE,gEAAgE;MAC7EC,QAAQ,EAAE;IACd,CAAC;EACL;EAEA,MAAMC,YAAY,GAAGV,mDAAE,CAAC,qBAAqB,EAAE,kCAAkC,CAAC;EAClF,MAAMW,KAAK,GAAGb,wEAAc,CAACpI,QAAQ,CAAC6I,KAAK,CAAC,IAAIG,YAAY;;EAE5D;AACJ;AACA;EACI,MAAME,OAAO,GAAGA,CAAC;IACbjJ,OAAO;IACPC,YAAY;IACZiJ,cAAc;IACdC,aAAa;IACbC,iBAAiB;IACjBC;EACJ,CAAC,KAAK;IACF,MAAMR,WAAW,GAAGV,wEAAc,CAACpI,QAAQ,CAAC8I,WAAW,IAAI,EAAE,CAAC;;IAE9D;IACA,MAAM;MAAErL;IAAQ,CAAC,GAAG8K,0DAAS,CAAEgB,MAAM,IAAK;MACtC,MAAMC,KAAK,GAAGD,MAAM,CAACf,uEAAkB,CAAC;MACxC,OAAO;QACH/K,OAAO,EAAE+L,KAAK,CAACC,UAAU,CAAC;MAC9B,CAAC;IACL,CAAC,CAAC;;IAEF;IACA,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;MAC/B,MAAMtG,cAAc,GAAGnD,OAAO,EAAEmD,cAAc;MAC9C,IAAI,CAACA,cAAc,EAAE,OAAO,KAAK;;MAEjC;MACA,OAAO,CAAC,EACJA,cAAc,CAACF,KAAK,IACpBE,cAAc,CAACX,UAAU,IACzBW,cAAc,CAACV,SAAS,IACxBU,cAAc,CAACR,SAAS,IACxBQ,cAAc,CAACN,IAAI,IACnBM,cAAc,CAACJ,QAAQ,IACvBI,cAAc,CAACH,OAAO,IACtBG,cAAc,CAACL,KAAK,IACpBK,cAAc,CAACN,IAAI,CACtB;IACL,CAAC;;IAED;IACA,MAAM;MACFrH,aAAa;MACbD,SAAS;MACTkC,WAAW;MACXD,OAAO,EAAEkM,WAAW;MACpB1I,WAAW;MACXvF;IACJ,CAAC,GAAGqE,uGAA+B,CAAC;MAChCC,QAAQ;MACRC,OAAO;MACPC,YAAY;MACZzC,OAAO;MAAE;MACTlC,WAAW,EAAE,kCAAkC;MAC/CuB,eAAe,EAAE,cAAc;MAC/BsD,eAAe,EAAEsJ,oBAAoB,CAAC,CAAC,CAAC;IAC5C,CAAC,CAAC;IAEFrN,6DAAS,CAAC,MAAM;MACZ;MACA,MAAMuN,gBAAgB,GAAG1M,QAAQ,CAACqE,aAAa,CAAC,+FAA+F,CAAC;MAEhJ,IAAIqI,gBAAgB,EAAE;QAClB;QACAA,gBAAgB,CAAC9N,KAAK,CAAC+N,OAAO,GAAG,MAAM;MAC3C;;MAEA;MACA,OAAO,MAAM;QACT,IAAID,gBAAgB,EAAE;UAClB;UACAA,gBAAgB,CAAC9N,KAAK,CAAC+N,OAAO,GAAG,EAAE;QACvC;MACJ,CAAC;IACL,CAAC,EAAE,EAAE,CAAC;IAEN,oBACIlO,KAAA,CAAAC,aAAA,2BACID,KAAA,CAAAC,aAAA;MACIC,SAAS,EAAC,sBAAsB;MAChCiO,uBAAuB,EAAE;QAAEC,MAAM,EAAEjB;MAAY;IAAE,CACpD,CAAC,EACD,CAACY,oBAAoB,CAAC,CAAC,IAAI,CAACjO,aAAa,IAAI,CAACD,SAAS,iBACpDG,KAAA,CAAAC,aAAA;MAAKC,SAAS,EAAC;IAAmB,gBAC9BF,KAAA,CAAAC,aAAA,YAAG,0EAA2E,CAAC,eAC/ED,KAAA,CAAAC,aAAA,gBAAO,6DAAkE,CACxE,CACR,eACDD,KAAA,CAAAC,aAAA,CAACN,+EAAuB;MACpBC,WAAW,EAAC,kCAAkC;MAC9CE,aAAa,EAAEA,aAAc;MAC7BD,SAAS,EAAEA,SAAU;MACrBE,YAAY,EAAEA;IAAa,CAC9B,CACA,CAAC;EAEd,CAAC;;EAED;AACJ;AACA;AACA;AACA;EACI,MAAMsO,KAAK,GAAIC,KAAK,IAAK;IAErB,MAAM;MAAEC;IAAmB,CAAC,GAAGD,KAAK,CAACE,UAAU,IAAI,CAAC,CAAC;IACrD,oBACIxO,KAAA,CAAAC,aAAA;MAAKE,KAAK,EAAE;QACR+N,OAAO,EAAE,MAAM;QACfO,cAAc,EAAE,eAAe;QAC/BC,UAAU,EAAE,QAAQ;QACpBC,KAAK,EAAE;MACX;IAAE,gBACE3O,KAAA,CAAAC,aAAA;MAAME,KAAK,EAAE;QAAEqK,UAAU,EAAE;MAAI;IAAE,GAAE8C,KAAY,CAAC,eAChDtN,KAAA,CAAAC,aAAA;MACIiG,GAAG,EAAE,GAAG7B,QAAQ,CAACuK,UAAU,IAAI,uDAAuD,6CAA8C;MACpIC,GAAG,EAAC,sBAAsB;MAC1B1O,KAAK,EAAE;QACH2O,MAAM,EAAE,MAAM;QACdC,SAAS,EAAE,MAAM;QACjBJ,KAAK,EAAE;MACX;IAAE,CACL,CACA,CAAC;EAEd,CAAC;;EAED;AACJ;AACA;EACI,MAAMK,IAAI,GAAGA,CAAA,KAAM;IACf,MAAM7B,WAAW,GAAGV,wEAAc,CAACpI,QAAQ,CAAC8I,WAAW,IAAI,EAAE,CAAC;IAC9D,oBACInN,KAAA,CAAAC,aAAA,2BACID,KAAA,CAAAC,aAAA;MACIC,SAAS,EAAC,sBAAsB;MAChCiO,uBAAuB,EAAE;QAAEC,MAAM,EAAEjB;MAAY;IAAE,CACpD,CAAC,eACFnN,KAAA,CAAAC,aAAA;MAAKC,SAAS,EAAC;IAAoB,gBAC/BF,KAAA,CAAAC,aAAA;MAAKQ,EAAE,EAAC;IAAkC,CAErC,CACJ,CACJ,CAAC;EAEd,CAAC;;EAED;AACJ;AACA;EACI,MAAMwO,WAAW,GAAG;IAChBC,IAAI,EAAE,cAAc;IAAE;IACtB5B,KAAK,eAAEtN,KAAA,CAAAC,aAAA,CAACoO,KAAK,MAAE,CAAC;IAChBc,OAAO,eAAEnP,KAAA,CAAAC,aAAA,CAACsN,OAAO,MAAE,CAAC;IACpB6B,IAAI,eAAEpP,KAAA,CAAAC,aAAA,CAAC+O,IAAI,MAAE,CAAC;IACdK,cAAc,EAAEA,CAAA,KAAM,IAAI;IAAE;IAC5BC,SAAS,EAAEhC,KAAK;IAChBF,QAAQ,EAAE;MACNmC,QAAQ,EAAElL,QAAQ,EAAE+I,QAAQ,IAAI;MAChC;IACJ,CAAC;IACD;IACAoC,KAAK,EAAEnL,QAAQ,EAAEmL,KAAK,IAAI;EAC9B,CAAC;;EAED;EACA,IAAI;IACAhD,mFAAqB,CAACyC,WAAW,CAAC;EACtC,CAAC,CAAC,OAAOpG,KAAK,EAAE;IACZ9F,OAAO,CAAC8F,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;EAC3E;AACJ,C", "sources": ["webpack://monoova-payments-for-woocommerce/./assets/js/src/blocks/primer-checkout-container.js", "webpack://monoova-payments-for-woocommerce/./assets/js/src/hooks/usePersistentPaymentDetailsContainer.js", "webpack://monoova-payments-for-woocommerce/./assets/js/src/hooks/usePrimerCheckoutInitialization.js", "webpack://monoova-payments-for-woocommerce/external var [\"wc\",\"wcBlocksData\"]", "webpack://monoova-payments-for-woocommerce/external var [\"wc\",\"wcBlocksRegistry\"]", "webpack://monoova-payments-for-woocommerce/external var [\"wc\",\"wcSettings\"]", "webpack://monoova-payments-for-woocommerce/external window [\"wp\",\"data\"]", "webpack://monoova-payments-for-woocommerce/external window [\"wp\",\"element\"]", "webpack://monoova-payments-for-woocommerce/external window [\"wp\",\"htmlEntities\"]", "webpack://monoova-payments-for-woocommerce/external window [\"wp\",\"i18n\"]", "webpack://monoova-payments-for-woocommerce/webpack/bootstrap", "webpack://monoova-payments-for-woocommerce/webpack/runtime/compat get default export", "webpack://monoova-payments-for-woocommerce/webpack/runtime/define property getters", "webpack://monoova-payments-for-woocommerce/webpack/runtime/hasOwnProperty shorthand", "webpack://monoova-payments-for-woocommerce/webpack/runtime/make namespace object", "webpack://monoova-payments-for-woocommerce/./assets/js/src/blocks/monoova-card-block.js"], "sourcesContent": ["export const PrimerCheckoutContainer = ({\n    containerId,\n    isLoading = false,\n    isInitialized = false,\n    containerRef = null\n}) => {\n    return (\n        <div className=\"embedded-card-form\" style={{ marginTop: '20px' }}>\n            {isLoading && !isInitialized && (\n                <div className=\"primer-loading-indicator\" style={{\n                    padding: '20px',\n                    textAlign: 'center',\n                    color: '#666'\n                }}>\n                    Loading payment form...\n                </div>\n            )}\n            {/* Target container for persistent Primer checkout */}\n            <div ref={containerRef} className=\"primer-container-target\">\n                <div id={containerId}></div>\n            </div>\n        </div>\n    );\n};\n", "import { useEffect, useRef } from '@wordpress/element';\n\n// Global container management for persistent Primer checkout\nclass PrimerCheckoutManager {\n    constructor() {\n        this.containers = new Map();\n        this.activeContainers = new Set();\n        this.isInitialized = false;\n    }\n\n    // Create or get existing container\n    getOrCreateContainer(paymentMethodId, containerId) {\n        const containerKey = `${paymentMethodId}_${containerId}`;\n        \n        if (!this.containers.has(containerKey)) {\n            // Create container element\n            const container = document.createElement('div');\n            container.id = containerId;\n            container.className = 'primer-checkout-persistent-container';\n            container.style.cssText = `\n                position: absolute;\n                top: -9999px;\n                left: -9999px;\n                width: 100%;\n                visibility: hidden;\n                opacity: 0;\n                pointer-events: none;\n                transition: all 0.3s ease;\n            `;\n            \n            // Append to body to keep it persistent\n            document.body.appendChild(container);\n            \n            this.containers.set(containerKey, {\n                element: container,\n                isInitialized: false,\n                isVisible: false,\n                orderId: null, // Store orderId for payment completion\n                clientToken: null // Store clientToken as well\n            });\n        }\n        \n        return this.containers.get(containerKey);\n    }\n\n    // Show container in target element\n    showContainer(paymentMethodId, containerId, targetElement) {\n        const containerKey = `${paymentMethodId}_${containerId}`;\n        const containerInfo = this.containers.get(containerKey);\n        \n        if (containerInfo && containerInfo.element) {\n            // Hide all other containers first\n            this.hideAllContainers();\n            \n            // Move container to target and show it\n            if (targetElement) {\n                targetElement.appendChild(containerInfo.element);\n                containerInfo.element.style.cssText = `\n                    position: relative;\n                    top: auto;\n                    left: auto;\n                    width: 100%;\n                    visibility: visible;\n                    opacity: 1;\n                    pointer-events: auto;\n                    transition: all 0.3s ease;\n                `;\n                containerInfo.isVisible = true;\n                this.activeContainers.add(containerKey);\n            }\n        }\n    }\n\n    // Hide container\n    hideContainer(paymentMethodId, containerId) {\n        const containerKey = `${paymentMethodId}_${containerId}`;\n        const containerInfo = this.containers.get(containerKey);\n        \n        if (containerInfo && containerInfo.element) {\n            // Move back to body and hide\n            document.body.appendChild(containerInfo.element);\n            containerInfo.element.style.cssText = `\n                position: absolute;\n                top: -9999px;\n                left: -9999px;\n                width: 100%;\n                visibility: hidden;\n                opacity: 0;\n                pointer-events: none;\n                transition: all 0.3s ease;\n            `;\n            containerInfo.isVisible = false;\n            this.activeContainers.delete(containerKey);\n        }\n    }\n\n    // Hide all containers\n    hideAllContainers() {\n        this.containers.forEach((containerInfo, containerKey) => {\n            if (containerInfo.isVisible) {\n                this.hideContainer(containerKey.split('_')[0], containerKey.split('_')[1]);\n            }\n        });\n    }\n\n    // Clear order data for a specific container\n    clearOrderData(paymentMethodId, containerId) {\n        const containerKey = `${paymentMethodId}_${containerId}`;\n        const containerInfo = this.containers.get(containerKey);\n\n        if (containerInfo) {\n            containerInfo.orderId = null;\n            containerInfo.clientToken = null;\n            containerInfo.instructions = null; // Also clear instructions\n            containerInfo.isInitialized = false; // Reset initialization state\n        }\n    }\n\n    // Set initialization status\n    setContainerInitialized(paymentMethodId, containerId) {\n        const containerKey = `${paymentMethodId}_${containerId}`;\n        const containerInfo = this.containers.get(containerKey);\n        if (containerInfo) {\n            containerInfo.isInitialized = true;\n        }\n    }\n\n    // Check if container is initialized\n    isContainerInitialized(paymentMethodId, containerId) {\n        const containerKey = `${paymentMethodId}_${containerId}`;\n        const containerInfo = this.containers.get(containerKey);\n        return containerInfo ? containerInfo.isInitialized : false;\n    }\n\n    // Set order and token data\n    setOrderData(paymentMethodId, containerId, orderId, clientToken, instructions = null) {\n        const containerKey = `${paymentMethodId}_${containerId}`;\n        const containerInfo = this.containers.get(containerKey);\n        if (containerInfo) {\n            containerInfo.orderId = orderId;\n            containerInfo.clientToken = clientToken;\n            containerInfo.instructions = instructions; // Add instructions storage (using for PayID/Bank Transfer)\n        } else {\n            console.warn(`[PrimerCheckoutManager] Container ${containerKey} not found for setting order data`);\n        }\n    }\n\n    // Get order data\n    getOrderData(paymentMethodId, containerId) {\n        const containerKey = `${paymentMethodId}_${containerId}`;\n        const containerInfo = this.containers.get(containerKey);\n        const result = containerInfo ? {\n            orderId: containerInfo.orderId,\n            clientToken: containerInfo.clientToken,\n            instructions: containerInfo.instructions // Include instructions in returned data\n        } : { orderId: null, clientToken: null, instructions: null };\n        \n        return result;\n    }\n\n    // Get container element\n    getContainerElement(paymentMethodId, containerId) {\n        const containerKey = `${paymentMethodId}_${containerId}`;\n        const containerInfo = this.containers.get(containerKey);\n        return containerInfo ? containerInfo.element : null;\n    }\n\n    // Cleanup\n    cleanup() {\n        this.containers.forEach((containerInfo) => {\n            if (containerInfo.element && containerInfo.element.parentNode) {\n                containerInfo.element.parentNode.removeChild(containerInfo.element);\n            }\n        });\n        this.containers.clear();\n        this.activeContainers.clear();\n    }\n}\n\n// Global instance\nconst primerCheckoutManager = new PrimerCheckoutManager();\n\n// Hook for persistent container management\nexport const usePersistentPaymentDetailsContainer = (paymentMethodId, containerId) => {\n    const targetRef = useRef(null);\n    const isActiveRef = useRef(false);\n    \n    // Generate persistent container ID\n    const persistentContainerId = `persistent-${paymentMethodId}-${containerId}`;\n\n    useEffect(() => {\n        // Create or get container\n        const containerInfo = primerCheckoutManager.getOrCreateContainer(paymentMethodId, containerId);\n        \n        // Update the container's ID to match what Primer expects\n        if (containerInfo.element) {\n            containerInfo.element.id = persistentContainerId;\n        }\n        \n        // Show container when component mounts\n        if (targetRef.current && !isActiveRef.current) {\n            primerCheckoutManager.showContainer(paymentMethodId, containerId, targetRef.current);\n            isActiveRef.current = true;\n        }\n\n        // Cleanup on unmount\n        return () => {\n            if (isActiveRef.current) {\n                primerCheckoutManager.hideContainer(paymentMethodId, containerId);\n                isActiveRef.current = false;\n            }\n        };\n    }, [paymentMethodId, containerId]);\n\n    return {\n        targetRef,\n        containerElement: primerCheckoutManager.getContainerElement(paymentMethodId, containerId),\n        containerIdActive: persistentContainerId, // Return the active container ID for Primer\n        isContainerInitialized: primerCheckoutManager.isContainerInitialized(paymentMethodId, containerId),\n        setContainerInitialized: () => primerCheckoutManager.setContainerInitialized(paymentMethodId, containerId),\n        // Order data persistence methods\n        setOrderData: (orderId, clientToken, instructions = null) => primerCheckoutManager.setOrderData(paymentMethodId, containerId, orderId, clientToken, instructions),\n        getOrderData: () => primerCheckoutManager.getOrderData(paymentMethodId, containerId),\n        clearOrderData: () => {\n            primerCheckoutManager.clearOrderData(paymentMethodId, containerId);\n        },\n        showContainer: () => {\n            if (targetRef.current && !isActiveRef.current) {\n                primerCheckoutManager.showContainer(paymentMethodId, containerId, targetRef.current);\n                isActiveRef.current = true;\n            }\n        },\n        hideContainer: () => {\n            if (isActiveRef.current) {\n                primerCheckoutManager.hideContainer(paymentMethodId, containerId);\n                isActiveRef.current = false;\n            }\n        }\n    };\n};\n\n// Cleanup function for page unload\nif (typeof window !== 'undefined') {\n    window.addEventListener('beforeunload', () => {\n        primerCheckoutManager.cleanup();\n    });\n}\n\nexport default primerCheckoutManager;\n", "import { useEffect, useState, useCallback, useRef } from '@wordpress/element';\nimport { usePersistentPaymentDetailsContainer } from './usePersistentPaymentDetailsContainer';\n\nexport const usePrimerCheckoutInitialization = ({\n    settings,\n    billing,\n    shippingData,\n    orderId: existingOrderId, // Accept existing order ID from props\n    containerId = \"in-checkout-primer-sdk-container\",\n    paymentMethodId = \"monoova_card\",\n    hasRequiredInfo = true // New parameter to control API calling\n}) => {\n    const [isInitialized, setIsInitialized] = useState(false);\n    const [clientToken, setClientToken] = useState(null);\n    const [orderId, setOrderId] = useState(existingOrderId || null); // Initialize with existing order ID\n    const [isLoading, setIsLoading] = useState(false);\n    const [billingRef, setBillingRef] = useState(null);\n    \n    // Use persistent container management\n    const {\n        targetRef,\n        containerElement,\n        containerIdActive, // This will be the actual ID of the persistent container\n        isContainerInitialized,\n        setContainerInitialized,\n        setOrderData, // Store order data in persistent container\n        getOrderData, // Retrieve order data from persistent container\n        showContainer,\n        hideContainer\n    } = usePersistentPaymentDetailsContainer(paymentMethodId, containerId);\n    \n    // Use refs to prevent duplicate API calls\n    const isCreatingOrderRef = useRef(false);\n    const isInitializingPrimerRef = useRef(false);\n\n    // Check if we should use existing initialized container\n    const shouldUseExistingContainer = isContainerInitialized && containerElement;\n    \n    // Get persistent order data\n    const persistentOrderData = getOrderData();\n    const persistentOrderId = persistentOrderData?.orderId;\n    const persistentClientToken = persistentOrderData?.clientToken;\n\n    // Reset function for retry scenarios\n    const resetStates = useCallback(() => {\n        setIsInitialized(false);\n        setClientToken(null);\n        setOrderId(null);\n        setIsLoading(false);\n        setBillingRef(null);\n        \n        // Reset refs to allow new initialization\n        isCreatingOrderRef.current = false;\n        isInitializingPrimerRef.current = false;\n        \n        // Note: We don't reset the persistent container here as it should remain available\n    }, []);\n    const loadPrimerSDK = useCallback(() => {\n        return new Promise((resolve, reject) => {\n            // check if Primer and 2 CDNs are already loaded\n            if (\n                window.Primer && document.querySelector('link[href=\"https://sdk.primer.io/web/v2.54.5/Checkout.css\"]') &&\n                document.querySelector('script[src=\"https://sdk.primer.io/web/v2.54.5/Primer.min.js\"]')\n            ) {\n                resolve();\n                return;\n            }\n            // need to load 2 CDNs below:\n            // <link rel=\"stylesheet\" href=\"https://sdk.primer.io/web/v2.54.5/Checkout.css\" />\n            //<script src=\"https://sdk.primer.io/web/v2.54.5/Primer.min.js\" integrity=\"sha384-yUCj6Q8h0Q6zFc35iT7v7pFoqlgpBD/xVr5SQxOgAnu2Cq286mf7IAyrFBJ8OsIa\" crossorigin=\"anonymous\"></script>\n\n            // load css\n            const link = document.createElement('link');\n            link.rel = 'stylesheet';\n            link.href = 'https://sdk.primer.io/web/v2.54.5/Checkout.css';\n            document.head.appendChild(link);\n\n            const script = document.createElement('script');\n            script.src = 'https://sdk.primer.io/web/v2.54.5/Primer.min.js';\n            script.crossOrigin = 'anonymous';\n\n            script.onload = resolve;\n            script.onerror = reject;\n            document.head.appendChild(script);\n        });\n    }, []);\n\n    const createOrderAndGetToken = useCallback(async () => {\n        // Prevent duplicate API calls\n        if (isCreatingOrderRef.current) {\n            return null;\n        }\n\n        // If we already have a token, don't create another one\n        if (clientToken || persistentClientToken) {\n            const existingToken = clientToken || persistentClientToken;\n            const finalOrderId = existingOrderId || orderId || persistentOrderId;\n            \n            // Update local state if using persistent data\n            if (!clientToken && persistentClientToken) {\n                setClientToken(persistentClientToken);\n            }\n            if (!orderId && finalOrderId) {\n                setOrderId(finalOrderId);\n            }\n            \n            return { token: existingToken, orderId: finalOrderId, checkoutData: null };\n        }\n\n        // Use existing order ID if available (WooCommerce checkout draft order)\n        const finalOrderId = existingOrderId || orderId || persistentOrderId;\n        \n        if (!finalOrderId) {\n            throw new Error('No order ID available for token generation');\n        }\n\n        try {\n            isCreatingOrderRef.current = true;\n\n            // Format address data with fallbacks for checkout page\n            const formatAddress = (address) => ({\n                first_name: address?.first_name || '',\n                last_name: address?.last_name || '',\n                company: address?.company || '',\n                address_1: address?.address_1 || '',\n                address_2: address?.address_2 || '',\n                city: address?.city || '',\n                state: address?.state || '',\n                postcode: address?.postcode || '',\n                country: address?.country || '',\n                email: address?.email || '',\n                phone: address?.phone || ''\n            });\n\n            // Use billing address or create empty object for checkout\n            const billingAddress = billing?.billingAddress || {};\n\n            // Generate client token for existing order via AJAX\n            const response = await fetch(settings.ajax_url, {\n                method: 'POST',\n                body: new URLSearchParams({\n                    action: 'monoova_get_client_token', // Use the client token endpoint instead of express checkout\n                    _wpnonce: settings.generate_token_nonce,\n                    order_id: finalOrderId, // Pass the existing order ID,\n                    is_in_checkout: !!document.body.classList.contains('woocommerce-checkout'),\n                    billingAddress: JSON.stringify(formatAddress(billingAddress)), // Include billing address as fallback\n                    card_type: 'visa' // Default card type\n                })\n            });\n\n            const result = await response.json();\n\n            if (!result.success) {\n                throw new Error(result.data?.message || 'Failed to generate client token');\n            }\n\n            const { clientToken: token } = result.data;\n            setClientToken(token);\n            setOrderId(finalOrderId);\n            \n            // Store in persistent container for later use\n            setOrderData(finalOrderId, token);\n\n            return { token, orderId: finalOrderId, checkoutData: result.data };\n\n        } catch (error) {\n            console.error('Error generating client token:', error);\n            throw error;\n        } finally {\n            isCreatingOrderRef.current = false;\n        }\n    }, [settings, billing, shippingData, clientToken, orderId, existingOrderId, persistentClientToken, persistentOrderId]);\n\n    // Handle payment completion\n    const handlePaymentComplete = useCallback(async (data, checkoutData) => {\n        try {\n            const primerPaymentId = data?.payment?.id;\n            if (!primerPaymentId) {\n                throw new Error('Payment ID not received from Primer');\n            }\n\n            // Get the most current order data at payment completion time\n            const currentOrderData = getOrderData();\n            const currentOrderId = orderId || currentOrderData?.orderId;\n            const currentClientToken = clientToken || currentOrderData?.clientToken;\n\n            if (!currentOrderId) {\n                throw new Error('Order ID not available for payment completion');\n            }\n\n            // Complete payment via AJAX\n            const response = await fetch(settings.ajax_url, {\n                method: 'POST',\n                body: new URLSearchParams({\n                    action: settings.ajax_complete_express_action,\n                    nonce: settings.express_checkout_nonce,\n                    orderId: currentOrderId,\n                    primerPaymentId: primerPaymentId,\n                    clientRef: checkoutData?.clientTransactionUniqueReference || '',\n                    is_in_checkout: !!document.body.classList.contains('woocommerce-checkout'),\n                })\n            });\n\n            const result = await response.json();\n\n            if (result.success) {\n                // Payment successful - redirect to success page\n                window.location.href = result.data.redirect_url;\n            } else {\n                throw new Error(result.data?.message || 'Payment completion failed');\n            }\n\n        } catch (error) {\n            console.error('Error completing payment:', error);\n            alert(error.message || settings.i18n?.generic_error || 'Payment completion failed');\n            resetStates();\n        }\n    }, [settings, orderId, clientToken, getOrderData, resetStates]);\n\n    // Initialize Primer Universal Checkout with token\n    const initializePrimerCheckout = useCallback(async (token, checkoutData) => {\n        // Prevent duplicate Primer initialization\n        if (isInitializingPrimerRef.current) {\n            return;\n        }\n\n        // If already initialized, don't initialize again\n        if (isContainerInitialized || isInitialized) {\n            return;\n        }\n\n        try {\n            isInitializingPrimerRef.current = true;\n\n            // Load Primer SDK\n            await loadPrimerSDK();\n\n            const { Primer } = window;\n\n            if (!Primer) {\n                throw new Error('Primer SDK not loaded');\n            }\n\n            const primerOptions = {\n                container: `#${containerIdActive || containerId}`, // Use persistent container ID or fallback to original\n                clientSessionCachingEnabled: true,\n                style: {\n                    inputLabel: {\n                        fontFamily: settings.checkout_ui_styles?.input_label?.font_family || 'Helvetica, Arial, sans-serif',\n                        fontSize: settings.checkout_ui_styles?.input_label?.font_size || '14px',\n                        fontWeight: settings.checkout_ui_styles?.input_label?.font_weight || 'normal',\n                        color: settings.checkout_ui_styles?.input_label?.color || '#000000',\n                    },\n                    input: {\n                        base: {\n                            fontFamily: settings.checkout_ui_styles?.input?.font_family || 'Helvetica, Arial, sans-serif',\n                            fontSize: settings.checkout_ui_styles?.input?.font_size || '14px',\n                            fontWeight: settings.checkout_ui_styles?.input?.font_weight || 'normal',\n                            background: settings.checkout_ui_styles?.input?.background_color || '#FAFAFA',\n                            borderColor: settings.checkout_ui_styles?.input?.border_color || '#E8E8E8',\n                            borderRadius: settings.checkout_ui_styles?.input?.border_radius || '8px',\n                            color: settings.checkout_ui_styles?.input?.text_color || '#000000',\n                        },\n                        focus: {\n                            borderColor: '#2ab5c4',\n                            boxShadow: '0 0 0 2px rgba(42, 181, 196, 0.2)',\n                        },\n                        error: {\n                            borderColor: '#d63638',\n                            color: '#d63638',\n                        }\n                    },\n                    submitButton: {\n                        base: {\n                            color: settings.checkout_ui_styles?.submit_button?.text_color || '#000000',\n                            background: settings.checkout_ui_styles?.submit_button?.background || '#2ab5c4',\n                            borderRadius: settings.checkout_ui_styles?.submit_button?.border_radius || '10px',\n                            borderColor: settings.checkout_ui_styles?.submit_button?.border_color || '#2ab5c4',\n                            fontFamily: settings.checkout_ui_styles?.submit_button?.font_family || 'Helvetica, Arial, sans-serif',\n                            fontSize: settings.checkout_ui_styles?.submit_button?.font_size || '17px',\n                            fontWeight: settings.checkout_ui_styles?.submit_button?.font_weight || 'bold',\n                            boxShadow: 'none'\n                        },\n                        disabled: {\n                            color: '#9b9b9b',\n                            background: '#e1deda'\n                        }\n                    },\n                    loadingScreen: {\n                        color: settings.checkout_ui_styles?.submit_button?.background || '#2ab5c4'\n                    }\n                },\n                errorMessage: {\n                    disabled: true\n                },\n                successScreen: false, // Disable success screen as we handle it manually\n                onCheckoutComplete: (data) => {\n                    console.log('Primer checkout complete:', data);\n                    handlePaymentComplete(data, checkoutData);\n                },\n                onCheckoutFail: (error, { payment }) => {\n                    console.error('Primer checkout failed:', error, payment);\n                    \n                    let errorMessage = settings.i18n?.generic_error || 'Payment failed';\n                    if (error && error.message) {\n                        errorMessage = error.message;\n                    } else if (payment && payment.processor && payment.processor.message) {\n                        errorMessage = payment.processor.message;\n                    }\n                    \n                    alert(errorMessage);\n                    resetStates();\n                },\n                onCheckoutCancel: () => {\n                    console.log('Primer checkout cancelled');\n                    resetStates();\n                }\n            };\n\n            // Initialize Primer Universal Checkout - this will show the form with loading indicator\n            await Primer.showUniversalCheckout(token, primerOptions);\n            \n            // Mark as initialized\n            setIsInitialized(true);\n            setContainerInitialized(); // Mark persistent container as initialized\n            setIsLoading(false);\n        } catch (error) {\n            console.error('Error initializing Primer checkout:', error);\n            setIsLoading(false);\n            alert(error.message || settings.i18n?.generic_error || 'Failed to initialize payment form');\n        } finally {\n            isInitializingPrimerRef.current = false;\n        }\n    }, [settings, containerId, loadPrimerSDK, handlePaymentComplete, resetStates, isInitialized, containerIdActive, setContainerInitialized]);\n\n\n    // Initialize payment when conditions are met - with persistent container support\n    useEffect(() => {\n        const initializePayment = async () => {\n            // If we have an existing initialized container, just show it and return\n            if (shouldUseExistingContainer) {\n                setIsInitialized(true);\n                setIsLoading(false);\n                \n                // Restore persistent data to local state\n                if (persistentClientToken && !clientToken) {\n                    setClientToken(persistentClientToken);\n                }\n                if (persistentOrderId && !orderId) {\n                    setOrderId(persistentOrderId);\n                }\n                \n                showContainer();\n                return;\n            }\n\n            // Prevent any initialization if already done or in progress\n            if (isInitialized || isLoading || isCreatingOrderRef.current || isInitializingPrimerRef.current) {\n                return;\n            }\n\n            // Check if we have an existing order ID (required for token generation)\n            if (!existingOrderId) {\n                return;\n            }\n\n            // Check if guest has provided all required information before proceeding\n            if (!hasRequiredInfo) {\n                return;\n            }\n\n            // Create unique reference for billing data to prevent duplicate initialization\n            const currentBillingRef = billing?.billingAddress ? \n                `${billing.billingAddress.email}_${billing.billingAddress.first_name}_${billing.billingAddress.last_name}` : null;\n            \n            // We should initialize if we haven't already, and we have billing info or are on checkout page\n            const hasBillingData = billing?.billingAddress || document.body.classList.contains('woocommerce-checkout');\n            const billingDataChanged = currentBillingRef !== billingRef;\n            \n            if (!hasBillingData || !billingDataChanged) {\n                return;\n            }\n            \n            try {\n                setIsLoading(true);\n                setBillingRef(currentBillingRef); // Set ref to prevent re-initialization with same data\n\n                const result = await createOrderAndGetToken();\n                \n                if (result && result.token) {\n                    await initializePrimerCheckout(result.token, result.checkoutData);\n                } else {\n                    console.warn('No token received from order creation');\n                    setIsLoading(false);\n                }\n                \n            } catch (error) {\n                console.error('Error during payment initialization:', error);\n                setIsLoading(false);\n                alert(error.message || 'Failed to initialize payment form.');\n            }\n        };\n\n        // Use a small delay to prevent rapid successive calls during React hydration\n        const timeoutId = setTimeout(initializePayment, 100);\n        \n        return () => clearTimeout(timeoutId);\n    }, [\n        existingOrderId, // Include existing order ID to trigger when it becomes available\n        hasRequiredInfo, // Replace individual billing field dependencies with this\n        shouldUseExistingContainer,\n        showContainer,\n        persistentOrderId,\n        persistentClientToken\n    ]); // Only depend on required info validation and container state\n\n    return {\n        isInitialized: isInitialized || shouldUseExistingContainer,\n        isLoading,\n        clientToken: clientToken || persistentClientToken, // Return persistent token if available\n        orderId: existingOrderId || orderId || persistentOrderId, // Prioritize existing order ID\n        resetStates,\n        containerRef: targetRef // Return ref for the target container\n    };\n};", "module.exports = wc.wcBlocksData;", "module.exports = wc.wcBlocksRegistry;", "module.exports = wc.wcSettings;", "module.exports = window[\"wp\"][\"data\"];", "module.exports = window[\"wp\"][\"element\"];", "module.exports = window[\"wp\"][\"htmlEntities\"];", "module.exports = window[\"wp\"][\"i18n\"];", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "import { registerPaymentMethod } from '@woocommerce/blocks-registry';\nimport { decodeEntities } from '@wordpress/html-entities';\nimport { getSetting } from '@woocommerce/settings';\nimport { __ } from '@wordpress/i18n';\nimport { useEffect } from '@wordpress/element';\nimport { useSelect } from '@wordpress/data';\nimport { CHECKOUT_STORE_KEY } from '@woocommerce/block-data';\nimport { PrimerCheckoutContainer } from './primer-checkout-container';\nimport { usePrimerCheckoutInitialization } from '../hooks/usePrimerCheckoutInitialization';\n/**\n * Internal dependencies\n */\n// We might need specific components for card fields later\n// import CardFields from './components/card-fields';\n\n// Defensive check for WooCommerce availability\nif (typeof registerPaymentMethod === 'undefined') {\n    console.warn('WooCommerce Blocks registerPaymentMethod is not available. This may be due to edit mode or missing dependencies.');\n} else {\n    // Main logic - only execute if registerPaymentMethod is available\n    \n    // Try to get settings from WooCommerce settings registry first, then fallback to global variable\n    let settings = {};\n    try {\n        // Check if getSetting is available before using it\n        if (typeof getSetting !== 'undefined') {\n            // The key should match the payment method name (monoova_card), not monoova_card_data\n            settings = getSetting('monoova_card_data', {});\n        } else {\n            throw new Error('getSetting function not available');\n        }\n    } catch (error) {\n        console.error('getSetting not available, trying fallback:', error);\n        // Fallback to global variable if getSetting is not available (e.g., in edit mode)\n        settings = window.monoova_card_blocks_params || {};\n    }\n\n    if (!settings || typeof settings !== 'object' || Object.keys(settings).length === 0) {\n        console.warn(\"Monoova Card settings not found or empty, using defaults\");\n        settings = {\n            title: 'Credit / Debit Card',\n            description: 'Accept payments via Mastercard, Visa, Apple pay and Google pay',\n            supports: []\n        };\n    }\n\n    const defaultLabel = __('Credit / Debit Card', 'monoova-payments-for-woocommerce');\n    const label = decodeEntities(settings.title) || defaultLabel;\n\n    /**\n     * Content component with Primer Universal Checkout\n     */\n    const Content = ({ \n        billing, \n        shippingData, \n        checkoutStatus, \n        paymentStatus, \n        eventRegistration, \n        emitResponse \n    }) => {\n        const description = decodeEntities(settings.description || '');\n\n        // Get the existing order ID from WooCommerce checkout store\n        const { orderId } = useSelect((select) => {\n            const store = select(CHECKOUT_STORE_KEY);\n            return {\n                orderId: store.getOrderId(),\n            };\n        });\n\n        // Helper function to check if required guest information is available\n        const hasRequiredGuestInfo = () => {\n            const billingAddress = billing?.billingAddress;\n            if (!billingAddress) return false;\n            \n            // Required fields for guest customers to generate client token\n            return !!(\n                billingAddress.email &&\n                billingAddress.first_name &&\n                billingAddress.last_name &&\n                billingAddress.address_1 &&\n                billingAddress.city &&\n                billingAddress.postcode &&\n                billingAddress.country &&\n                billingAddress.state &&\n                billingAddress.city\n            );\n        };\n\n        // Use the custom hook for Primer checkout initialization\n        const {\n            isInitialized,\n            isLoading,\n            clientToken,\n            orderId: hookOrderId,\n            resetStates,\n            containerRef\n        } = usePrimerCheckoutInitialization({\n            settings,\n            billing,\n            shippingData,\n            orderId, // Pass the existing order ID from checkout store\n            containerId: \"in-checkout-primer-sdk-container\",\n            paymentMethodId: \"monoova_card\",\n            hasRequiredInfo: hasRequiredGuestInfo() // Pass the validation result\n        });\n\n        useEffect(() => {\n            // Selector for the block checkout's \"Place Order\" button\n            const placeOrderButton = document.querySelector('.wc-block-components-button.wp-element-button.wc-block-components-checkout-place-order-button');\n\n            if (placeOrderButton) {\n                // Hide the button when Monoova Card is selected\n                placeOrderButton.style.display = 'none';\n            }\n\n            // Cleanup function: runs when component unmounts (e.g., user selects another payment method)\n            return () => {\n                if (placeOrderButton) {\n                    // Restore the button's default display style\n                    placeOrderButton.style.display = '';\n                }\n            };\n        }, []);\n\n        return (\n            <div>\n                <div\n                    className=\"monoova-card-content\"\n                    dangerouslySetInnerHTML={{ __html: description }}\n                />\n                {!hasRequiredGuestInfo() && !isInitialized && !isLoading && (\n                    <div className=\"monoova-card-info\">\n                        <p>Please complete your billing information to initialize the payment form.</p>\n                        <small>Required: Email, Name, Address, City, Postcode, and Country</small>\n                    </div>\n                )}\n                <PrimerCheckoutContainer\n                    containerId=\"in-checkout-primer-sdk-container\"\n                    isInitialized={isInitialized}\n                    isLoading={isLoading}\n                    containerRef={containerRef}\n                />\n            </div>\n        );\n    };\n\n    /**\n     * Label component\n     *\n     * @param {Object} props Props from payment API.\n     */\n    const Label = (props) => {\n        \n        const { PaymentMethodLabel } = props.components || {};\n        return (\n            <div style={{\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center',\n                width: '100%'\n            }}>\n                <span style={{ fontWeight: 600 }}>{label}</span>\n                <img\n                    src={`${settings.plugin_url || '/wp-content/plugins/monoova-payments-for-woocommerce/'}assets/images/card-payment-method-types.svg`}\n                    alt=\"Card payment methods\"\n                    style={{\n                        height: '32px',\n                        maxHeight: '32px',\n                        width: 'auto'\n                    }}\n                />\n            </div>\n        );\n    };\n\n    /**\n     * Edit component (for block editor - don't initialize payment)\n     */\n    const Edit = () => {\n        const description = decodeEntities(settings.description || '');\n        return (\n            <div>\n                <div\n                    className=\"monoova-card-content\"\n                    dangerouslySetInnerHTML={{ __html: description }}\n                />\n                <div className=\"embedded-card-form\">\n                    <div id=\"in-checkout-primer-sdk-container\">\n                        \n                    </div>\n                </div>\n            </div>\n        );\n    };\n\n    /**\n     * Monoova Card payment method config object.\n     */\n    const MonoovaCard = {\n        name: \"monoova_card\", // Matches the name in PHP\n        label: <Label />,\n        content: <Content />,\n        edit: <Edit />,\n        canMakePayment: () => true, // Basic check, might need refinement\n        ariaLabel: label,\n        supports: {\n            features: settings?.supports ?? [],\n            // Add other supported features like savePaymentInfo if applicable\n        },\n        // Add icons if available in settings\n        icons: settings?.icons ?? null,\n    };\n\n    // Register the payment method\n    try {\n        registerPaymentMethod(MonoovaCard);\n    } catch (error) {\n        console.error('Failed to register Monoova Card payment method:', error);\n    }\n}\n"], "names": ["PrimerCheckoutContainer", "containerId", "isLoading", "isInitialized", "containerRef", "React", "createElement", "className", "style", "marginTop", "padding", "textAlign", "color", "ref", "id", "useEffect", "useRef", "PrimerCheckoutManager", "constructor", "containers", "Map", "activeContainers", "Set", "getOrCreateContainer", "paymentMethodId", "containerKey", "has", "container", "document", "cssText", "body", "append<PERSON><PERSON><PERSON>", "set", "element", "isVisible", "orderId", "clientToken", "get", "showContainer", "targetElement", "containerInfo", "hideAllContainers", "add", "<PERSON><PERSON><PERSON><PERSON>", "delete", "for<PERSON>ach", "split", "clearOrderData", "instructions", "setContainerInitialized", "isContainerInitialized", "setOrderData", "console", "warn", "getOrderData", "result", "getContainerElement", "cleanup", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "clear", "primerCheckoutManager", "usePersistentPaymentDetailsContainer", "targetRef", "isActiveRef", "persistentContainerId", "current", "containerElement", "containerIdActive", "window", "addEventListener", "useState", "useCallback", "usePrimerCheckoutInitialization", "settings", "billing", "shippingData", "existingOrderId", "hasRequiredInfo", "setIsInitialized", "setClientToken", "setOrderId", "setIsLoading", "billingRef", "setBillingRef", "isCreatingOrderRef", "isInitializingPrimerRef", "shouldUseExistingContainer", "persistentOrderData", "persistentOrderId", "persistentClientToken", "resetStates", "loadPrimerSDK", "Promise", "resolve", "reject", "Primer", "querySelector", "link", "rel", "href", "head", "script", "src", "crossOrigin", "onload", "onerror", "createOrderAndGetToken", "existingToken", "finalOrderId", "token", "checkoutData", "Error", "formatAddress", "address", "first_name", "last_name", "company", "address_1", "address_2", "city", "state", "postcode", "country", "email", "phone", "billing<PERSON><PERSON>ress", "response", "fetch", "ajax_url", "method", "URLSearchParams", "action", "_wpnonce", "generate_token_nonce", "order_id", "is_in_checkout", "classList", "contains", "JSON", "stringify", "card_type", "json", "success", "data", "message", "error", "handlePaymentComplete", "primerPaymentId", "payment", "currentOrderData", "currentOrderId", "currentClientToken", "ajax_complete_express_action", "nonce", "express_checkout_nonce", "clientRef", "clientTransactionUniqueReference", "location", "redirect_url", "alert", "i18n", "generic_error", "initializePrimerCheckout", "primerOptions", "clientSessionCachingEnabled", "inputLabel", "fontFamily", "checkout_ui_styles", "input_label", "font_family", "fontSize", "font_size", "fontWeight", "font_weight", "input", "base", "background", "background_color", "borderColor", "border_color", "borderRadius", "border_radius", "text_color", "focus", "boxShadow", "submitButton", "submit_button", "disabled", "loadingScreen", "errorMessage", "successScreen", "onCheckoutComplete", "log", "onCheckoutFail", "processor", "onCheckoutCancel", "showUniversalCheckout", "initializePayment", "currentBillingRef", "hasBillingData", "billingDataChanged", "timeoutId", "setTimeout", "clearTimeout", "registerPaymentMethod", "decodeEntities", "getSetting", "__", "useSelect", "CHECKOUT_STORE_KEY", "monoova_card_blocks_params", "Object", "keys", "length", "title", "description", "supports", "defaultLabel", "label", "Content", "checkoutStatus", "paymentStatus", "eventRegistration", "emitResponse", "select", "store", "getOrderId", "hasRequiredGuestInfo", "hookOrderId", "placeOrderButton", "display", "dangerouslySetInnerHTML", "__html", "Label", "props", "PaymentMethodLabel", "components", "justifyContent", "alignItems", "width", "plugin_url", "alt", "height", "maxHeight", "Edit", "MonoovaCard", "name", "content", "edit", "canMakePayment", "aria<PERSON><PERSON><PERSON>", "features", "icons"], "sourceRoot": ""}