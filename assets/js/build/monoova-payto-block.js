<<<<<<< HEAD
(()=>{"use strict";const e=wc.wcBlocksRegistry,o=wc.wcSettings,t=window.wp.htmlEntities,a=window.wp.i18n,n=window.wp.element;if("function"!=typeof e.registerPaymentMethod)console.warn("Monoova PayTo Block: registerPaymentMethod not available. Available globals:",Object.keys(window.wc||{}));else{let r={};if("function"==typeof o.getSetting)try{r=(0,o.getSetting)("monoova_payto_data",{})}catch(e){console.log("Monoova PayTo Block: getSetting failed:",e)}r&&0!==Object.keys(r).length||(r=window.monoova_payto_blocks_params||{},console.log("Monoova PayTo Block: Using fallback settings:",r)),r&&0!==Object.keys(r).length||(console.warn("Monoova PayTo Block: No settings found, using defaults"),r={title:"PayTo",description:"Set up PayTo directly from your bank using BSB and Account Number or PayID.",supports:[]});const m=(0,a.__)("PayTo","monoova-payments-for-woocommerce"),c=(0,t.decodeEntities)(r.title)||m,l=({eventRegistration:e,emitResponse:o})=>{const[t,r]=(0,n.useState)("payid"),[m,c]=(0,n.useState)("PhoneNumber"),[l,i]=(0,n.useState)(""),[s,p]=(0,n.useState)(""),[d,y]=(0,n.useState)(""),[u,g]=(0,n.useState)(""),[_,b]=(0,n.useState)({}),{onPaymentSetup:v}=e,{responseTypes:f,noticeContexts:w}=o,h=()=>{const e={};if("payid"===t)if(l.trim()){const o=l.trim();switch(m){case"PhoneNumber":/^(\+61\s?[4-5]\d{8}|0[4-5]\d{8})$/.test(o)||(e.payidValue=(0,a.__)("Please enter a valid Australian mobile number (e.g., ********** or +***********).","monoova-payments-for-woocommerce"));break;case"Email":/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(o)||(e.payidValue=(0,a.__)("Please enter a valid email address.","monoova-payments-for-woocommerce"));break;case"ABN":/^\d{11}$/.test(o.replace(/\s/g,""))||(e.payidValue=(0,a.__)("Please enter a valid 11-digit ABN.","monoova-payments-for-woocommerce"));break;case"ACN":/^\d{9}$/.test(o.replace(/\s/g,""))||(e.payidValue=(0,a.__)("Please enter a valid 9-digit ACN.","monoova-payments-for-woocommerce"));break;case"OrganisationId":(!o||o.length<2)&&(e.payidValue=(0,a.__)("Please enter a valid Organisation ID.","monoova-payments-for-woocommerce"))}}else e.payidValue=(0,a.__)("PayID is required.","monoova-payments-for-woocommerce");else s.trim()||(e.accountName=(0,a.__)("Account name is required.","monoova-payments-for-woocommerce")),d.trim()?/^\d{3}-?\d{3}$/.test(d.trim())||(e.bsb=(0,a.__)("Please enter a valid BSB (6 digits).","monoova-payments-for-woocommerce")):e.bsb=(0,a.__)("BSB is required.","monoova-payments-for-woocommerce"),u.trim()||(e.accountNumber=(0,a.__)("Account number is required.","monoova-payments-for-woocommerce"));return b(e),0===Object.keys(e).length};return React.useEffect((()=>v((()=>h()?{type:f.SUCCESS,meta:{paymentMethodData:{payto_payment_method:t,payto_payid_type:"payid"===t?m:"",payto_payid_value:"payid"===t?l:"",payto_account_name:"bsb_account"===t?s:"",payto_bsb:"bsb_account"===t?d:"",payto_account_number:"bsb_account"===t?u:""}}}:{type:f.ERROR,message:(0,a.__)("Please correct the errors in the PayTo form.","monoova-payments-for-woocommerce"),messageContext:w.PAYMENTS}))),[v,t,m,l,s,d,u,h]),React.createElement("div",{className:"monoova-payto-form",style:{padding:"15px",border:"1px solid #ddd",borderRadius:"5px",backgroundColor:"#f9f9f9",margin:"10px 0"}},React.createElement("div",{className:"payto-payment-method-selection",style:{marginBottom:"15px"}},React.createElement("label",{style:{display:"block",marginBottom:"10px",fontWeight:"500"}},(0,a.__)("Pay with","monoova-payments-for-woocommerce")),React.createElement("div",null,React.createElement("label",{style:{marginRight:"20px",fontWeight:"normal"}},React.createElement("input",{type:"radio",name:"payto_payment_method",value:"payid",checked:"payid"===t,onChange:e=>r(e.target.value),style:{marginRight:"8px"}}),(0,a.__)("PayID","monoova-payments-for-woocommerce")),React.createElement("label",{style:{fontWeight:"normal"}},React.createElement("input",{type:"radio",name:"payto_payment_method",value:"bsb_account",checked:"bsb_account"===t,onChange:e=>r(e.target.value),style:{marginRight:"8px"}}),(0,a.__)("BSB and account number","monoova-payments-for-woocommerce")))),"payid"===t&&React.createElement("div",{className:"payto-fields-group",style:{padding:"10px",backgroundColor:"#fff",borderRadius:"3px"}},React.createElement("h4",{style:{marginTop:0,marginBottom:"10px",color:"#333"}},(0,a.__)("Enter your PayID details","monoova-payments-for-woocommerce")),React.createElement("div",{style:{marginBottom:"15px"}},React.createElement("label",{style:{display:"block",marginBottom:"5px",fontWeight:"500"}},(0,a.__)("PayID Type","monoova-payments-for-woocommerce")," *"),React.createElement("select",{value:m,onChange:e=>{c(e.target.value),i("")},style:{width:"100%",padding:"8px",border:"1px solid #ddd",borderRadius:"3px"}},React.createElement("option",{value:"PhoneNumber"},(0,a.__)("Mobile Number","monoova-payments-for-woocommerce")),React.createElement("option",{value:"Email"},(0,a.__)("Email Address","monoova-payments-for-woocommerce")),React.createElement("option",{value:"ABN"},(0,a.__)("ABN (Australian Business Number)","monoova-payments-for-woocommerce")),React.createElement("option",{value:"ACN"},(0,a.__)("ACN (Australian Company Number)","monoova-payments-for-woocommerce")),React.createElement("option",{value:"OrganisationId"},(0,a.__)("Organisation ID","monoova-payments-for-woocommerce")))),React.createElement("div",{style:{marginBottom:"10px"}},React.createElement("label",{style:{display:"block",marginBottom:"5px",fontWeight:"500"}},"PhoneNumber"===m&&(0,a.__)("Mobile Number","monoova-payments-for-woocommerce"),"Email"===m&&(0,a.__)("Email Address","monoova-payments-for-woocommerce"),"ABN"===m&&(0,a.__)("ABN","monoova-payments-for-woocommerce"),"ACN"===m&&(0,a.__)("ACN","monoova-payments-for-woocommerce"),"OrganisationId"===m&&(0,a.__)("Organisation ID","monoova-payments-for-woocommerce")," *"),React.createElement("input",{type:"Email"===m?"email":"PhoneNumber"===m?"tel":"text",value:l,onChange:e=>{let o=e.target.value;"PhoneNumber"===m&&(o=(e=>{const o=e.replace(/\D/g,"");return o.startsWith("61")?"+61"+o.slice(2):o.startsWith("0")?o:o.length<=9&&!o.startsWith("0")?"0"+o:e})(o)),i(o)},placeholder:"PhoneNumber"===m?(0,a.__)("********** or +***********","monoova-payments-for-woocommerce"):"Email"===m?(0,a.__)("<EMAIL>","monoova-payments-for-woocommerce"):"ABN"===m?(0,a.__)("*********01","monoova-payments-for-woocommerce"):"ACN"===m?(0,a.__)("*********","monoova-payments-for-woocommerce"):(0,a.__)("Enter Organisation ID","monoova-payments-for-woocommerce"),style:{width:"100%",padding:"8px",border:"1px solid #ddd",borderRadius:"3px",borderColor:_.payidValue?"#e74c3c":"#ddd"}}),_.payidValue&&React.createElement("div",{style:{color:"#e74c3c",fontSize:"12px",marginTop:"5px"}},_.payidValue),React.createElement("div",{style:{color:"#666",fontSize:"12px",marginTop:"5px"}},"PhoneNumber"===m&&(0,a.__)("Enter your mobile number with or without country code","monoova-payments-for-woocommerce"),"Email"===m&&(0,a.__)("Use the email address registered as your PayID","monoova-payments-for-woocommerce"),"ABN"===m&&(0,a.__)("Enter your 11-digit ABN","monoova-payments-for-woocommerce"),"ACN"===m&&(0,a.__)("Enter your 9-digit ACN","monoova-payments-for-woocommerce"),"OrganisationId"===m&&(0,a.__)("Enter your organisation identifier","monoova-payments-for-woocommerce")))),"bsb_account"===t&&React.createElement("div",{className:"payto-fields-group",style:{padding:"10px",backgroundColor:"#fff",borderRadius:"3px"}},React.createElement("h4",{style:{marginTop:0,marginBottom:"10px",color:"#333"}},(0,a.__)("Enter your bank details","monoova-payments-for-woocommerce")),React.createElement("div",{style:{marginBottom:"10px"}},React.createElement("label",{style:{display:"block",marginBottom:"5px",fontWeight:"500"}},(0,a.__)("Name associated with bank account","monoova-payments-for-woocommerce")," *"),React.createElement("input",{type:"text",value:s,onChange:e=>p(e.target.value),placeholder:(0,a.__)("Enter your name","monoova-payments-for-woocommerce"),style:{width:"100%",padding:"8px",border:"1px solid #ddd",borderRadius:"3px",borderColor:_.accountName?"#e74c3c":"#ddd"}}),_.accountName&&React.createElement("div",{style:{color:"#e74c3c",fontSize:"12px",marginTop:"5px"}},_.accountName)),React.createElement("div",{style:{display:"flex",gap:"10px",marginBottom:"10px"}},React.createElement("div",{style:{flex:1}},React.createElement("label",{style:{display:"block",marginBottom:"5px",fontWeight:"500"}},(0,a.__)("BSB","monoova-payments-for-woocommerce")," *"),React.createElement("input",{type:"text",value:d,onChange:e=>y(e.target.value),placeholder:(0,a.__)("Enter BSB","monoova-payments-for-woocommerce"),style:{width:"100%",padding:"8px",border:"1px solid #ddd",borderRadius:"3px",borderColor:_.bsb?"#e74c3c":"#ddd"}}),_.bsb&&React.createElement("div",{style:{color:"#e74c3c",fontSize:"12px",marginTop:"5px"}},_.bsb)),React.createElement("div",{style:{flex:1}},React.createElement("label",{style:{display:"block",marginBottom:"5px",fontWeight:"500"}},(0,a.__)("Account Number","monoova-payments-for-woocommerce")," *"),React.createElement("input",{type:"text",value:u,onChange:e=>g(e.target.value),placeholder:(0,a.__)("Enter your account number","monoova-payments-for-woocommerce"),style:{width:"100%",padding:"8px",border:"1px solid #ddd",borderRadius:"3px",borderColor:_.accountNumber?"#e74c3c":"#ddd"}}),_.accountNumber&&React.createElement("div",{style:{color:"#e74c3c",fontSize:"12px",marginTop:"5px"}},_.accountNumber)))),React.createElement("div",{style:{marginTop:"15px",padding:"10px",backgroundColor:"#e7f3ff",borderRadius:"3px",fontSize:"14px"}},React.createElement("strong",null,(0,a.__)("Authorise recurring payments","monoova-payments-for-woocommerce")),React.createElement("p",{style:{margin:"5px 0 0 0",color:"#666"}},(0,a.__)("Approve your recurring payment in your online banking or banking app!","monoova-payments-for-woocommerce"))))},i=function(e){return React.createElement(l,e)},s=e=>React.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",width:"100%"}},React.createElement("span",{style:{fontWeight:600}},c),React.createElement("img",{src:`${r.plugin_url||"/wp-content/plugins/monoova-payments-for-woocommerce/"}assets/images/payto-logo.svg`,alt:"PayTo logo",style:{height:"24px",width:"auto"}})),p={name:"monoova_payto",label:React.createElement(s,null),content:React.createElement(i,null),edit:React.createElement(i,null),canMakePayment:function(){return!0},ariaLabel:c,supports:{features:r.supports||[]},icons:r.icons||null};try{(0,e.registerPaymentMethod)(p)}catch(e){console.error("Monoova PayTo Block: Failed to register payment method:",e)}}})();
=======
/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "@woocommerce/blocks-registry":
/*!******************************************!*\
  !*** external ["wc","wcBlocksRegistry"] ***!
  \******************************************/
/***/ ((module) => {

module.exports = wc.wcBlocksRegistry;

/***/ }),

/***/ "@woocommerce/settings":
/*!************************************!*\
  !*** external ["wc","wcSettings"] ***!
  \************************************/
/***/ ((module) => {

module.exports = wc.wcSettings;

/***/ }),

/***/ "@wordpress/element":
/*!*********************************!*\
  !*** external ["wp","element"] ***!
  \*********************************/
/***/ ((module) => {

module.exports = window["wp"]["element"];

/***/ }),

/***/ "@wordpress/html-entities":
/*!**************************************!*\
  !*** external ["wp","htmlEntities"] ***!
  \**************************************/
/***/ ((module) => {

module.exports = window["wp"]["htmlEntities"];

/***/ }),

/***/ "@wordpress/i18n":
/*!******************************!*\
  !*** external ["wp","i18n"] ***!
  \******************************/
/***/ ((module) => {

module.exports = window["wp"]["i18n"];

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/compat get default export */
/******/ 	(() => {
/******/ 		// getDefaultExport function for compatibility with non-harmony modules
/******/ 		__webpack_require__.n = (module) => {
/******/ 			var getter = module && module.__esModule ?
/******/ 				() => (module['default']) :
/******/ 				() => (module);
/******/ 			__webpack_require__.d(getter, { a: getter });
/******/ 			return getter;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/************************************************************************/
var __webpack_exports__ = {};
// This entry needs to be wrapped in an IIFE because it needs to be isolated against other modules in the chunk.
(() => {
/*!*****************************************************!*\
  !*** ./assets/js/src/blocks/monoova-payto-block.js ***!
  \*****************************************************/
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _woocommerce_blocks_registry__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @woocommerce/blocks-registry */ "@woocommerce/blocks-registry");
/* harmony import */ var _woocommerce_blocks_registry__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_woocommerce_blocks_registry__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _woocommerce_settings__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @woocommerce/settings */ "@woocommerce/settings");
/* harmony import */ var _woocommerce_settings__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_woocommerce_settings__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _wordpress_html_entities__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @wordpress/html-entities */ "@wordpress/html-entities");
/* harmony import */ var _wordpress_html_entities__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_wordpress_html_entities__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @wordpress/i18n */ "@wordpress/i18n");
/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @wordpress/element */ "@wordpress/element");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_4__);
/**
 * Monoova PayTo Block for WooCommerce Blocks
 *
 * Uses ES6 imports with defensive programming to handle cases where WooCommerce dependencies
 * may not be available (edit mode, missing plugins, etc.)
 */







// If registerPaymentMethod is not available, we can't register the payment method
if (typeof _woocommerce_blocks_registry__WEBPACK_IMPORTED_MODULE_0__.registerPaymentMethod !== "function") {
  console.warn("Monoova PayTo Block: registerPaymentMethod not available. Available globals:", Object.keys(window.wc || {}));
} else {
  // Try to get settings
  let settings = {};
  if (typeof _woocommerce_settings__WEBPACK_IMPORTED_MODULE_1__.getSetting === "function") {
    try {
      settings = (0,_woocommerce_settings__WEBPACK_IMPORTED_MODULE_1__.getSetting)("monoova_payto_data", {});
    } catch (error) {
      console.log("Monoova PayTo Block: getSetting failed:", error);
    }
  }

  // Fallback to global variable if getSetting didn't work
  if (!settings || Object.keys(settings).length === 0) {
    settings = window.monoova_payto_blocks_params || {};
    console.log("Monoova PayTo Block: Using fallback settings:", settings);
  }

  // Set defaults if no settings available
  if (!settings || Object.keys(settings).length === 0) {
    console.warn("Monoova PayTo Block: No settings found, using defaults");
    settings = {
      title: "PayTo",
      description: "Set up PayTo directly from your bank using BSB and Account Number or PayID.",
      supports: []
    };
  }
  const defaultLabel = (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("PayTo", "monoova-payments-for-woocommerce");
  const label = (0,_wordpress_html_entities__WEBPACK_IMPORTED_MODULE_2__.decodeEntities)(settings.title) || defaultLabel;

  /**
   * PayTo Payment Form Component
   */
  const PayToForm = ({
    eventRegistration,
    emitResponse
  }) => {
    const [paymentMethod, setPaymentMethod] = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_4__.useState)("payid");
    const [payidType, setPayidType] = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_4__.useState)("PhoneNumber");
    const [payidValue, setPayidValue] = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_4__.useState)("");
    const [accountName, setAccountName] = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_4__.useState)("");
    const [bsb, setBsb] = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_4__.useState)("");
    const [accountNumber, setAccountNumber] = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_4__.useState)("");
    const [errors, setErrors] = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_4__.useState)({});
    const {
      onPaymentSetup
    } = eventRegistration;
    const {
      responseTypes,
      noticeContexts
    } = emitResponse;

    // Format mobile number input
    const formatMobileNumber = value => {
      // Remove all non-digits
      const numbers = value.replace(/\D/g, "");

      // Handle different input patterns
      if (numbers.startsWith("61")) {
        // Already has country code
        return "+61" + numbers.slice(2);
      } else if (numbers.startsWith("0")) {
        // Australian format starting with 0
        return numbers;
      } else if (numbers.length <= 9 && !numbers.startsWith("0")) {
        // Could be without leading 0, add it
        return "0" + numbers;
      }
      return value;
    };

    // Validation function
    const validateForm = () => {
      const newErrors = {};
      if (paymentMethod === "payid") {
        if (!payidValue.trim()) {
          newErrors.payidValue = (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("PayID is required.", "monoova-payments-for-woocommerce");
        } else {
          // Validate based on PayID type
          const value = payidValue.trim();
          switch (payidType) {
            case "PhoneNumber":
              if (!/^(\+61\s?[4-5]\d{8}|0[4-5]\d{8})$/.test(value)) {
                newErrors.payidValue = (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Please enter a valid Australian mobile number (e.g., ********** or +***********).", "monoova-payments-for-woocommerce");
              }
              break;
            case "Email":
              if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
                newErrors.payidValue = (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Please enter a valid email address.", "monoova-payments-for-woocommerce");
              }
              break;
            case "ABN":
              if (!/^\d{11}$/.test(value.replace(/\s/g, ""))) {
                newErrors.payidValue = (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Please enter a valid 11-digit ABN.", "monoova-payments-for-woocommerce");
              }
              break;
            case "ACN":
              if (!/^\d{9}$/.test(value.replace(/\s/g, ""))) {
                newErrors.payidValue = (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Please enter a valid 9-digit ACN.", "monoova-payments-for-woocommerce");
              }
              break;
            case "OrganisationId":
              if (!value || value.length < 2) {
                newErrors.payidValue = (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Please enter a valid Organisation ID.", "monoova-payments-for-woocommerce");
              }
              break;
          }
        }
      } else {
        if (!accountName.trim()) {
          newErrors.accountName = (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Account name is required.", "monoova-payments-for-woocommerce");
        }
        if (!bsb.trim()) {
          newErrors.bsb = (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("BSB is required.", "monoova-payments-for-woocommerce");
        } else if (!/^\d{3}-?\d{3}$/.test(bsb.trim())) {
          newErrors.bsb = (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Please enter a valid BSB (6 digits).", "monoova-payments-for-woocommerce");
        }
        if (!accountNumber.trim()) {
          newErrors.accountNumber = (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Account number is required.", "monoova-payments-for-woocommerce");
        }
      }
      setErrors(newErrors);
      return Object.keys(newErrors).length === 0;
    };

    // Register payment setup handler
    React.useEffect(() => {
      const unsubscribe = onPaymentSetup(() => {
        if (!validateForm()) {
          return {
            type: responseTypes.ERROR,
            message: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Please correct the errors in the PayTo form.", "monoova-payments-for-woocommerce"),
            messageContext: noticeContexts.PAYMENTS
          };
        }

        // Return payment data
        return {
          type: responseTypes.SUCCESS,
          meta: {
            paymentMethodData: {
              payto_payment_method: paymentMethod,
              payto_payid_type: paymentMethod === "payid" ? payidType : "",
              payto_payid_value: paymentMethod === "payid" ? payidValue : "",
              payto_account_name: paymentMethod === "bsb_account" ? accountName : "",
              payto_bsb: paymentMethod === "bsb_account" ? bsb : "",
              payto_account_number: paymentMethod === "bsb_account" ? accountNumber : ""
            }
          }
        };
      });
      return unsubscribe;
    }, [onPaymentSetup, paymentMethod, payidType, payidValue, accountName, bsb, accountNumber, validateForm]);
    return /*#__PURE__*/React.createElement("div", {
      className: "monoova-payto-form",
      style: {
        padding: "15px",
        border: "1px solid #ddd",
        borderRadius: "5px",
        backgroundColor: "#f9f9f9",
        margin: "10px 0"
      }
    }, /*#__PURE__*/React.createElement("div", {
      className: "payto-payment-method-selection",
      style: {
        marginBottom: "15px"
      }
    }, /*#__PURE__*/React.createElement("label", {
      style: {
        display: "block",
        marginBottom: "10px",
        fontWeight: "500"
      }
    }, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Pay with", "monoova-payments-for-woocommerce")), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("label", {
      style: {
        marginRight: "20px",
        fontWeight: "normal"
      }
    }, /*#__PURE__*/React.createElement("input", {
      type: "radio",
      name: "payto_payment_method",
      value: "payid",
      checked: paymentMethod === "payid",
      onChange: e => setPaymentMethod(e.target.value),
      style: {
        marginRight: "8px"
      }
    }), (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("PayID", "monoova-payments-for-woocommerce")), /*#__PURE__*/React.createElement("label", {
      style: {
        fontWeight: "normal"
      }
    }, /*#__PURE__*/React.createElement("input", {
      type: "radio",
      name: "payto_payment_method",
      value: "bsb_account",
      checked: paymentMethod === "bsb_account",
      onChange: e => setPaymentMethod(e.target.value),
      style: {
        marginRight: "8px"
      }
    }), (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("BSB and account number", "monoova-payments-for-woocommerce")))), paymentMethod === "payid" && /*#__PURE__*/React.createElement("div", {
      className: "payto-fields-group",
      style: {
        padding: "10px",
        backgroundColor: "#fff",
        borderRadius: "3px"
      }
    }, /*#__PURE__*/React.createElement("h4", {
      style: {
        marginTop: 0,
        marginBottom: "10px",
        color: "#333"
      }
    }, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Enter your PayID details", "monoova-payments-for-woocommerce")), /*#__PURE__*/React.createElement("div", {
      style: {
        marginBottom: "15px"
      }
    }, /*#__PURE__*/React.createElement("label", {
      style: {
        display: "block",
        marginBottom: "5px",
        fontWeight: "500"
      }
    }, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("PayID Type", "monoova-payments-for-woocommerce"), " *"), /*#__PURE__*/React.createElement("select", {
      value: payidType,
      onChange: e => {
        setPayidType(e.target.value);
        setPayidValue(""); // Clear value when type changes
      },
      style: {
        width: "100%",
        padding: "8px",
        border: "1px solid #ddd",
        borderRadius: "3px"
      }
    }, /*#__PURE__*/React.createElement("option", {
      value: "PhoneNumber"
    }, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Mobile Number", "monoova-payments-for-woocommerce")), /*#__PURE__*/React.createElement("option", {
      value: "Email"
    }, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Email Address", "monoova-payments-for-woocommerce")), /*#__PURE__*/React.createElement("option", {
      value: "ABN"
    }, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("ABN (Australian Business Number)", "monoova-payments-for-woocommerce")), /*#__PURE__*/React.createElement("option", {
      value: "ACN"
    }, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("ACN (Australian Company Number)", "monoova-payments-for-woocommerce")), /*#__PURE__*/React.createElement("option", {
      value: "OrganisationId"
    }, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Organisation ID", "monoova-payments-for-woocommerce")))), /*#__PURE__*/React.createElement("div", {
      style: {
        marginBottom: "10px"
      }
    }, /*#__PURE__*/React.createElement("label", {
      style: {
        display: "block",
        marginBottom: "5px",
        fontWeight: "500"
      }
    }, payidType === "PhoneNumber" && (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Mobile Number", "monoova-payments-for-woocommerce"), payidType === "Email" && (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Email Address", "monoova-payments-for-woocommerce"), payidType === "ABN" && (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("ABN", "monoova-payments-for-woocommerce"), payidType === "ACN" && (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("ACN", "monoova-payments-for-woocommerce"), payidType === "OrganisationId" && (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Organisation ID", "monoova-payments-for-woocommerce"), " *"), /*#__PURE__*/React.createElement("input", {
      type: payidType === "Email" ? "email" : payidType === "PhoneNumber" ? "tel" : "text",
      value: payidValue,
      onChange: e => {
        let value = e.target.value;
        // Format mobile number if it's a phone number
        if (payidType === "PhoneNumber") {
          value = formatMobileNumber(value);
        }
        setPayidValue(value);
      },
      placeholder: payidType === "PhoneNumber" ? (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("********** or +***********", "monoova-payments-for-woocommerce") : payidType === "Email" ? (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("<EMAIL>", "monoova-payments-for-woocommerce") : payidType === "ABN" ? (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("*********01", "monoova-payments-for-woocommerce") : payidType === "ACN" ? (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("*********", "monoova-payments-for-woocommerce") : (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Enter Organisation ID", "monoova-payments-for-woocommerce"),
      style: {
        width: "100%",
        padding: "8px",
        border: "1px solid #ddd",
        borderRadius: "3px",
        borderColor: errors.payidValue ? "#e74c3c" : "#ddd"
      }
    }), errors.payidValue && /*#__PURE__*/React.createElement("div", {
      style: {
        color: "#e74c3c",
        fontSize: "12px",
        marginTop: "5px"
      }
    }, errors.payidValue), /*#__PURE__*/React.createElement("div", {
      style: {
        color: "#666",
        fontSize: "12px",
        marginTop: "5px"
      }
    }, payidType === "PhoneNumber" && (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Enter your mobile number with or without country code", "monoova-payments-for-woocommerce"), payidType === "Email" && (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Use the email address registered as your PayID", "monoova-payments-for-woocommerce"), payidType === "ABN" && (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Enter your 11-digit ABN", "monoova-payments-for-woocommerce"), payidType === "ACN" && (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Enter your 9-digit ACN", "monoova-payments-for-woocommerce"), payidType === "OrganisationId" && (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Enter your organisation identifier", "monoova-payments-for-woocommerce")))), paymentMethod === "bsb_account" && /*#__PURE__*/React.createElement("div", {
      className: "payto-fields-group",
      style: {
        padding: "10px",
        backgroundColor: "#fff",
        borderRadius: "3px"
      }
    }, /*#__PURE__*/React.createElement("h4", {
      style: {
        marginTop: 0,
        marginBottom: "10px",
        color: "#333"
      }
    }, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Enter your bank details", "monoova-payments-for-woocommerce")), /*#__PURE__*/React.createElement("div", {
      style: {
        marginBottom: "10px"
      }
    }, /*#__PURE__*/React.createElement("label", {
      style: {
        display: "block",
        marginBottom: "5px",
        fontWeight: "500"
      }
    }, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Name associated with bank account", "monoova-payments-for-woocommerce"), " *"), /*#__PURE__*/React.createElement("input", {
      type: "text",
      value: accountName,
      onChange: e => setAccountName(e.target.value),
      placeholder: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Enter your name", "monoova-payments-for-woocommerce"),
      style: {
        width: "100%",
        padding: "8px",
        border: "1px solid #ddd",
        borderRadius: "3px",
        borderColor: errors.accountName ? "#e74c3c" : "#ddd"
      }
    }), errors.accountName && /*#__PURE__*/React.createElement("div", {
      style: {
        color: "#e74c3c",
        fontSize: "12px",
        marginTop: "5px"
      }
    }, errors.accountName)), /*#__PURE__*/React.createElement("div", {
      style: {
        display: "flex",
        gap: "10px",
        marginBottom: "10px"
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        flex: 1
      }
    }, /*#__PURE__*/React.createElement("label", {
      style: {
        display: "block",
        marginBottom: "5px",
        fontWeight: "500"
      }
    }, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("BSB", "monoova-payments-for-woocommerce"), " *"), /*#__PURE__*/React.createElement("input", {
      type: "text",
      value: bsb,
      onChange: e => setBsb(e.target.value),
      placeholder: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Enter BSB", "monoova-payments-for-woocommerce"),
      style: {
        width: "100%",
        padding: "8px",
        border: "1px solid #ddd",
        borderRadius: "3px",
        borderColor: errors.bsb ? "#e74c3c" : "#ddd"
      }
    }), errors.bsb && /*#__PURE__*/React.createElement("div", {
      style: {
        color: "#e74c3c",
        fontSize: "12px",
        marginTop: "5px"
      }
    }, errors.bsb)), /*#__PURE__*/React.createElement("div", {
      style: {
        flex: 1
      }
    }, /*#__PURE__*/React.createElement("label", {
      style: {
        display: "block",
        marginBottom: "5px",
        fontWeight: "500"
      }
    }, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Account Number", "monoova-payments-for-woocommerce"), " *"), /*#__PURE__*/React.createElement("input", {
      type: "text",
      value: accountNumber,
      onChange: e => setAccountNumber(e.target.value),
      placeholder: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Enter your account number", "monoova-payments-for-woocommerce"),
      style: {
        width: "100%",
        padding: "8px",
        border: "1px solid #ddd",
        borderRadius: "3px",
        borderColor: errors.accountNumber ? "#e74c3c" : "#ddd"
      }
    }), errors.accountNumber && /*#__PURE__*/React.createElement("div", {
      style: {
        color: "#e74c3c",
        fontSize: "12px",
        marginTop: "5px"
      }
    }, errors.accountNumber)))), /*#__PURE__*/React.createElement("div", {
      style: {
        marginTop: "15px",
        padding: "10px",
        backgroundColor: "#e7f3ff",
        borderRadius: "3px",
        fontSize: "14px"
      }
    }, /*#__PURE__*/React.createElement("strong", null, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Authorise recurring payments", "monoova-payments-for-woocommerce")), /*#__PURE__*/React.createElement("p", {
      style: {
        margin: "5px 0 0 0",
        color: "#666"
      }
    }, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Approve your recurring payment in your online banking or banking app!", "monoova-payments-for-woocommerce"))));
  };

  /**
   * Content component for PayTo
   */
  const Content = function (props) {
    return /*#__PURE__*/React.createElement(PayToForm, props);
  };

  /**
   * Label component with PayTo icon
   */
  const Label = props => {
    return /*#__PURE__*/React.createElement("div", {
      style: {
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center",
        width: "100%"
      }
    }, /*#__PURE__*/React.createElement("span", {
      style: {
        fontWeight: 600
      }
    }, label), /*#__PURE__*/React.createElement("img", {
      src: `${settings.plugin_url || "/wp-content/plugins/monoova-payments-for-woocommerce/"}assets/images/payto-logo.svg`,
      alt: "PayTo logo",
      style: {
        height: "24px",
        width: "auto"
      }
    }));
  };

  /**
   * Monoova PayTo payment method config object
   */
  const MonoovaPayTo = {
    name: "monoova_payto",
    label: /*#__PURE__*/React.createElement(Label, null),
    content: /*#__PURE__*/React.createElement(Content, null),
    edit: /*#__PURE__*/React.createElement(Content, null),
    canMakePayment: function () {
      return true;
    },
    ariaLabel: label,
    supports: {
      features: settings.supports || []
    },
    icons: settings.icons || null
  };

  // Register the payment method
  try {
    (0,_woocommerce_blocks_registry__WEBPACK_IMPORTED_MODULE_0__.registerPaymentMethod)(MonoovaPayTo);
  } catch (error) {
    console.error("Monoova PayTo Block: Failed to register payment method:", error);
  }
}
})();

/******/ })()
;
//# sourceMappingURL=monoova-payto-block.js.map
>>>>>>> dev-PAYTO
