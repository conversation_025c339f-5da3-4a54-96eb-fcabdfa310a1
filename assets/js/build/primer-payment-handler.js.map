{"version": 3, "file": "primer-payment-handler.js", "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,UAASA,CAAC,EAAE;EACT,YAAY;;EAEZ,IAAIC,sBAAsB,GAAG,IAAI;EACjC,IAAIC,oBAAoB,GAAG,KAAK;;EAEhC;EACA,IAAI,OAAOC,kBAAkB,KAAK,WAAW,EAAE;IAC3CC,OAAO,CAACC,KAAK,CAAC,kEAAkE,CAAC;IACjFC,YAAY,CAAC,gFAAgF,CAAC;IAC9F;EACJ;EAEA,MAAM;IAAEC,QAAQ;IAAEC,QAAQ;IAAEC,kBAAkB;IAAEC,sBAAsB;IAAEC,IAAI;IAAEC,gBAAgB;IAAEC;EAAa,CAAC,GAAGV,kBAAkB;;EAEnI;AACJ;AACA;AACA;EACI,SAASG,YAAYA,CAACQ,OAAO,EAAE;IAC3B,MAAMC,QAAQ,GAAGf,CAAC,CAAC,YAAY,CAAC;IAChC,IAAIe,QAAQ,CAACC,MAAM,EAAE;MACjBD,QAAQ,CAACE,QAAQ,CAAC,QAAQ,CAAC,CAACC,IAAI,CAAC,2BAA2BJ,OAAO,SAAS,CAAC;IACjF,CAAC,MAAM;MACHK,KAAK,CAACL,OAAO,CAAC,CAAC,CAAC;IACpB;IACA;IACA,IAAId,CAAC,CAACoB,SAAS,EAAEpB,CAAC,CAACoB,SAAS,CAAC,CAAC;IAC9BpB,CAAC,CAAC,qBAAqB,CAAC,CAACqB,IAAI,CAAC,CAAC;EACnC;;EAEA;AACJ;AACA;EACI,eAAeC,gBAAgBA,CAAA,EAAG;IAC9B,IAAI;MACA,MAAMC,QAAQ,GAAG,MAAMvB,CAAC,CAACwB,IAAI,CAAC;QAC1BC,GAAG,EAAElB,QAAQ;QACbmB,IAAI,EAAE,MAAM;QACZC,IAAI,EAAE;UACFC,MAAM,EAAE,0BAA0B;UAAE;UACpCpB,QAAQ,EAAEA,QAAQ;UAClBqB,QAAQ,EAAEpB,kBAAkB,CAAC;QACjC;MACJ,CAAC,CAAC;MAEF,IAAIc,QAAQ,CAACO,OAAO,IAAIP,QAAQ,CAACI,IAAI,IAAIJ,QAAQ,CAACI,IAAI,CAACI,WAAW,EAAE;QAChE,OAAOR,QAAQ,CAACI,IAAI;MACxB,CAAC,MAAM;QACH,MAAM,IAAIK,KAAK,CAACT,QAAQ,CAACI,IAAI,IAAIJ,QAAQ,CAACI,IAAI,CAACb,OAAO,GAAGS,QAAQ,CAACI,IAAI,CAACb,OAAO,GAAIH,IAAI,CAACsB,oBAAoB,IAAI,qCAAsC,CAAC;MAC1J;IACJ,CAAC,CAAC,OAAO5B,KAAK,EAAE;MACZD,OAAO,CAACC,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,IAAI6B,YAAY,GAAGvB,IAAI,CAACsB,oBAAoB,IAAI,qCAAqC;MACrF,IAAI5B,KAAK,CAAC8B,YAAY,IAAI9B,KAAK,CAAC8B,YAAY,CAACR,IAAI,IAAItB,KAAK,CAAC8B,YAAY,CAACR,IAAI,CAACb,OAAO,EAAE;QAClFoB,YAAY,GAAG7B,KAAK,CAAC8B,YAAY,CAACR,IAAI,CAACb,OAAO;MAClD,CAAC,MAAM,IAAIT,KAAK,CAACS,OAAO,EAAE;QACtBoB,YAAY,GAAG7B,KAAK,CAACS,OAAO;MAChC;MACAR,YAAY,CAAC4B,YAAY,CAAC;MAC1B;MACAE,UAAU,CAAC,MAAM;QACbC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG1B,YAAY,IAAI,GAAG;MAC9C,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;MACV,MAAMR,KAAK,CAAC,CAAC;IACjB;EACJ;;EAEA;AACJ;AACA;AACA;EACI,eAAemC,wBAAwBA,CAACC,SAAS,EAAE;IAC/C,IAAIvC,oBAAoB,EAAE;MACtB;IACJ;IACAA,oBAAoB,GAAG,IAAI;IAE3B,IAAI,CAACuC,SAAS,IAAI,CAACA,SAAS,CAACV,WAAW,EAAE;MACtCzB,YAAY,CAACK,IAAI,CAAC+B,mBAAmB,IAAI,yCAAyC,CAAC;MACnFxC,oBAAoB,GAAG,KAAK;MAC5B;IACJ;IAEA,IAAID,sBAAsB,EAAE;MACxB,IAAI;QACA,MAAMA,sBAAsB,CAAC0C,OAAO,CAAC,CAAC;MAC1C,CAAC,CAAC,OAAOtC,KAAK,EAAE;QACZD,OAAO,CAACwC,IAAI,CAAC,4CAA4C,EAAEvC,KAAK,CAAC;MACrE;MACAJ,sBAAsB,GAAG,IAAI;IACjC;;IAEA;IACA,MAAM4C,OAAO,GAAG;MACZC,SAAS,EAAE,qBAAqB;MAChCC,2BAA2B,EAAE,KAAK;MAAE;MACpC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAC,KAAK,EAAE;QACHC,UAAU,EAAE;UACRC,UAAU,EAAEtC,gBAAgB,EAAEuC,kBAAkB,EAAEC,WAAW,EAAEC,WAAW,IAAI,8BAA8B;UAC5GC,QAAQ,EAAE1C,gBAAgB,EAAEuC,kBAAkB,EAAEC,WAAW,EAAEG,SAAS,IAAI,MAAM;UAChFC,UAAU,EAAE5C,gBAAgB,EAAEuC,kBAAkB,EAAEC,WAAW,EAAEK,WAAW,IAAI,QAAQ;UACtFC,KAAK,EAAE9C,gBAAgB,EAAEuC,kBAAkB,EAAEC,WAAW,EAAEM,KAAK,IAAI;QACvE,CAAC;QACDC,KAAK,EAAE;UACHC,IAAI,EAAE;YACFV,UAAU,EAAEtC,gBAAgB,EAAEuC,kBAAkB,EAAEQ,KAAK,EAAEN,WAAW,IAAI,8BAA8B;YACtGC,QAAQ,EAAE1C,gBAAgB,EAAEuC,kBAAkB,EAAEQ,KAAK,EAAEJ,SAAS,IAAI,MAAM;YAC1EC,UAAU,EAAE5C,gBAAgB,EAAEuC,kBAAkB,EAAEQ,KAAK,EAAEF,WAAW,IAAI,QAAQ;YAChFI,UAAU,EAAEjD,gBAAgB,EAAEuC,kBAAkB,EAAEQ,KAAK,EAAEG,gBAAgB,IAAI,SAAS;YACtFC,WAAW,EAAEnD,gBAAgB,EAAEuC,kBAAkB,EAAEQ,KAAK,EAAEK,YAAY,IAAI,SAAS;YACnFC,YAAY,EAAErD,gBAAgB,EAAEuC,kBAAkB,EAAEQ,KAAK,EAAEO,aAAa,IAAI,KAAK;YACjFR,KAAK,EAAE9C,gBAAgB,EAAEuC,kBAAkB,EAAEQ,KAAK,EAAEQ,UAAU,IAAI;UACtE;QACJ,CAAC;QACDC,YAAY,EAAE;UACVR,IAAI,EAAE;YACFF,KAAK,EAAE9C,gBAAgB,EAAEuC,kBAAkB,EAAEkB,aAAa,EAAEF,UAAU,IAAI,SAAS;YACnFN,UAAU,EAAEjD,gBAAgB,EAAEuC,kBAAkB,EAAEkB,aAAa,EAAER,UAAU,IAAI,SAAS;YACxFI,YAAY,EAAErD,gBAAgB,EAAEuC,kBAAkB,EAAEkB,aAAa,EAAEH,aAAa,IAAI,MAAM;YAC1FH,WAAW,EAAEnD,gBAAgB,EAAEuC,kBAAkB,EAAEkB,aAAa,EAAEL,YAAY,IAAI,SAAS;YAC3Fd,UAAU,EAAEtC,gBAAgB,EAAEuC,kBAAkB,EAAEkB,aAAa,EAAEhB,WAAW,IAAI,8BAA8B;YAC9GC,QAAQ,EAAE1C,gBAAgB,EAAEuC,kBAAkB,EAAEkB,aAAa,EAAEd,SAAS,IAAI,MAAM;YAClFC,UAAU,EAAE5C,gBAAgB,EAAEuC,kBAAkB,EAAEkB,aAAa,EAAEZ,WAAW,IAAI,MAAM;YACtFa,SAAS,EAAE;UACf,CAAC;UACDC,QAAQ,EAAE;YACNb,KAAK,EAAE,SAAS;YAChBG,UAAU,EAAE;UAChB;QACJ,CAAC;QACDW,aAAa,EAAE;UACXd,KAAK,EAAE9C,gBAAgB,EAAEuC,kBAAkB,EAAEkB,aAAa,EAAER,UAAU,IAAI;QAC9E;MACJ,CAAC;MACD3B,YAAY,EAAE;QACVqC,QAAQ,EAAE,IAAI;QAAE;QAChBE,kBAAkBA,CAAC3D,OAAO,EAAE;UACxBR,YAAY,CAACQ,OAAO,CAAC;QACzB,CAAC;QACD4D,kBAAkBA,CAAA,EAAG;UACjB,MAAM3D,QAAQ,GAAGf,CAAC,CAAC,YAAY,CAAC;UAChC,IAAIe,QAAQ,CAACC,MAAM,EAAE;YACjBD,QAAQ,CAAC4D,WAAW,CAAC,QAAQ,CAAC,CAACC,KAAK,CAAC,CAAC;UAC1C;QACJ;MACJ,CAAC;MACDC,aAAa,EAAE,KAAK;MAAE;MACtBC,kBAAkB,EAAE,MAAOnD,IAAI,IAAK;QAChC;QACA,MAAMoD,eAAe,GAAGpD,IAAI,EAAEqD,OAAO,EAAEC,EAAE;;QAEzC;QACA,IAAI;UACA,MAAMC,aAAa,GAAG,MAAMlF,CAAC,CAACwB,IAAI,CAAC;YAC/BC,GAAG,EAAElB,QAAQ;YACbmB,IAAI,EAAE,MAAM;YACZC,IAAI,EAAE;cACFC,MAAM,EAAE,2BAA2B;cAAE;cACrCpB,QAAQ,EAAEA,QAAQ;cAClB2E,iBAAiB,EAAEJ,eAAe;cAAE;cACpCK,sBAAsB,EAAE3C,SAAS,CAAC4C,gCAAgC;cAAE;cACpExD,QAAQ,EAAEnB,sBAAsB,CAAC;YACrC;UACJ,CAAC,CAAC;UAEF,IAAIwE,aAAa,CAACpD,OAAO,IAAIoD,aAAa,CAACvD,IAAI,IAAIuD,aAAa,CAACvD,IAAI,CAAC2D,YAAY,EAAE;YAChFjD,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG2C,aAAa,CAACvD,IAAI,CAAC2D,YAAY;UAC1D,CAAC,MAAM;YACH,MAAM,IAAItD,KAAK,CAACkD,aAAa,CAACvD,IAAI,IAAIuD,aAAa,CAACvD,IAAI,CAACb,OAAO,GAAGoE,aAAa,CAACvD,IAAI,CAACb,OAAO,GAAIH,IAAI,CAAC4E,sBAAsB,IAAI,gCAAiC,CAAC;UACtK;QACJ,CAAC,CAAC,OAAOlF,KAAK,EAAE;UACZD,OAAO,CAACC,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;UAC9D,IAAI6B,YAAY,GAAGvB,IAAI,CAAC4E,sBAAsB,IAAI,gCAAgC;UACjF,IAAIlF,KAAK,CAAC8B,YAAY,IAAI9B,KAAK,CAAC8B,YAAY,CAACR,IAAI,IAAItB,KAAK,CAAC8B,YAAY,CAACR,IAAI,CAACb,OAAO,EAAE;YACnFoB,YAAY,GAAG7B,KAAK,CAAC8B,YAAY,CAACR,IAAI,CAACb,OAAO;UAClD,CAAC,MAAM,IAAIT,KAAK,CAACS,OAAO,EAAE;YACtBoB,YAAY,GAAG7B,KAAK,CAACS,OAAO;UAChC;UACAR,YAAY,CAAC4B,YAAY,CAAC;QAC9B;MACJ,CAAC;MACDsD,cAAc,EAAEA,CAACnF,KAAK,EAAE;QAAE2E;MAAQ,CAAC,EAAES,OAAO,KAAK;QAC7CrF,OAAO,CAACC,KAAK,CAAC,wBAAwB,EAAEA,KAAK,EAAE2E,OAAO,CAAC;QACvD,IAAI9C,YAAY,GAAGvB,IAAI,CAAC+E,wBAAwB,IAAI,8DAA8D;QAClH,IAAIrF,KAAK,IAAIA,KAAK,CAACS,OAAO,EAAE;UACxBoB,YAAY,GAAG7B,KAAK,CAACS,OAAO;QAChC,CAAC,MAAM,IAAIkE,OAAO,IAAIA,OAAO,CAACW,SAAS,IAAIX,OAAO,CAACW,SAAS,CAAC7E,OAAO,EAAE;UAClEoB,YAAY,GAAG8C,OAAO,CAACW,SAAS,CAAC7E,OAAO;QAC5C,CAAC,MAAM,IAAI,OAAOT,KAAK,KAAK,QAAQ,EAAE;UAClC6B,YAAY,GAAG7B,KAAK;QACxB;QACAC,YAAY,CAAC4B,YAAY,CAAC;QAC1B;QACAhC,oBAAoB,GAAG,KAAK;MAChC,CAAC;MACD;MACA0F,qBAAqB,EAAEA,CAAA,KAAMxF,OAAO,CAACyF,GAAG,CAAC,+BAA+B,CAAC;MACzEC,eAAe,EAAEA,CAAA,KAAM1F,OAAO,CAACyF,GAAG,CAAC,yBAAyB,CAAC;MAC7DE,gBAAgB,EAAEA,CAAA,KAAM;QACpB3F,OAAO,CAACyF,GAAG,CAAC,0BAA0B,CAAC;QACvCvF,YAAY,CAACK,IAAI,CAACqF,kBAAkB,IAAI,yBAAyB,CAAC;QAClE;QACA;QACA9F,oBAAoB,GAAG,KAAK;MAChC,CAAC;MACD+F,mBAAmB,EAAEA,CAAA,KAAM7F,OAAO,CAACyF,GAAG,CAAC,6BAA6B,CAAC;MACrEK,mBAAmB,EAAEA,CAAA,KAAM9F,OAAO,CAACyF,GAAG,CAAC,6BAA6B,CAAC;MACrEM,sBAAsB,EAAGxE,IAAI,IAAKvB,OAAO,CAACyF,GAAG,CAAC,gCAAgC,EAAElE,IAAI,CAAC;MACrFyE,qBAAqB,EAAGzE,IAAI,IAAK;QAC7BvB,OAAO,CAACyF,GAAG,CAAC,+BAA+B,EAAElE,IAAI,CAAC;QAClDrB,YAAY,CAACK,IAAI,CAAC0F,WAAW,IAAI,+BAA+B,CAAC;MACrE,CAAC;MACDC,iBAAiB,EAAG3E,IAAI,IAAKvB,OAAO,CAACyF,GAAG,CAAC,+DAA+D,EAAElE,IAAI,CAAC;MAC/G4E,eAAe,EAAG5E,IAAI,IAAK;QACvBvB,OAAO,CAACyF,GAAG,CAAC,yBAAyB,EAAElE,IAAI,CAAC;QAC5CrB,YAAY,CAACK,IAAI,CAAC6F,cAAc,IAAI,iBAAiB,CAAC;MAC1D;IACJ,CAAC;IAED,IAAI;MACAxG,CAAC,CAAC,qBAAqB,CAAC,CAACyG,IAAI,CAAC,CAAC,CAAC,CAAC;MACjCxG,sBAAsB,GAAG,MAAMyG,MAAM,CAACC,qBAAqB,CAAClE,SAAS,CAACV,WAAW,EAAEc,OAAO,CAAC;IAC/F,CAAC,CAAC,OAAOxC,KAAK,EAAE;MACZD,OAAO,CAACC,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;MACrEC,YAAY,CAAC,CAACK,IAAI,CAACiG,uBAAuB,IAAI,qCAAqC,KAAKvG,KAAK,CAACS,OAAO,IAAI,EAAE,CAAC,CAAC;IACjH;IACAZ,oBAAoB,GAAG,KAAK;EAChC;;EAEA;EACAF,CAAC,CAAC,kBAAiB;IACf;IACAA,CAAC,CAAC,0BAA0B,CAAC,CAACqB,IAAI,CAAC,CAAC;IACpC;;IAEA,IAAI,CAACb,QAAQ,EAAE;MACXF,YAAY,CAACK,IAAI,CAACkG,sBAAsB,IAAI,mDAAmD,CAAC;MAChG;IACJ;IACA,IAAI,CAACpG,kBAAkB,EAAE;MACrBH,YAAY,CAACK,IAAI,CAACmG,mBAAmB,IAAI,4CAA4C,CAAC;MACtF;IACJ;IAEA,IAAI;MACA,MAAMrE,SAAS,GAAG,MAAMnB,gBAAgB,CAAC,CAAC;MAC1C,IAAImB,SAAS,IAAIA,SAAS,CAACV,WAAW,EAAE;QACpCS,wBAAwB,CAACC,SAAS,CAAC;MACvC;IACJ,CAAC,CAAC,OAAOpC,KAAK,EAAE;MACZ;MACAD,OAAO,CAACC,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;IACjE;EACJ,CAAC,CAAC;AAEN,CAAC,EAAE0G,MAAM,CAAC,C", "sources": ["webpack://monoova-payments-for-woocommerce/./assets/js/src/primer-payment-handler.js"], "sourcesContent": ["/**\n * Primer Payment Handler for Monoova Payments Redirect Flow\n *\n * This script handles:\n * 1. Fetching the client session token via AJAX.\n * 2. Initializing the Primer Universal Checkout on the redirect page.\n * 3. Handling payment completion and redirecting back to WooCommerce.\n * 4. Dynamically configuring Primer options based on gateway admin settings.\n *\n * Gateway Settings Integration:\n * - saved_cards: Controls customer vault/tokenization\n * - enable_apple_pay: Shows/hides Apple Pay button\n * - enable_google_pay: Shows/hides Google Pay button  \n * - capture: Controls immediate capture vs authorization\n */\n(function($) {\n    'use strict';\n\n    let primerCheckoutInstance = null;\n    let isInitializingPrimer = false;\n\n    // Check if primerRedirectData is available (localized from PHP)\n    if (typeof primerRedirectData === 'undefined') {\n        console.error('Primer Redirect Data not found. AJAX URL or Order ID is missing.');\n        displayError('Critical error: Payment page configuration is missing. Please contact support.');\n        return;\n    }\n\n    const { ajax_url, order_id, client_token_nonce, complete_payment_nonce, i18n, gateway_settings, checkout_url } = primerRedirectData;\n\n    /**\n     * Displays an error message on the page.\n     * @param {string} message The error message to display.\n     */\n    function displayError(message) {\n        const errorBox = $('#error-box');\n        if (errorBox.length) {\n            errorBox.addClass('active').html(`<span class=\"ErrorText\">${message}</span>`);\n        } else {\n            alert(message); // Fallback if error-box is not present\n        }\n        // Unblock UI if it was blocked\n        if ($.unblockUI) $.unblockUI(); \n        $('#checkout-container').hide();\n    }\n\n    /**\n     * Fetches the client token from the server.\n     */\n    async function fetchClientToken() {\n        try {\n            const response = await $.ajax({\n                url: ajax_url,\n                type: 'POST',\n                data: {\n                    action: 'monoova_get_client_token', // Action hook in class-monoova-card-gateway.php\n                    order_id: order_id,\n                    _wpnonce: client_token_nonce // Nonce for fetching client token\n                }\n            });\n\n            if (response.success && response.data && response.data.clientToken) {\n                return response.data;\n            } else {\n                throw new Error(response.data && response.data.message ? response.data.message : (i18n.error_fetching_token || 'Could not retrieve payment session.'));\n            }\n        } catch (error) {\n            console.error('Error fetching client token:', error);\n            let errorMessage = i18n.error_fetching_token || 'Could not retrieve payment session.';\n            if (error.responseJSON && error.responseJSON.data && error.responseJSON.data.message) {\n                errorMessage = error.responseJSON.data.message;\n            } else if (error.message) {\n                errorMessage = error.message;\n            }\n            displayError(errorMessage);\n            // Redirect back to checkout page after a short delay\n            setTimeout(() => {\n                window.location.href = checkout_url || '/';\n            }, 3000); // 3-second delay to allow user to read the message\n            throw error; // Re-throw to stop further execution\n        }\n    }\n\n    /**\n     * Initializes the Primer Universal Checkout.\n     * @param {object} tokenData - The token data object containing clientToken, clientTransactionUniqueReference, etc.\n     */\n    async function initializePrimerCheckout(tokenData) {\n        if (isInitializingPrimer) {\n            return;\n        }\n        isInitializingPrimer = true;\n\n        if (!tokenData || !tokenData.clientToken) {\n            displayError(i18n.error_invalid_token || 'Invalid payment session token provided.');\n            isInitializingPrimer = false;\n            return;\n        }\n\n        if (primerCheckoutInstance) {\n            try {\n                await primerCheckoutInstance.destroy();\n            } catch (error) {\n                console.warn('Error destroying existing Primer instance:', error);\n            }\n            primerCheckoutInstance = null;\n        }\n\n        // TODO: read Primer SDK docs to add/modify options\n        const options = {\n            container: '#checkout-container',\n            clientSessionCachingEnabled: false, // Keep this consistent with original settings\n            // // Configure payment methods based on admin settings\n            //     // Enable/disable Apple Pay based on admin setting\n            // applePay: gateway_settings?.enable_apple_pay ? {\n            //     buttonType: 'plain',\n            //     buttonStyle: 'black'\n            // } : null,\n            // // Enable/disable Google Pay based on admin setting\n            // googlePay: gateway_settings?.enable_google_pay ? {\n            //     buttonType: 'plain'\n            // } : null,\n            // // Configure customer vault (saved cards) based on admin setting\n            // customer: gateway_settings?.saved_cards ? {\n            //     vaultOnSuccess: true\n            // } : {\n            //     vaultOnSuccess: false\n            // },\n            // // Configure capture behavior based on admin setting\n            // clientSession: {\n            //     captureVaultedCardCvv: gateway_settings?.capture !== false\n            // },\n            // submitButton: {\n            //     amountVisible: true, // Show amount in the checkout\n            // },\n            style: {\n                inputLabel: {\n                    fontFamily: gateway_settings?.checkout_ui_styles?.input_label?.font_family || 'Helvetica, Arial, sans-serif',\n                    fontSize: gateway_settings?.checkout_ui_styles?.input_label?.font_size || '14px',\n                    fontWeight: gateway_settings?.checkout_ui_styles?.input_label?.font_weight || 'normal',\n                    color: gateway_settings?.checkout_ui_styles?.input_label?.color || '#000000',\n                },\n                input: {\n                    base: {\n                        fontFamily: gateway_settings?.checkout_ui_styles?.input?.font_family || 'Helvetica, Arial, sans-serif',\n                        fontSize: gateway_settings?.checkout_ui_styles?.input?.font_size || '14px',\n                        fontWeight: gateway_settings?.checkout_ui_styles?.input?.font_weight || 'normal',\n                        background: gateway_settings?.checkout_ui_styles?.input?.background_color || '#FAFAFA',\n                        borderColor: gateway_settings?.checkout_ui_styles?.input?.border_color || '#E8E8E8',\n                        borderRadius: gateway_settings?.checkout_ui_styles?.input?.border_radius || '8px',\n                        color: gateway_settings?.checkout_ui_styles?.input?.text_color || '#000000',\n                    },\n                },\n                submitButton: {\n                    base: {\n                        color: gateway_settings?.checkout_ui_styles?.submit_button?.text_color || '#000000',\n                        background: gateway_settings?.checkout_ui_styles?.submit_button?.background || '#2ab5c4',\n                        borderRadius: gateway_settings?.checkout_ui_styles?.submit_button?.border_radius || '10px',\n                        borderColor: gateway_settings?.checkout_ui_styles?.submit_button?.border_color || '#2ab5c4',\n                        fontFamily: gateway_settings?.checkout_ui_styles?.submit_button?.font_family || 'Helvetica, Arial, sans-serif',\n                        fontSize: gateway_settings?.checkout_ui_styles?.submit_button?.font_size || '17px',\n                        fontWeight: gateway_settings?.checkout_ui_styles?.submit_button?.font_weight || 'bold',\n                        boxShadow: 'none'\n                    },\n                    disabled: {\n                        color: '#9b9b9b',\n                        background: '#e1deda'\n                    }\n                },\n                loadingScreen: {\n                    color: gateway_settings?.checkout_ui_styles?.submit_button?.background || '#2ab5c4'\n                }\n            },\n            errorMessage: {\n                disabled: true, // We handle errors with displayError\n                onErrorMessageShow(message) {\n                    displayError(message);\n                },\n                onErrorMessageHide() {\n                    const errorBox = $('#error-box');\n                    if (errorBox.length) {\n                        errorBox.removeClass('active').empty();\n                    }\n                }\n            },\n            successScreen: false, // Disable success screen since we handle redirects manually\n            onCheckoutComplete: async (data) => {\n                // Extract the Primer payment ID or token\n                const primerPaymentId = data?.payment?.id;\n\n                // Payment successful according to Primer UI, now verify and complete with WooCommerce backend\n                try {\n                    const paymentResult = await $.ajax({\n                        url: ajax_url,\n                        type: 'POST',\n                        data: {\n                            action: 'monoova_complete_checkout', // New AJAX action to handle completion\n                            order_id: order_id,\n                            primer_payment_id: primerPaymentId, // Send Primer's payment ID/token\n                            client_transaction_ref: tokenData.clientTransactionUniqueReference, // Send the original client ref\n                            _wpnonce: complete_payment_nonce // Nonce for completing payment\n                        }\n                    });\n\n                    if (paymentResult.success && paymentResult.data && paymentResult.data.redirect_url) {\n                        window.location.href = paymentResult.data.redirect_url;\n                    } else {\n                        throw new Error(paymentResult.data && paymentResult.data.message ? paymentResult.data.message : (i18n.error_finalizing_order || 'Could not finalize your order.'));\n                    }\n                } catch (error) {\n                    console.error('Error completing payment with backend:', error);\n                    let errorMessage = i18n.error_finalizing_order || 'Could not finalize your order.';\n                     if (error.responseJSON && error.responseJSON.data && error.responseJSON.data.message) {\n                        errorMessage = error.responseJSON.data.message;\n                    } else if (error.message) {\n                        errorMessage = error.message;\n                    }\n                    displayError(errorMessage);\n                }\n            },\n            onCheckoutFail: (error, { payment }, handler) => {\n                console.error('Primer onCheckoutFail:', error, payment);\n                let errorMessage = i18n.payment_failed_try_again || 'Your payment could not be processed. Please try again later.';\n                if (error && error.message) {\n                    errorMessage = error.message;\n                } else if (payment && payment.processor && payment.processor.message) {\n                    errorMessage = payment.processor.message;\n                } else if (typeof error === 'string') {\n                    errorMessage = error;\n                }\n                displayError(errorMessage);\n                // Optionally, could call handler.showErrorMessage(errorMessage) if not using custom display\n                isInitializingPrimer = false;\n            },\n            // Optional: Add other lifecycle callbacks from client.js if needed\n            onBeforeCheckoutStart: () => console.log('Primer: onBeforeCheckoutStart'),\n            onCheckoutStart: () => console.log('Primer: onCheckoutStart'),\n            onCheckoutCancel: () => {\n                console.log('Primer: onCheckoutCancel');\n                displayError(i18n.checkout_cancelled || 'Checkout was cancelled.');\n                // Optionally redirect to cart or previous page\n                // window.location.href = primerRedirectData.cart_url || '/'; \n                isInitializingPrimer = false;\n            },\n            onPaymentMethodShow: () => console.log('Primer: onPaymentMethodShow'),\n            onPaymentMethodHide: () => console.log('Primer: onPaymentMethodHide'),\n            onAuthorizationSuccess: (data) => console.log('Primer: onAuthorizationSuccess', data),\n            onAuthorizationFailed: (data) => {\n                console.log('Primer: onAuthorizationFailed', data);\n                displayError(i18n.auth_failed || 'Payment authorization failed.');\n            },\n            onPaymentComplete: (data) => console.log('Primer: onPaymentComplete (different from onCheckoutComplete)', data),\n            onPaymentFailed: (data) => {\n                console.log('Primer: onPaymentFailed', data);\n                displayError(i18n.payment_failed || 'Payment failed.');\n            }\n        };\n\n        try {\n            $('#checkout-container').show(); // Ensure container is visible before showing checkout\n            primerCheckoutInstance = await Primer.showUniversalCheckout(tokenData.clientToken, options);\n        } catch (error) {\n            console.error('Error initializing Primer Universal Checkout:', error);\n            displayError((i18n.error_init_payment_form || 'Failed to initialize payment form: ') + (error.message || ''));\n        }\n        isInitializingPrimer = false;\n    }\n\n    // Main execution flow on document ready (modern syntax)\n    $(async function() {\n        // Hide loading message and show checkout container once JS is ready to fetch token\n        $('#loading-payment-message').hide();\n        // $('#checkout-container').show(); // This will be shown after successful token fetch before Primer init\n\n        if (!order_id) {\n            displayError(i18n.error_missing_order_id || 'Order ID is missing. Cannot proceed with payment.');\n            return;\n        }\n        if (!client_token_nonce) {\n            displayError(i18n.error_missing_nonce || 'Security token is missing. Cannot proceed.');\n            return;\n        }\n\n        try {\n            const tokenData = await fetchClientToken();\n            if (tokenData && tokenData.clientToken) {\n                initializePrimerCheckout(tokenData);\n            }\n        } catch (error) {\n            // Error already displayed by fetchClientToken or initializePrimerCheckout\n            console.error('Failed to initialize payment process:', error);\n        }\n    });\n\n})(jQuery);"], "names": ["$", "primerCheckoutInstance", "isInitializingPrimer", "primerRedirectData", "console", "error", "displayError", "ajax_url", "order_id", "client_token_nonce", "complete_payment_nonce", "i18n", "gateway_settings", "checkout_url", "message", "errorBox", "length", "addClass", "html", "alert", "unblockUI", "hide", "fetchClientToken", "response", "ajax", "url", "type", "data", "action", "_wpnonce", "success", "clientToken", "Error", "error_fetching_token", "errorMessage", "responseJSON", "setTimeout", "window", "location", "href", "initializePrimerCheckout", "tokenData", "error_invalid_token", "destroy", "warn", "options", "container", "clientSessionCachingEnabled", "style", "inputLabel", "fontFamily", "checkout_ui_styles", "input_label", "font_family", "fontSize", "font_size", "fontWeight", "font_weight", "color", "input", "base", "background", "background_color", "borderColor", "border_color", "borderRadius", "border_radius", "text_color", "submitButton", "submit_button", "boxShadow", "disabled", "loadingScreen", "onErrorMessageShow", "onErrorMessageHide", "removeClass", "empty", "successScreen", "onCheckoutComplete", "primerPaymentId", "payment", "id", "paymentResult", "primer_payment_id", "client_transaction_ref", "clientTransactionUniqueReference", "redirect_url", "error_finalizing_order", "onCheckoutFail", "handler", "payment_failed_try_again", "processor", "onBeforeCheckoutStart", "log", "onCheckoutStart", "onCheckoutCancel", "checkout_cancelled", "onPaymentMethodShow", "onPaymentMethodHide", "onAuthorizationSuccess", "onAuthorizationFailed", "auth_failed", "onPaymentComplete", "onPaymentFailed", "payment_failed", "show", "Primer", "showUniversalCheckout", "error_init_payment_form", "error_missing_order_id", "error_missing_nonce", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}