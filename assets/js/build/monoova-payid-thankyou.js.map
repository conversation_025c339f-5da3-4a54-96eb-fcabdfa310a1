{"version": 3, "file": "monoova-payid-thankyou.js", "mappings": ";;;;AAAAA,QAAQ,CAACC,gBAAgB,CAAC,kBAAkB,EAAE,YAAY;EACtD,MAAMC,SAAS,GAAGF,QAAQ,CAACG,cAAc,CAAC,8BAA8B,CAAC;EACzE,IAAI,CAACD,SAAS,EAAE;;EAEhB;EACAA,SAAS,CAACD,gBAAgB,CAAC,OAAO,EAAE,UAAUG,CAAC,EAAE;IAC7C,MAAMC,MAAM,GAAGD,CAAC,CAACE,MAAM,CAACC,OAAO,CAAC,sBAAsB,CAAC;IACvD,IAAI,CAACF,MAAM,EAAE;IAEb,MAAMG,QAAQ,GAAGH,MAAM,CAACI,OAAO,CAACD,QAAQ;IACxC,MAAME,aAAa,GAAGR,SAAS,CAACS,aAAa,CAAC,8BAA8BH,QAAQ,IAAI,CAAC;IACzF,IAAI,CAACE,aAAa,EAAE;IAEpB,MAAME,UAAU,GAAGF,aAAa,CAACG,SAAS,CAACC,IAAI,CAAC,CAAC;IACjDC,SAAS,CAACC,SAAS,CACdC,SAAS,CAACL,UAAU,CAAC,CACrBM,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CACdC,KAAK,CAACC,GAAG,IAAIC,OAAO,CAACC,KAAK,CAAC,kBAAkB,EAAEF,GAAG,CAAC,CAAC;EAC7D,CAAC,CAAC;;EAEF;EACA,MAAMG,eAAe,GAAGvB,QAAQ,CAACG,cAAc,CAAC,iBAAiB,CAAC;EAClE,IAAIoB,eAAe,EAAE;IACjB,MAAMC,SAAS,GAAGD,eAAe,CAACE,YAAY,CAAC,cAAc,CAAC;IAC9D,IAAID,SAAS,IAAI,OAAOE,MAAM,KAAK,WAAW,EAAE;MAC5C;MACAH,eAAe,CAACI,SAAS,GAAG,EAAE;MAC9B,IAAID,MAAM,CAACH,eAAe,EAAE;QACxBK,IAAI,EAAEJ,SAAS;QACfK,KAAK,EAAE,GAAG;QACVC,MAAM,EAAE,GAAG;QACXC,SAAS,EAAE,SAAS;QACpBC,UAAU,EAAE,SAAS;QACrBC,YAAY,EAAEP,MAAM,CAACQ,YAAY,CAACC;MACtC,CAAC,CAAC;IACN;EACJ;;EAEA;EACA,MAAMC,QAAQ,GAAGpC,QAAQ,CAACG,cAAc,CAAC,yBAAyB,CAAC;EACnE,IAAIiC,QAAQ,EAAE;IACV,MAAMC,SAAS,GAAGrC,QAAQ,CAACG,cAAc,CAAC,oBAAoB,CAAC;IAC/D,MAAMmC,QAAQ,GAAGtC,QAAQ,CAACG,cAAc,CAAC,mBAAmB,CAAC;IAC7DiC,QAAQ,CAACnC,gBAAgB,CAAC,QAAQ,EAAE,YAAY;MAC5C,IAAI,IAAI,CAACsC,KAAK,KAAK,OAAO,EAAE;QACxBF,SAAS,CAACG,KAAK,CAACC,OAAO,GAAG,OAAO;QACjCH,QAAQ,CAACE,KAAK,CAACC,OAAO,GAAG,MAAM;MACnC,CAAC,MAAM;QACHJ,SAAS,CAACG,KAAK,CAACC,OAAO,GAAG,MAAM;QAChCH,QAAQ,CAACE,KAAK,CAACC,OAAO,GAAG,OAAO;MACpC;IACJ,CAAC,CAAC;EACN;;EAEA;EACA,MAAMC,OAAO,GAAGxC,SAAS,CAACO,OAAO,CAACiC,OAAO;EACzC,MAAMC,QAAQ,GAAGzC,SAAS,CAACO,OAAO,CAACkC,QAAQ;EAC3C,MAAMC,KAAK,GAAG1C,SAAS,CAACO,OAAO,CAACmC,KAAK;EACrC,MAAMC,OAAO,GAAG3C,SAAS,CAACO,OAAO,CAACoC,OAAO;EACzC,MAAMC,eAAe,GAAG5C,SAAS,CAACO,OAAO,CAACqC,eAAe;EAEzD,MAAMC,WAAW,GAAG/C,QAAQ,CAACG,cAAc,CAAC,yBAAyB,CAAC;EACtE,MAAM6C,aAAa,GAAGhD,QAAQ,CAACG,cAAc,CAAC,2BAA2B,CAAC;EAC1E,MAAM8C,WAAW,GAAGjD,QAAQ,CAACG,cAAc,CAAC,yBAAyB,CAAC;EACtE,MAAM+C,YAAY,GAAGlD,QAAQ,CAACG,cAAc,CAAC,0BAA0B,CAAC;EACxE,MAAMgD,iBAAiB,GAAGnD,QAAQ,CAACG,cAAc,CAAC,0BAA0B,CAAC;EAC7E,MAAMiD,UAAU,GAAGpD,QAAQ,CAACG,cAAc,CAAC,qBAAqB,CAAC;EAEjE,IAAIkD,eAAe,EAAEC,iBAAiB;EAEtC,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACtB,IAAIF,eAAe,EAAE;MACjBG,aAAa,CAACH,eAAe,CAAC;IAClC;EACJ,CAAC;EAED,MAAMI,gBAAgB,GAAGA,CAACC,MAAM,EAAEC,MAAM,GAAG,EAAE,KAAK;IAC9CJ,WAAW,CAAC,CAAC;IACbR,WAAW,CAACP,KAAK,CAACC,OAAO,GAAG,MAAM;IAClCO,aAAa,CAACR,KAAK,CAACC,OAAO,GAAG,MAAM;IACpCS,YAAY,CAACV,KAAK,CAACC,OAAO,GAAG,MAAM;IACnCQ,WAAW,CAACT,KAAK,CAACC,OAAO,GAAG,MAAM;IAElC,QAAQiB,MAAM;MACV,KAAK,MAAM;QACPV,aAAa,CAACR,KAAK,CAACC,OAAO,GAAG,OAAO;QACrC;MACJ,KAAK,QAAQ;QACT,IAAIU,iBAAiB,IAAIQ,MAAM,EAAE;UAC7B,IAAIA,MAAM,CAACC,MAAM,KAAK,CAAC,EAAE;YACrBD,MAAM,GAAG,0BAA0B;UACvC;UACAR,iBAAiB,CAACtC,SAAS,GAAG,WAAW8C,MAAM,EAAE;QACrD;QACAT,YAAY,CAACV,KAAK,CAACC,OAAO,GAAG,OAAO;QACpC;MACJ,KAAK,WAAW;MAChB,KAAK,SAAS;QACVQ,WAAW,CAACT,KAAK,CAACC,OAAO,GAAG,OAAO;QACnC;MACJ;QACI;QACAM,WAAW,CAACP,KAAK,CAACC,OAAO,GAAG,OAAO;QACnC;IACR;EACJ,CAAC;EAED,MAAMoB,WAAW,GAAGA,CAAA,KAAM;IACtB,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAE,8BAA8B,CAAC;IACzDF,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEtB,OAAO,CAAC;IACpCoB,QAAQ,CAACE,MAAM,CAAC,WAAW,EAAErB,QAAQ,CAAC;IACtCmB,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEpB,KAAK,CAAC;IAE/BqB,KAAK,CAACpB,OAAO,EAAE;MACXqB,MAAM,EAAE,MAAM;MACdC,IAAI,EAAEL;IACV,CAAC,CAAC,CACG5C,IAAI,CAACkD,QAAQ,IAAIA,QAAQ,CAACC,IAAI,CAAC,CAAC,CAAC,CACjCnD,IAAI,CAACoD,IAAI,IAAI;MACV,IAAIA,IAAI,CAACC,OAAO,IAAID,IAAI,CAACA,IAAI,CAACZ,MAAM,KAAK,SAAS,EAAE;QAChDD,gBAAgB,CAACa,IAAI,CAACA,IAAI,CAACZ,MAAM,EAAEY,IAAI,CAACA,IAAI,CAACX,MAAM,CAAC;MACxD;IACJ,CAAC,CAAC,CACDxC,KAAK,CAACG,KAAK,IAAID,OAAO,CAACC,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC,CAAC;EAClF,CAAC;EAED,MAAMkD,YAAY,GAAGA,CAAA,KAAM;IACvB;IACAX,WAAW,CAAC,CAAC;IACb;IACAR,eAAe,GAAGoB,WAAW,CAACZ,WAAW,EAAE,KAAK,CAAC;EACrD,CAAC;EAED,MAAMa,cAAc,GAAGA,CAAA,KAAM;IACzB,IAAI,CAAC5B,eAAe,IAAIA,eAAe,IAAI,CAAC,IAAI,CAACM,UAAU,EAAE;IAC7DE,iBAAiB,GAAGmB,WAAW,CAAC,MAAM;MAClC,MAAME,QAAQ,GAAG7B,eAAe,GAAG,IAAI,GAAG,IAAI8B,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;MAC9D,IAAIF,QAAQ,GAAG,CAAC,EAAE;QACdnB,aAAa,CAACF,iBAAiB,CAAC;QAChCG,gBAAgB,CAAC,SAAS,CAAC;QAC3B;MACJ;MACA,MAAMqB,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACL,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;MACzD,MAAMM,KAAK,GAAGF,IAAI,CAACC,KAAK,CAAEL,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,IAAK,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;MAC/E,MAAMO,OAAO,GAAGH,IAAI,CAACC,KAAK,CAAEL,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,IAAK,IAAI,GAAG,EAAE,CAAC,CAAC;MACvE,MAAMQ,OAAO,GAAGJ,IAAI,CAACC,KAAK,CAAEL,QAAQ,IAAI,IAAI,GAAG,EAAE,CAAC,GAAI,IAAI,CAAC;MAE3D,MAAMS,WAAW,GAAGhC,UAAU,CAACzC,aAAa,CAAC,uBAAuB,CAAC;MACrE,MAAM0E,UAAU,GAAGjC,UAAU,CAACzC,aAAa,CAAC,sBAAsB,CAAC;MACnE,IAAIyE,WAAW,IAAIC,UAAU,EAAE;QAC3BA,UAAU,CAAC1D,SAAS,GAAG,WAAW,IAAIiD,IAAI,CAAC9B,eAAe,GAAG,IAAI,CAAC,CAACwC,cAAc,CAAC,CAAC,WAAW;QAC9F,IAAIC,SAAS,GAAG,EAAE;QAClB,IAAIT,IAAI,GAAG,CAAC,EAAES,SAAS,CAACC,IAAI,CAAC,GAAGV,IAAI,IAAIA,IAAI,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM,EAAE,CAAC;QACtE,IAAIG,KAAK,GAAG,CAAC,EAAEM,SAAS,CAACC,IAAI,CAAC,GAAGP,KAAK,IAAIA,KAAK,KAAK,CAAC,GAAG,MAAM,GAAG,OAAO,EAAE,CAAC;QAC3E,IAAIC,OAAO,GAAG,CAAC,EAAEK,SAAS,CAACC,IAAI,CAAC,GAAGN,OAAO,IAAIA,OAAO,KAAK,CAAC,GAAG,QAAQ,GAAG,SAAS,EAAE,CAAC;QACrF,IAAIK,SAAS,CAAC3B,MAAM,GAAG,CAAC,EAAE2B,SAAS,CAACC,IAAI,CAAC,GAAGL,OAAO,IAAIA,OAAO,KAAK,CAAC,GAAG,QAAQ,GAAG,SAAS,EAAE,CAAC;QAC9FE,UAAU,CAAC1D,SAAS,IAAI,KAAK4D,SAAS,CAACE,IAAI,CAAC,IAAI,CAAC,aAAa;MAClE;IACJ,CAAC,EAAE,IAAI,CAAC;EACZ,CAAC;EAEDjB,YAAY,CAAC,CAAC;EACdE,cAAc,CAAC,CAAC;AACpB,CAAC,CAAC,C", "sources": ["webpack://monoova-payments-for-woocommerce/./assets/js/src/monoova-payid-thankyou.js"], "sourcesContent": ["document.addEventListener(\"DOMContentLoaded\", function () {\n    const container = document.getElementById(\"monoova-payment-instructions\")\n    if (!container) return\n\n    // --- Copy to Clipboard ---\n    container.addEventListener(\"click\", function (e) {\n        const button = e.target.closest(\".monoova-copy-button\")\n        if (!button) return\n\n        const targetId = button.dataset.targetId\n        const targetElement = container.querySelector(`.copy-target[data-copy-id=\"${targetId}\"]`)\n        if (!targetElement) return\n\n        const textToCopy = targetElement.innerText.trim()\n        navigator.clipboard\n            .writeText(textToCopy)\n            .then(() => {})\n            .catch(err => console.error(\"Failed to copy: \", err))\n    })\n\n    // --- QR Code Generation ---\n    const qrCodeContainer = document.getElementById(\"monoova-qr-code\")\n    if (qrCodeContainer) {\n        const qrPayload = qrCodeContainer.getAttribute(\"data-payload\")\n        if (qrPayload && typeof QRCode !== \"undefined\") {\n            // Clear any previous QR code\n            qrCodeContainer.innerHTML = \"\"\n            new QRCode(qrCodeContainer, {\n                text: qrPayload,\n                width: 180,\n                height: 180,\n                colorDark: \"#000000\",\n                colorLight: \"#ffffff\",\n                correctLevel: QRCode.CorrectLevel.H,\n            })\n        }\n    }\n\n    // --- Method Switcher ---\n    const switcher = document.getElementById(\"monoova-method-switcher\")\n    if (switcher) {\n        const payidView = document.getElementById(\"monoova-payid-view\")\n        const bankView = document.getElementById(\"monoova-bank-view\")\n        switcher.addEventListener(\"change\", function () {\n            if (this.value === \"payid\") {\n                payidView.style.display = \"block\"\n                bankView.style.display = \"none\"\n            } else {\n                payidView.style.display = \"none\"\n                bankView.style.display = \"block\"\n            }\n        })\n    }\n\n    // --- Polling and Countdown Logic ---\n    const orderId = container.dataset.orderId\n    const orderKey = container.dataset.orderKey\n    const nonce = container.dataset.nonce\n    const ajaxUrl = container.dataset.ajaxUrl\n    const expiryTimestamp = container.dataset.expiryTimestamp\n\n    const pendingView = document.getElementById(\"monoova-payment-pending\")\n    const confirmedView = document.getElementById(\"monoova-payment-confirmed\")\n    const expiredView = document.getElementById(\"monoova-payment-expired\")\n    const rejectedView = document.getElementById(\"monoova-payment-rejected\")\n    const rejectionReasonEl = document.getElementById(\"monoova-rejection-reason\")\n    const expiryInfo = document.getElementById(\"monoova-expiry-info\")\n\n    let pollingInterval, countdownInterval\n\n    const stopPolling = () => {\n        if (pollingInterval) {\n            clearInterval(pollingInterval)\n        }\n    }\n\n    const updateStatusView = (status, reason = \"\") => {\n        stopPolling()\n        pendingView.style.display = \"none\"\n        confirmedView.style.display = \"none\"\n        rejectedView.style.display = \"none\"\n        expiredView.style.display = \"none\"\n\n        switch (status) {\n            case \"paid\":\n                confirmedView.style.display = \"block\"\n                break\n            case \"failed\":\n                if (rejectionReasonEl && reason) {\n                    if (reason.length === 0) {\n                        reason = \"Unexpected Payment issue\"\n                    }\n                    rejectionReasonEl.innerText = `Reason: ${reason}`\n                }\n                rejectedView.style.display = \"block\"\n                break\n            case \"cancelled\":\n            case \"expired\":\n                expiredView.style.display = \"block\"\n                break\n            default:\n                // If status is pending or something else, keep showing pending view\n                pendingView.style.display = \"block\"\n                break\n        }\n    }\n\n    const checkStatus = () => {\n        const formData = new FormData()\n        formData.append(\"action\", \"monoova_check_payment_status\")\n        formData.append(\"order_id\", orderId)\n        formData.append(\"order_key\", orderKey)\n        formData.append(\"nonce\", nonce)\n\n        fetch(ajaxUrl, {\n            method: \"POST\",\n            body: formData,\n        })\n            .then(response => response.json())\n            .then(data => {\n                if (data.success && data.data.status !== \"pending\") {\n                    updateStatusView(data.data.status, data.data.reason)\n                }\n            })\n            .catch(error => console.error(\"Error polling for payment status:\", error))\n    }\n\n    const startPolling = () => {\n        // Initial check\n        checkStatus()\n        // Poll every 10 seconds\n        pollingInterval = setInterval(checkStatus, 10000)\n    }\n\n    const startCountdown = () => {\n        if (!expiryTimestamp || expiryTimestamp <= 0 || !expiryInfo) return\n        countdownInterval = setInterval(() => {\n            const distance = expiryTimestamp * 1000 - new Date().getTime()\n            if (distance < 0) {\n                clearInterval(countdownInterval)\n                updateStatusView(\"expired\")\n                return\n            }\n            const days = Math.floor(distance / (1000 * 60 * 60 * 24))\n            const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))\n            const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60))\n            const seconds = Math.floor((distance % (1000 * 60)) / 1000)\n\n            const expiryLabel = expiryInfo.querySelector(\".monoova-expiry-label\")\n            const expiryTime = expiryInfo.querySelector(\".monoova-expiry-time\")\n            if (expiryLabel && expiryTime) {\n                expiryTime.innerHTML = `<strong>${new Date(expiryTimestamp * 1000).toLocaleString()}</strong>`\n                let remaining = []\n                if (days > 0) remaining.push(`${days} ${days === 1 ? \"day\" : \"days\"}`)\n                if (hours > 0) remaining.push(`${hours} ${hours === 1 ? \"hour\" : \"hours\"}`)\n                if (minutes > 0) remaining.push(`${minutes} ${minutes === 1 ? \"minute\" : \"minutes\"}`)\n                if (remaining.length < 3) remaining.push(`${seconds} ${seconds === 1 ? \"second\" : \"seconds\"}`)\n                expiryTime.innerHTML += ` (${remaining.join(\", \")} remaining)`\n            }\n        }, 1000)\n    }\n\n    startPolling()\n    startCountdown()\n})\n"], "names": ["document", "addEventListener", "container", "getElementById", "e", "button", "target", "closest", "targetId", "dataset", "targetElement", "querySelector", "textToCopy", "innerText", "trim", "navigator", "clipboard", "writeText", "then", "catch", "err", "console", "error", "qrCodeContainer", "qrPayload", "getAttribute", "QRCode", "innerHTML", "text", "width", "height", "colorDark", "colorLight", "correctLevel", "CorrectLevel", "H", "switcher", "payidView", "bankView", "value", "style", "display", "orderId", "orderKey", "nonce", "ajaxUrl", "expiryTimestamp", "pending<PERSON>iew", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "rejectionReasonEl", "expiryInfo", "pollingInterval", "countdownInterval", "stopPolling", "clearInterval", "updateStatusView", "status", "reason", "length", "checkStatus", "formData", "FormData", "append", "fetch", "method", "body", "response", "json", "data", "success", "startPolling", "setInterval", "startCountdown", "distance", "Date", "getTime", "days", "Math", "floor", "hours", "minutes", "seconds", "expiry<PERSON><PERSON><PERSON>", "expiryTime", "toLocaleString", "remaining", "push", "join"], "sourceRoot": ""}