{"version": 3, "file": "monoova-payto-block.js", "mappings": ";;;;;;;;;;AAAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;UCAA;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;;WCtBA;WACA;WACA;WACA;WACA;WACA,iCAAiC,WAAW;WAC5C;WACA;;;;;WCPA;WACA;WACA;WACA;WACA,yCAAyC,wCAAwC;WACjF;WACA;WACA;;;;;WCPA;;;;;WCAA;WACA;WACA;WACA,uDAAuD,iBAAiB;WACxE;WACA,gDAAgD,aAAa;WAC7D;;;;;;;;;;;;;;;;;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;;AAEoE;AAClB;AACO;AACrB;AACS;;AAE7C;AACA,IAAI,OAAOA,+EAAqB,KAAK,UAAU,EAAE;EAC7CK,OAAO,CAACC,IAAI,CACR,8EAA8E,EAC9EC,MAAM,CAACC,IAAI,CAACC,MAAM,CAACC,EAAE,IAAI,CAAC,CAAC,CAC/B,CAAC;AACL,CAAC,MAAM;EACH;EACA,IAAIC,QAAQ,GAAG,CAAC,CAAC;EACjB,IAAI,OAAOV,6DAAU,KAAK,UAAU,EAAE;IAClC,IAAI;MACAU,QAAQ,GAAGV,iEAAU,CAAC,oBAAoB,EAAE,CAAC,CAAC,CAAC;IACnD,CAAC,CAAC,OAAOW,KAAK,EAAE;MACZP,OAAO,CAACQ,GAAG,CAAC,yCAAyC,EAAED,KAAK,CAAC;IACjE;EACJ;;EAEA;EACA,IAAI,CAACD,QAAQ,IAAIJ,MAAM,CAACC,IAAI,CAACG,QAAQ,CAAC,CAACG,MAAM,KAAK,CAAC,EAAE;IACjDH,QAAQ,GAAGF,MAAM,CAACM,2BAA2B,IAAI,CAAC,CAAC;IACnDV,OAAO,CAACQ,GAAG,CAAC,+CAA+C,EAAEF,QAAQ,CAAC;EAC1E;;EAEA;EACA,IAAI,CAACA,QAAQ,IAAIJ,MAAM,CAACC,IAAI,CAACG,QAAQ,CAAC,CAACG,MAAM,KAAK,CAAC,EAAE;IACjDT,OAAO,CAACC,IAAI,CAAC,wDAAwD,CAAC;IACtEK,QAAQ,GAAG;MACPK,KAAK,EAAE,OAAO;MACdC,WAAW,EAAE,6EAA6E;MAC1FC,QAAQ,EAAE;IACd,CAAC;EACL;EAEA,MAAMC,YAAY,GAAGhB,mDAAE,CAAC,OAAO,EAAE,kCAAkC,CAAC;EACpE,MAAMiB,KAAK,GAAGlB,wEAAc,CAACS,QAAQ,CAACK,KAAK,CAAC,IAAIG,YAAY;;EAE5D;AACJ;AACA;EACI,MAAME,SAAS,GAAGA,CAAC;IAAEC,iBAAiB;IAAEC;EAAa,CAAC,KAAK;IACvD,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGrB,4DAAQ,CAAC,OAAO,CAAC;IAC3D,MAAM,CAACsB,SAAS,EAAEC,YAAY,CAAC,GAAGvB,4DAAQ,CAAC,aAAa,CAAC;IACzD,MAAM,CAACwB,UAAU,EAAEC,aAAa,CAAC,GAAGzB,4DAAQ,CAAC,EAAE,CAAC;IAChD,MAAM,CAAC0B,WAAW,EAAEC,cAAc,CAAC,GAAG3B,4DAAQ,CAAC,EAAE,CAAC;IAClD,MAAM,CAAC4B,GAAG,EAAEC,MAAM,CAAC,GAAG7B,4DAAQ,CAAC,EAAE,CAAC;IAClC,MAAM,CAAC8B,aAAa,EAAEC,gBAAgB,CAAC,GAAG/B,4DAAQ,CAAC,EAAE,CAAC;IACtD,MAAM,CAACgC,MAAM,EAAEC,SAAS,CAAC,GAAGjC,4DAAQ,CAAC,CAAC,CAAC,CAAC;IAExC,MAAM;MAAEkC;IAAe,CAAC,GAAGhB,iBAAiB;IAC5C,MAAM;MAAEiB,aAAa;MAAEC;IAAe,CAAC,GAAGjB,YAAY;;IAEtD;IACA,MAAMkB,kBAAkB,GAAGC,KAAK,IAAI;MAChC;MACA,MAAMC,OAAO,GAAGD,KAAK,CAACE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;;MAExC;MACA,IAAID,OAAO,CAACE,UAAU,CAAC,IAAI,CAAC,EAAE;QAC1B;QACA,OAAO,KAAK,GAAGF,OAAO,CAACG,KAAK,CAAC,CAAC,CAAC;MACnC,CAAC,MAAM,IAAIH,OAAO,CAACE,UAAU,CAAC,GAAG,CAAC,EAAE;QAChC;QACA,OAAOF,OAAO;MAClB,CAAC,MAAM,IAAIA,OAAO,CAAC7B,MAAM,IAAI,CAAC,IAAI,CAAC6B,OAAO,CAACE,UAAU,CAAC,GAAG,CAAC,EAAE;QACxD;QACA,OAAO,GAAG,GAAGF,OAAO;MACxB;MAEA,OAAOD,KAAK;IAChB,CAAC;;IAED;IACA,MAAMK,YAAY,GAAGA,CAAA,KAAM;MACvB,MAAMC,SAAS,GAAG,CAAC,CAAC;MAEpB,IAAIxB,aAAa,KAAK,OAAO,EAAE;QAC3B,IAAI,CAACI,UAAU,CAACqB,IAAI,CAAC,CAAC,EAAE;UACpBD,SAAS,CAACpB,UAAU,GAAGzB,mDAAE,CAAC,oBAAoB,EAAE,kCAAkC,CAAC;QACvF,CAAC,MAAM;UACH;UACA,MAAMuC,KAAK,GAAGd,UAAU,CAACqB,IAAI,CAAC,CAAC;UAC/B,QAAQvB,SAAS;YACb,KAAK,aAAa;cACd,IAAI,CAAC,mCAAmC,CAACwB,IAAI,CAACR,KAAK,CAAC,EAAE;gBAClDM,SAAS,CAACpB,UAAU,GAAGzB,mDAAE,CACrB,mFAAmF,EACnF,kCACJ,CAAC;cACL;cACA;YACJ,KAAK,OAAO;cACR,IAAI,CAAC,4BAA4B,CAAC+C,IAAI,CAACR,KAAK,CAAC,EAAE;gBAC3CM,SAAS,CAACpB,UAAU,GAAGzB,mDAAE,CACrB,qCAAqC,EACrC,kCACJ,CAAC;cACL;cACA;YACJ,KAAK,KAAK;cACN,IAAI,CAAC,UAAU,CAAC+C,IAAI,CAACR,KAAK,CAACE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,EAAE;gBAC5CI,SAAS,CAACpB,UAAU,GAAGzB,mDAAE,CACrB,oCAAoC,EACpC,kCACJ,CAAC;cACL;cACA;YACJ,KAAK,KAAK;cACN,IAAI,CAAC,SAAS,CAAC+C,IAAI,CAACR,KAAK,CAACE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,EAAE;gBAC3CI,SAAS,CAACpB,UAAU,GAAGzB,mDAAE,CACrB,mCAAmC,EACnC,kCACJ,CAAC;cACL;cACA;YACJ,KAAK,gBAAgB;cACjB,IAAI,CAACuC,KAAK,IAAIA,KAAK,CAAC5B,MAAM,GAAG,CAAC,EAAE;gBAC5BkC,SAAS,CAACpB,UAAU,GAAGzB,mDAAE,CACrB,uCAAuC,EACvC,kCACJ,CAAC;cACL;cACA;UACR;QACJ;MACJ,CAAC,MAAM;QACH,IAAI,CAAC2B,WAAW,CAACmB,IAAI,CAAC,CAAC,EAAE;UACrBD,SAAS,CAAClB,WAAW,GAAG3B,mDAAE,CAAC,2BAA2B,EAAE,kCAAkC,CAAC;QAC/F;QACA,IAAI,CAAC6B,GAAG,CAACiB,IAAI,CAAC,CAAC,EAAE;UACbD,SAAS,CAAChB,GAAG,GAAG7B,mDAAE,CAAC,kBAAkB,EAAE,kCAAkC,CAAC;QAC9E,CAAC,MAAM,IAAI,CAAC,gBAAgB,CAAC+C,IAAI,CAAClB,GAAG,CAACiB,IAAI,CAAC,CAAC,CAAC,EAAE;UAC3CD,SAAS,CAAChB,GAAG,GAAG7B,mDAAE,CAAC,sCAAsC,EAAE,kCAAkC,CAAC;QAClG;QACA,IAAI,CAAC+B,aAAa,CAACe,IAAI,CAAC,CAAC,EAAE;UACvBD,SAAS,CAACd,aAAa,GAAG/B,mDAAE,CAAC,6BAA6B,EAAE,kCAAkC,CAAC;QACnG;MACJ;MAEAkC,SAAS,CAACW,SAAS,CAAC;MACpB,OAAOzC,MAAM,CAACC,IAAI,CAACwC,SAAS,CAAC,CAAClC,MAAM,KAAK,CAAC;IAC9C,CAAC;;IAED;IACAqC,KAAK,CAACC,SAAS,CAAC,MAAM;MAClB,MAAMC,WAAW,GAAGf,cAAc,CAAC,MAAM;QACrC,IAAI,CAACS,YAAY,CAAC,CAAC,EAAE;UACjB,OAAO;YACHO,IAAI,EAAEf,aAAa,CAACgB,KAAK;YACzBC,OAAO,EAAErD,mDAAE,CAAC,8CAA8C,EAAE,kCAAkC,CAAC;YAC/FsD,cAAc,EAAEjB,cAAc,CAACkB;UACnC,CAAC;QACL;;QAEA;QACA,OAAO;UACHJ,IAAI,EAAEf,aAAa,CAACoB,OAAO;UAC3BC,IAAI,EAAE;YACFC,iBAAiB,EAAE;cACfC,oBAAoB,EAAEtC,aAAa;cACnCuC,gBAAgB,EAAEvC,aAAa,KAAK,OAAO,GAAGE,SAAS,GAAG,EAAE;cAC5DsC,iBAAiB,EAAExC,aAAa,KAAK,OAAO,GAAGI,UAAU,GAAG,EAAE;cAC9DqC,kBAAkB,EAAEzC,aAAa,KAAK,aAAa,GAAGM,WAAW,GAAG,EAAE;cACtEoC,SAAS,EAAE1C,aAAa,KAAK,aAAa,GAAGQ,GAAG,GAAG,EAAE;cACrDmC,oBAAoB,EAAE3C,aAAa,KAAK,aAAa,GAAGU,aAAa,GAAG;YAC5E;UACJ;QACJ,CAAC;MACL,CAAC,CAAC;MAEF,OAAOmB,WAAW;IACtB,CAAC,EAAE,CAACf,cAAc,EAAEd,aAAa,EAAEE,SAAS,EAAEE,UAAU,EAAEE,WAAW,EAAEE,GAAG,EAAEE,aAAa,EAAEa,YAAY,CAAC,CAAC;IAEzG,oBACII,KAAA,CAAAiB,aAAA;MACIC,SAAS,EAAC,oBAAoB;MAC9BC,KAAK,EAAE;QACHC,OAAO,EAAE,MAAM;QACfC,MAAM,EAAE,gBAAgB;QACxBC,YAAY,EAAE,KAAK;QACnBC,eAAe,EAAE,SAAS;QAC1BC,MAAM,EAAE;MACZ;IAAE,gBAEFxB,KAAA,CAAAiB,aAAA;MAAKC,SAAS,EAAC,gCAAgC;MAACC,KAAK,EAAE;QAAEM,YAAY,EAAE;MAAO;IAAE,gBAC5EzB,KAAA,CAAAiB,aAAA;MAAOE,KAAK,EAAE;QAAEO,OAAO,EAAE,OAAO;QAAED,YAAY,EAAE,MAAM;QAAEE,UAAU,EAAE;MAAM;IAAE,GACvE3E,mDAAE,CAAC,UAAU,EAAE,kCAAkC,CAC/C,CAAC,eACRgD,KAAA,CAAAiB,aAAA,2BACIjB,KAAA,CAAAiB,aAAA;MAAOE,KAAK,EAAE;QAAES,WAAW,EAAE,MAAM;QAAED,UAAU,EAAE;MAAS;IAAE,gBACxD3B,KAAA,CAAAiB,aAAA;MACId,IAAI,EAAC,OAAO;MACZ0B,IAAI,EAAC,sBAAsB;MAC3BtC,KAAK,EAAC,OAAO;MACbuC,OAAO,EAAEzD,aAAa,KAAK,OAAQ;MACnC0D,QAAQ,EAAEC,CAAC,IAAI1D,gBAAgB,CAAC0D,CAAC,CAACC,MAAM,CAAC1C,KAAK,CAAE;MAChD4B,KAAK,EAAE;QAAES,WAAW,EAAE;MAAM;IAAE,CACjC,CAAC,EACD5E,mDAAE,CAAC,OAAO,EAAE,kCAAkC,CAC5C,CAAC,eACRgD,KAAA,CAAAiB,aAAA;MAAOE,KAAK,EAAE;QAAEQ,UAAU,EAAE;MAAS;IAAE,gBACnC3B,KAAA,CAAAiB,aAAA;MACId,IAAI,EAAC,OAAO;MACZ0B,IAAI,EAAC,sBAAsB;MAC3BtC,KAAK,EAAC,aAAa;MACnBuC,OAAO,EAAEzD,aAAa,KAAK,aAAc;MACzC0D,QAAQ,EAAEC,CAAC,IAAI1D,gBAAgB,CAAC0D,CAAC,CAACC,MAAM,CAAC1C,KAAK,CAAE;MAChD4B,KAAK,EAAE;QAAES,WAAW,EAAE;MAAM;IAAE,CACjC,CAAC,EACD5E,mDAAE,CAAC,wBAAwB,EAAE,kCAAkC,CAC7D,CACN,CACJ,CAAC,EAGLqB,aAAa,KAAK,OAAO,iBACtB2B,KAAA,CAAAiB,aAAA;MACIC,SAAS,EAAC,oBAAoB;MAC9BC,KAAK,EAAE;QACHC,OAAO,EAAE,MAAM;QACfG,eAAe,EAAE,MAAM;QACvBD,YAAY,EAAE;MAClB;IAAE,gBACFtB,KAAA,CAAAiB,aAAA;MAAIE,KAAK,EAAE;QAAEe,SAAS,EAAE,CAAC;QAAET,YAAY,EAAE,MAAM;QAAEU,KAAK,EAAE;MAAO;IAAE,GAC5DnF,mDAAE,CAAC,0BAA0B,EAAE,kCAAkC,CAClE,CAAC,eAGLgD,KAAA,CAAAiB,aAAA;MAAKE,KAAK,EAAE;QAAEM,YAAY,EAAE;MAAO;IAAE,gBACjCzB,KAAA,CAAAiB,aAAA;MAAOE,KAAK,EAAE;QAAEO,OAAO,EAAE,OAAO;QAAED,YAAY,EAAE,KAAK;QAAEE,UAAU,EAAE;MAAM;IAAE,GACtE3E,mDAAE,CAAC,YAAY,EAAE,kCAAkC,CAAC,EAAC,IACnD,CAAC,eACRgD,KAAA,CAAAiB,aAAA;MACI1B,KAAK,EAAEhB,SAAU;MACjBwD,QAAQ,EAAEC,CAAC,IAAI;QACXxD,YAAY,CAACwD,CAAC,CAACC,MAAM,CAAC1C,KAAK,CAAC;QAC5Bb,aAAa,CAAC,EAAE,CAAC,EAAC;MACtB,CAAE;MACFyC,KAAK,EAAE;QACHiB,KAAK,EAAE,MAAM;QACbhB,OAAO,EAAE,KAAK;QACdC,MAAM,EAAE,gBAAgB;QACxBC,YAAY,EAAE;MAClB;IAAE,gBACFtB,KAAA,CAAAiB,aAAA;MAAQ1B,KAAK,EAAC;IAAa,GACtBvC,mDAAE,CAAC,eAAe,EAAE,kCAAkC,CACnD,CAAC,eACTgD,KAAA,CAAAiB,aAAA;MAAQ1B,KAAK,EAAC;IAAO,GAAEvC,mDAAE,CAAC,eAAe,EAAE,kCAAkC,CAAU,CAAC,eACxFgD,KAAA,CAAAiB,aAAA;MAAQ1B,KAAK,EAAC;IAAK,GACdvC,mDAAE,CAAC,kCAAkC,EAAE,kCAAkC,CACtE,CAAC,eACTgD,KAAA,CAAAiB,aAAA;MAAQ1B,KAAK,EAAC;IAAK,GACdvC,mDAAE,CAAC,iCAAiC,EAAE,kCAAkC,CACrE,CAAC,eACTgD,KAAA,CAAAiB,aAAA;MAAQ1B,KAAK,EAAC;IAAgB,GACzBvC,mDAAE,CAAC,iBAAiB,EAAE,kCAAkC,CACrD,CACJ,CACP,CAAC,eAGNgD,KAAA,CAAAiB,aAAA;MAAKE,KAAK,EAAE;QAAEM,YAAY,EAAE;MAAO;IAAE,gBACjCzB,KAAA,CAAAiB,aAAA;MAAOE,KAAK,EAAE;QAAEO,OAAO,EAAE,OAAO;QAAED,YAAY,EAAE,KAAK;QAAEE,UAAU,EAAE;MAAM;IAAE,GACtEpD,SAAS,KAAK,aAAa,IAAIvB,mDAAE,CAAC,eAAe,EAAE,kCAAkC,CAAC,EACtFuB,SAAS,KAAK,OAAO,IAAIvB,mDAAE,CAAC,eAAe,EAAE,kCAAkC,CAAC,EAChFuB,SAAS,KAAK,KAAK,IAAIvB,mDAAE,CAAC,KAAK,EAAE,kCAAkC,CAAC,EACpEuB,SAAS,KAAK,KAAK,IAAIvB,mDAAE,CAAC,KAAK,EAAE,kCAAkC,CAAC,EACpEuB,SAAS,KAAK,gBAAgB,IAC3BvB,mDAAE,CAAC,iBAAiB,EAAE,kCAAkC,CAAC,EAC5D,IACE,CAAC,eACRgD,KAAA,CAAAiB,aAAA;MACId,IAAI,EAAE5B,SAAS,KAAK,OAAO,GAAG,OAAO,GAAGA,SAAS,KAAK,aAAa,GAAG,KAAK,GAAG,MAAO;MACrFgB,KAAK,EAAEd,UAAW;MAClBsD,QAAQ,EAAEC,CAAC,IAAI;QACX,IAAIzC,KAAK,GAAGyC,CAAC,CAACC,MAAM,CAAC1C,KAAK;QAC1B;QACA,IAAIhB,SAAS,KAAK,aAAa,EAAE;UAC7BgB,KAAK,GAAGD,kBAAkB,CAACC,KAAK,CAAC;QACrC;QACAb,aAAa,CAACa,KAAK,CAAC;MACxB,CAAE;MACF8C,WAAW,EACP9D,SAAS,KAAK,aAAa,GACrBvB,mDAAE,CAAC,4BAA4B,EAAE,kCAAkC,CAAC,GACpEuB,SAAS,KAAK,OAAO,GACrBvB,mDAAE,CAAC,mBAAmB,EAAE,kCAAkC,CAAC,GAC3DuB,SAAS,KAAK,KAAK,GACnBvB,mDAAE,CAAC,aAAa,EAAE,kCAAkC,CAAC,GACrDuB,SAAS,KAAK,KAAK,GACnBvB,mDAAE,CAAC,WAAW,EAAE,kCAAkC,CAAC,GACnDA,mDAAE,CAAC,uBAAuB,EAAE,kCAAkC,CACvE;MACDmE,KAAK,EAAE;QACHiB,KAAK,EAAE,MAAM;QACbhB,OAAO,EAAE,KAAK;QACdC,MAAM,EAAE,gBAAgB;QACxBC,YAAY,EAAE,KAAK;QACnBgB,WAAW,EAAErD,MAAM,CAACR,UAAU,GAAG,SAAS,GAAG;MACjD;IAAE,CACL,CAAC,EACDQ,MAAM,CAACR,UAAU,iBACduB,KAAA,CAAAiB,aAAA;MAAKE,KAAK,EAAE;QAAEgB,KAAK,EAAE,SAAS;QAAEI,QAAQ,EAAE,MAAM;QAAEL,SAAS,EAAE;MAAM;IAAE,GAChEjD,MAAM,CAACR,UACP,CACR,eACDuB,KAAA,CAAAiB,aAAA;MAAKE,KAAK,EAAE;QAAEgB,KAAK,EAAE,MAAM;QAAEI,QAAQ,EAAE,MAAM;QAAEL,SAAS,EAAE;MAAM;IAAE,GAC7D3D,SAAS,KAAK,aAAa,IACxBvB,mDAAE,CACE,uDAAuD,EACvD,kCACJ,CAAC,EACJuB,SAAS,KAAK,OAAO,IAClBvB,mDAAE,CACE,gDAAgD,EAChD,kCACJ,CAAC,EACJuB,SAAS,KAAK,KAAK,IAChBvB,mDAAE,CAAC,yBAAyB,EAAE,kCAAkC,CAAC,EACpEuB,SAAS,KAAK,KAAK,IAChBvB,mDAAE,CAAC,wBAAwB,EAAE,kCAAkC,CAAC,EACnEuB,SAAS,KAAK,gBAAgB,IAC3BvB,mDAAE,CAAC,oCAAoC,EAAE,kCAAkC,CAC9E,CACJ,CACJ,CACR,EAGAqB,aAAa,KAAK,aAAa,iBAC5B2B,KAAA,CAAAiB,aAAA;MACIC,SAAS,EAAC,oBAAoB;MAC9BC,KAAK,EAAE;QACHC,OAAO,EAAE,MAAM;QACfG,eAAe,EAAE,MAAM;QACvBD,YAAY,EAAE;MAClB;IAAE,gBACFtB,KAAA,CAAAiB,aAAA;MAAIE,KAAK,EAAE;QAAEe,SAAS,EAAE,CAAC;QAAET,YAAY,EAAE,MAAM;QAAEU,KAAK,EAAE;MAAO;IAAE,GAC5DnF,mDAAE,CAAC,yBAAyB,EAAE,kCAAkC,CACjE,CAAC,eACLgD,KAAA,CAAAiB,aAAA;MAAKE,KAAK,EAAE;QAAEM,YAAY,EAAE;MAAO;IAAE,gBACjCzB,KAAA,CAAAiB,aAAA;MAAOE,KAAK,EAAE;QAAEO,OAAO,EAAE,OAAO;QAAED,YAAY,EAAE,KAAK;QAAEE,UAAU,EAAE;MAAM;IAAE,GACtE3E,mDAAE,CAAC,mCAAmC,EAAE,kCAAkC,CAAC,EAAC,IAC1E,CAAC,eACRgD,KAAA,CAAAiB,aAAA;MACId,IAAI,EAAC,MAAM;MACXZ,KAAK,EAAEZ,WAAY;MACnBoD,QAAQ,EAAEC,CAAC,IAAIpD,cAAc,CAACoD,CAAC,CAACC,MAAM,CAAC1C,KAAK,CAAE;MAC9C8C,WAAW,EAAErF,mDAAE,CAAC,iBAAiB,EAAE,kCAAkC,CAAE;MACvEmE,KAAK,EAAE;QACHiB,KAAK,EAAE,MAAM;QACbhB,OAAO,EAAE,KAAK;QACdC,MAAM,EAAE,gBAAgB;QACxBC,YAAY,EAAE,KAAK;QACnBgB,WAAW,EAAErD,MAAM,CAACN,WAAW,GAAG,SAAS,GAAG;MAClD;IAAE,CACL,CAAC,EACDM,MAAM,CAACN,WAAW,iBACfqB,KAAA,CAAAiB,aAAA;MAAKE,KAAK,EAAE;QAAEgB,KAAK,EAAE,SAAS;QAAEI,QAAQ,EAAE,MAAM;QAAEL,SAAS,EAAE;MAAM;IAAE,GAChEjD,MAAM,CAACN,WACP,CAER,CAAC,eACNqB,KAAA,CAAAiB,aAAA;MAAKE,KAAK,EAAE;QAAEO,OAAO,EAAE,MAAM;QAAEc,GAAG,EAAE,MAAM;QAAEf,YAAY,EAAE;MAAO;IAAE,gBAC/DzB,KAAA,CAAAiB,aAAA;MAAKE,KAAK,EAAE;QAAEsB,IAAI,EAAE;MAAE;IAAE,gBACpBzC,KAAA,CAAAiB,aAAA;MAAOE,KAAK,EAAE;QAAEO,OAAO,EAAE,OAAO;QAAED,YAAY,EAAE,KAAK;QAAEE,UAAU,EAAE;MAAM;IAAE,GACtE3E,mDAAE,CAAC,KAAK,EAAE,kCAAkC,CAAC,EAAC,IAC5C,CAAC,eACRgD,KAAA,CAAAiB,aAAA;MACId,IAAI,EAAC,MAAM;MACXZ,KAAK,EAAEV,GAAI;MACXkD,QAAQ,EAAEC,CAAC,IAAIlD,MAAM,CAACkD,CAAC,CAACC,MAAM,CAAC1C,KAAK,CAAE;MACtC8C,WAAW,EAAErF,mDAAE,CAAC,WAAW,EAAE,kCAAkC,CAAE;MACjEmE,KAAK,EAAE;QACHiB,KAAK,EAAE,MAAM;QACbhB,OAAO,EAAE,KAAK;QACdC,MAAM,EAAE,gBAAgB;QACxBC,YAAY,EAAE,KAAK;QACnBgB,WAAW,EAAErD,MAAM,CAACJ,GAAG,GAAG,SAAS,GAAG;MAC1C;IAAE,CACL,CAAC,EACDI,MAAM,CAACJ,GAAG,iBACPmB,KAAA,CAAAiB,aAAA;MAAKE,KAAK,EAAE;QAAEgB,KAAK,EAAE,SAAS;QAAEI,QAAQ,EAAE,MAAM;QAAEL,SAAS,EAAE;MAAM;IAAE,GAChEjD,MAAM,CAACJ,GACP,CAER,CAAC,eACNmB,KAAA,CAAAiB,aAAA;MAAKE,KAAK,EAAE;QAAEsB,IAAI,EAAE;MAAE;IAAE,gBACpBzC,KAAA,CAAAiB,aAAA;MAAOE,KAAK,EAAE;QAAEO,OAAO,EAAE,OAAO;QAAED,YAAY,EAAE,KAAK;QAAEE,UAAU,EAAE;MAAM;IAAE,GACtE3E,mDAAE,CAAC,gBAAgB,EAAE,kCAAkC,CAAC,EAAC,IACvD,CAAC,eACRgD,KAAA,CAAAiB,aAAA;MACId,IAAI,EAAC,MAAM;MACXZ,KAAK,EAAER,aAAc;MACrBgD,QAAQ,EAAEC,CAAC,IAAIhD,gBAAgB,CAACgD,CAAC,CAACC,MAAM,CAAC1C,KAAK,CAAE;MAChD8C,WAAW,EAAErF,mDAAE,CAAC,2BAA2B,EAAE,kCAAkC,CAAE;MACjFmE,KAAK,EAAE;QACHiB,KAAK,EAAE,MAAM;QACbhB,OAAO,EAAE,KAAK;QACdC,MAAM,EAAE,gBAAgB;QACxBC,YAAY,EAAE,KAAK;QACnBgB,WAAW,EAAErD,MAAM,CAACF,aAAa,GAAG,SAAS,GAAG;MACpD;IAAE,CACL,CAAC,EACDE,MAAM,CAACF,aAAa,iBACjBiB,KAAA,CAAAiB,aAAA;MAAKE,KAAK,EAAE;QAAEgB,KAAK,EAAE,SAAS;QAAEI,QAAQ,EAAE,MAAM;QAAEL,SAAS,EAAE;MAAM;IAAE,GAChEjD,MAAM,CAACF,aACP,CAER,CACJ,CACJ,CACR,eAEDiB,KAAA,CAAAiB,aAAA;MACIE,KAAK,EAAE;QACHe,SAAS,EAAE,MAAM;QACjBd,OAAO,EAAE,MAAM;QACfG,eAAe,EAAE,SAAS;QAC1BD,YAAY,EAAE,KAAK;QACnBiB,QAAQ,EAAE;MACd;IAAE,gBACFvC,KAAA,CAAAiB,aAAA,iBAASjE,mDAAE,CAAC,8BAA8B,EAAE,kCAAkC,CAAU,CAAC,eACzFgD,KAAA,CAAAiB,aAAA;MAAGE,KAAK,EAAE;QAAEK,MAAM,EAAE,WAAW;QAAEW,KAAK,EAAE;MAAO;IAAE,GAC5CnF,mDAAE,CACC,uEAAuE,EACvE,kCACJ,CACD,CACF,CACJ,CAAC;EAEd,CAAC;;EAED;AACJ;AACA;EACI,MAAM0F,OAAO,GAAG,SAAAA,CAAUC,KAAK,EAAE;IAC7B,oBAAO3C,KAAA,CAAAiB,aAAA,CAAC/C,SAAS,EAAKyE,KAAQ,CAAC;EACnC,CAAC;;EAED;AACJ;AACA;EACI,MAAMC,KAAK,GAAGD,KAAK,IAAI;IACnB,oBACI3C,KAAA,CAAAiB,aAAA;MACIE,KAAK,EAAE;QACHO,OAAO,EAAE,MAAM;QACfmB,cAAc,EAAE,eAAe;QAC/BC,UAAU,EAAE,QAAQ;QACpBV,KAAK,EAAE;MACX;IAAE,gBACFpC,KAAA,CAAAiB,aAAA;MAAME,KAAK,EAAE;QAAEQ,UAAU,EAAE;MAAI;IAAE,GAAE1D,KAAY,CAAC,eAChD+B,KAAA,CAAAiB,aAAA;MACI8B,GAAG,EAAE,GACDvF,QAAQ,CAACwF,UAAU,IAAI,uDAAuD,8BACnD;MAC/BC,GAAG,EAAC,YAAY;MAChB9B,KAAK,EAAE;QACH+B,MAAM,EAAE,MAAM;QACdd,KAAK,EAAE;MACX;IAAE,CACL,CACA,CAAC;EAEd,CAAC;;EAED;AACJ;AACA;EACI,MAAMe,YAAY,GAAG;IACjBtB,IAAI,EAAE,eAAe;IACrB5D,KAAK,eAAE+B,KAAA,CAAAiB,aAAA,CAAC2B,KAAK,MAAE,CAAC;IAChBQ,OAAO,eAAEpD,KAAA,CAAAiB,aAAA,CAACyB,OAAO,MAAE,CAAC;IACpBW,IAAI,eAAErD,KAAA,CAAAiB,aAAA,CAACyB,OAAO,MAAE,CAAC;IACjBY,cAAc,EAAE,SAAAA,CAAA,EAAY;MACxB,OAAO,IAAI;IACf,CAAC;IACDC,SAAS,EAAEtF,KAAK;IAChBF,QAAQ,EAAE;MACNyF,QAAQ,EAAEhG,QAAQ,CAACO,QAAQ,IAAI;IACnC,CAAC;IACD0F,KAAK,EAAEjG,QAAQ,CAACiG,KAAK,IAAI;EAC7B,CAAC;;EAED;EACA,IAAI;IACA5G,mFAAqB,CAACsG,YAAY,CAAC;EACvC,CAAC,CAAC,OAAO1F,KAAK,EAAE;IACZP,OAAO,CAACO,KAAK,CAAC,yDAAyD,EAAEA,KAAK,CAAC;EACnF;AACJ,C", "sources": ["webpack://monoova-payments-for-woocommerce/external var [\"wc\",\"wcBlocksRegistry\"]", "webpack://monoova-payments-for-woocommerce/external var [\"wc\",\"wcSettings\"]", "webpack://monoova-payments-for-woocommerce/external window [\"wp\",\"element\"]", "webpack://monoova-payments-for-woocommerce/external window [\"wp\",\"htmlEntities\"]", "webpack://monoova-payments-for-woocommerce/external window [\"wp\",\"i18n\"]", "webpack://monoova-payments-for-woocommerce/webpack/bootstrap", "webpack://monoova-payments-for-woocommerce/webpack/runtime/compat get default export", "webpack://monoova-payments-for-woocommerce/webpack/runtime/define property getters", "webpack://monoova-payments-for-woocommerce/webpack/runtime/hasOwnProperty shorthand", "webpack://monoova-payments-for-woocommerce/webpack/runtime/make namespace object", "webpack://monoova-payments-for-woocommerce/./assets/js/src/blocks/monoova-payto-block.js"], "sourcesContent": ["module.exports = wc.wcBlocksRegistry;", "module.exports = wc.wcSettings;", "module.exports = window[\"wp\"][\"element\"];", "module.exports = window[\"wp\"][\"htmlEntities\"];", "module.exports = window[\"wp\"][\"i18n\"];", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "/**\n * Monoova PayTo Block for WooCommerce Blocks\n *\n * Uses ES6 imports with defensive programming to handle cases where WooCommerce dependencies\n * may not be available (edit mode, missing plugins, etc.)\n */\n\nimport { registerPaymentMethod } from \"@woocommerce/blocks-registry\"\nimport { getSetting } from \"@woocommerce/settings\"\nimport { decodeEntities } from \"@wordpress/html-entities\"\nimport { __ } from \"@wordpress/i18n\"\nimport { useState } from \"@wordpress/element\"\n\n// If registerPaymentMethod is not available, we can't register the payment method\nif (typeof registerPaymentMethod !== \"function\") {\n    console.warn(\n        \"Monoova PayTo Block: registerPaymentMethod not available. Available globals:\",\n        Object.keys(window.wc || {})\n    )\n} else {\n    // Try to get settings\n    let settings = {}\n    if (typeof getSetting === \"function\") {\n        try {\n            settings = getSetting(\"monoova_payto_data\", {})\n        } catch (error) {\n            console.log(\"Monoova PayTo Block: getSetting failed:\", error)\n        }\n    }\n\n    // Fallback to global variable if getSetting didn't work\n    if (!settings || Object.keys(settings).length === 0) {\n        settings = window.monoova_payto_blocks_params || {}\n        console.log(\"Monoova PayTo Block: Using fallback settings:\", settings)\n    }\n\n    // Set defaults if no settings available\n    if (!settings || Object.keys(settings).length === 0) {\n        console.warn(\"Monoova PayTo Block: No settings found, using defaults\")\n        settings = {\n            title: \"PayTo\",\n            description: \"Set up PayTo directly from your bank using BSB and Account Number or PayID.\",\n            supports: [],\n        }\n    }\n\n    const defaultLabel = __(\"PayTo\", \"monoova-payments-for-woocommerce\")\n    const label = decodeEntities(settings.title) || defaultLabel\n\n    /**\n     * PayTo Payment Form Component\n     */\n    const PayToForm = ({ eventRegistration, emitResponse }) => {\n        const [paymentMethod, setPaymentMethod] = useState(\"payid\")\n        const [payidType, setPayidType] = useState(\"PhoneNumber\")\n        const [payidValue, setPayidValue] = useState(\"\")\n        const [accountName, setAccountName] = useState(\"\")\n        const [bsb, setBsb] = useState(\"\")\n        const [accountNumber, setAccountNumber] = useState(\"\")\n        const [errors, setErrors] = useState({})\n\n        const { onPaymentSetup } = eventRegistration\n        const { responseTypes, noticeContexts } = emitResponse\n\n        // Format mobile number input\n        const formatMobileNumber = value => {\n            // Remove all non-digits\n            const numbers = value.replace(/\\D/g, \"\")\n\n            // Handle different input patterns\n            if (numbers.startsWith(\"61\")) {\n                // Already has country code\n                return \"+61\" + numbers.slice(2)\n            } else if (numbers.startsWith(\"0\")) {\n                // Australian format starting with 0\n                return numbers\n            } else if (numbers.length <= 9 && !numbers.startsWith(\"0\")) {\n                // Could be without leading 0, add it\n                return \"0\" + numbers\n            }\n\n            return value\n        }\n\n        // Validation function\n        const validateForm = () => {\n            const newErrors = {}\n\n            if (paymentMethod === \"payid\") {\n                if (!payidValue.trim()) {\n                    newErrors.payidValue = __(\"PayID is required.\", \"monoova-payments-for-woocommerce\")\n                } else {\n                    // Validate based on PayID type\n                    const value = payidValue.trim()\n                    switch (payidType) {\n                        case \"PhoneNumber\":\n                            if (!/^(\\+61\\s?[4-5]\\d{8}|0[4-5]\\d{8})$/.test(value)) {\n                                newErrors.payidValue = __(\n                                    \"Please enter a valid Australian mobile number (e.g., 0412345678 or +***********).\",\n                                    \"monoova-payments-for-woocommerce\"\n                                )\n                            }\n                            break\n                        case \"Email\":\n                            if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(value)) {\n                                newErrors.payidValue = __(\n                                    \"Please enter a valid email address.\",\n                                    \"monoova-payments-for-woocommerce\"\n                                )\n                            }\n                            break\n                        case \"ABN\":\n                            if (!/^\\d{11}$/.test(value.replace(/\\s/g, \"\"))) {\n                                newErrors.payidValue = __(\n                                    \"Please enter a valid 11-digit ABN.\",\n                                    \"monoova-payments-for-woocommerce\"\n                                )\n                            }\n                            break\n                        case \"ACN\":\n                            if (!/^\\d{9}$/.test(value.replace(/\\s/g, \"\"))) {\n                                newErrors.payidValue = __(\n                                    \"Please enter a valid 9-digit ACN.\",\n                                    \"monoova-payments-for-woocommerce\"\n                                )\n                            }\n                            break\n                        case \"OrganisationId\":\n                            if (!value || value.length < 2) {\n                                newErrors.payidValue = __(\n                                    \"Please enter a valid Organisation ID.\",\n                                    \"monoova-payments-for-woocommerce\"\n                                )\n                            }\n                            break\n                    }\n                }\n            } else {\n                if (!accountName.trim()) {\n                    newErrors.accountName = __(\"Account name is required.\", \"monoova-payments-for-woocommerce\")\n                }\n                if (!bsb.trim()) {\n                    newErrors.bsb = __(\"BSB is required.\", \"monoova-payments-for-woocommerce\")\n                } else if (!/^\\d{3}-?\\d{3}$/.test(bsb.trim())) {\n                    newErrors.bsb = __(\"Please enter a valid BSB (6 digits).\", \"monoova-payments-for-woocommerce\")\n                }\n                if (!accountNumber.trim()) {\n                    newErrors.accountNumber = __(\"Account number is required.\", \"monoova-payments-for-woocommerce\")\n                }\n            }\n\n            setErrors(newErrors)\n            return Object.keys(newErrors).length === 0\n        }\n\n        // Register payment setup handler\n        React.useEffect(() => {\n            const unsubscribe = onPaymentSetup(() => {\n                if (!validateForm()) {\n                    return {\n                        type: responseTypes.ERROR,\n                        message: __(\"Please correct the errors in the PayTo form.\", \"monoova-payments-for-woocommerce\"),\n                        messageContext: noticeContexts.PAYMENTS,\n                    }\n                }\n\n                // Return payment data\n                return {\n                    type: responseTypes.SUCCESS,\n                    meta: {\n                        paymentMethodData: {\n                            payto_payment_method: paymentMethod,\n                            payto_payid_type: paymentMethod === \"payid\" ? payidType : \"\",\n                            payto_payid_value: paymentMethod === \"payid\" ? payidValue : \"\",\n                            payto_account_name: paymentMethod === \"bsb_account\" ? accountName : \"\",\n                            payto_bsb: paymentMethod === \"bsb_account\" ? bsb : \"\",\n                            payto_account_number: paymentMethod === \"bsb_account\" ? accountNumber : \"\",\n                        },\n                    },\n                }\n            })\n\n            return unsubscribe\n        }, [onPaymentSetup, paymentMethod, payidType, payidValue, accountName, bsb, accountNumber, validateForm])\n\n        return (\n            <div\n                className=\"monoova-payto-form\"\n                style={{\n                    padding: \"15px\",\n                    border: \"1px solid #ddd\",\n                    borderRadius: \"5px\",\n                    backgroundColor: \"#f9f9f9\",\n                    margin: \"10px 0\",\n                }}>\n                {/* Payment method selection */}\n                <div className=\"payto-payment-method-selection\" style={{ marginBottom: \"15px\" }}>\n                    <label style={{ display: \"block\", marginBottom: \"10px\", fontWeight: \"500\" }}>\n                        {__(\"Pay with\", \"monoova-payments-for-woocommerce\")}\n                    </label>\n                    <div>\n                        <label style={{ marginRight: \"20px\", fontWeight: \"normal\" }}>\n                            <input\n                                type=\"radio\"\n                                name=\"payto_payment_method\"\n                                value=\"payid\"\n                                checked={paymentMethod === \"payid\"}\n                                onChange={e => setPaymentMethod(e.target.value)}\n                                style={{ marginRight: \"8px\" }}\n                            />\n                            {__(\"PayID\", \"monoova-payments-for-woocommerce\")}\n                        </label>\n                        <label style={{ fontWeight: \"normal\" }}>\n                            <input\n                                type=\"radio\"\n                                name=\"payto_payment_method\"\n                                value=\"bsb_account\"\n                                checked={paymentMethod === \"bsb_account\"}\n                                onChange={e => setPaymentMethod(e.target.value)}\n                                style={{ marginRight: \"8px\" }}\n                            />\n                            {__(\"BSB and account number\", \"monoova-payments-for-woocommerce\")}\n                        </label>\n                    </div>\n                </div>\n\n                {/* PayID Fields */}\n                {paymentMethod === \"payid\" && (\n                    <div\n                        className=\"payto-fields-group\"\n                        style={{\n                            padding: \"10px\",\n                            backgroundColor: \"#fff\",\n                            borderRadius: \"3px\",\n                        }}>\n                        <h4 style={{ marginTop: 0, marginBottom: \"10px\", color: \"#333\" }}>\n                            {__(\"Enter your PayID details\", \"monoova-payments-for-woocommerce\")}\n                        </h4>\n\n                        {/* PayID Type Selection */}\n                        <div style={{ marginBottom: \"15px\" }}>\n                            <label style={{ display: \"block\", marginBottom: \"5px\", fontWeight: \"500\" }}>\n                                {__(\"PayID Type\", \"monoova-payments-for-woocommerce\")} *\n                            </label>\n                            <select\n                                value={payidType}\n                                onChange={e => {\n                                    setPayidType(e.target.value)\n                                    setPayidValue(\"\") // Clear value when type changes\n                                }}\n                                style={{\n                                    width: \"100%\",\n                                    padding: \"8px\",\n                                    border: \"1px solid #ddd\",\n                                    borderRadius: \"3px\",\n                                }}>\n                                <option value=\"PhoneNumber\">\n                                    {__(\"Mobile Number\", \"monoova-payments-for-woocommerce\")}\n                                </option>\n                                <option value=\"Email\">{__(\"Email Address\", \"monoova-payments-for-woocommerce\")}</option>\n                                <option value=\"ABN\">\n                                    {__(\"ABN (Australian Business Number)\", \"monoova-payments-for-woocommerce\")}\n                                </option>\n                                <option value=\"ACN\">\n                                    {__(\"ACN (Australian Company Number)\", \"monoova-payments-for-woocommerce\")}\n                                </option>\n                                <option value=\"OrganisationId\">\n                                    {__(\"Organisation ID\", \"monoova-payments-for-woocommerce\")}\n                                </option>\n                            </select>\n                        </div>\n\n                        {/* PayID Value Input */}\n                        <div style={{ marginBottom: \"10px\" }}>\n                            <label style={{ display: \"block\", marginBottom: \"5px\", fontWeight: \"500\" }}>\n                                {payidType === \"PhoneNumber\" && __(\"Mobile Number\", \"monoova-payments-for-woocommerce\")}\n                                {payidType === \"Email\" && __(\"Email Address\", \"monoova-payments-for-woocommerce\")}\n                                {payidType === \"ABN\" && __(\"ABN\", \"monoova-payments-for-woocommerce\")}\n                                {payidType === \"ACN\" && __(\"ACN\", \"monoova-payments-for-woocommerce\")}\n                                {payidType === \"OrganisationId\" &&\n                                    __(\"Organisation ID\", \"monoova-payments-for-woocommerce\")}\n                                {\" *\"}\n                            </label>\n                            <input\n                                type={payidType === \"Email\" ? \"email\" : payidType === \"PhoneNumber\" ? \"tel\" : \"text\"}\n                                value={payidValue}\n                                onChange={e => {\n                                    let value = e.target.value\n                                    // Format mobile number if it's a phone number\n                                    if (payidType === \"PhoneNumber\") {\n                                        value = formatMobileNumber(value)\n                                    }\n                                    setPayidValue(value)\n                                }}\n                                placeholder={\n                                    payidType === \"PhoneNumber\"\n                                        ? __(\"0412345678 or +***********\", \"monoova-payments-for-woocommerce\")\n                                        : payidType === \"Email\"\n                                        ? __(\"<EMAIL>\", \"monoova-payments-for-woocommerce\")\n                                        : payidType === \"ABN\"\n                                        ? __(\"*********01\", \"monoova-payments-for-woocommerce\")\n                                        : payidType === \"ACN\"\n                                        ? __(\"*********\", \"monoova-payments-for-woocommerce\")\n                                        : __(\"Enter Organisation ID\", \"monoova-payments-for-woocommerce\")\n                                }\n                                style={{\n                                    width: \"100%\",\n                                    padding: \"8px\",\n                                    border: \"1px solid #ddd\",\n                                    borderRadius: \"3px\",\n                                    borderColor: errors.payidValue ? \"#e74c3c\" : \"#ddd\",\n                                }}\n                            />\n                            {errors.payidValue && (\n                                <div style={{ color: \"#e74c3c\", fontSize: \"12px\", marginTop: \"5px\" }}>\n                                    {errors.payidValue}\n                                </div>\n                            )}\n                            <div style={{ color: \"#666\", fontSize: \"12px\", marginTop: \"5px\" }}>\n                                {payidType === \"PhoneNumber\" &&\n                                    __(\n                                        \"Enter your mobile number with or without country code\",\n                                        \"monoova-payments-for-woocommerce\"\n                                    )}\n                                {payidType === \"Email\" &&\n                                    __(\n                                        \"Use the email address registered as your PayID\",\n                                        \"monoova-payments-for-woocommerce\"\n                                    )}\n                                {payidType === \"ABN\" &&\n                                    __(\"Enter your 11-digit ABN\", \"monoova-payments-for-woocommerce\")}\n                                {payidType === \"ACN\" &&\n                                    __(\"Enter your 9-digit ACN\", \"monoova-payments-for-woocommerce\")}\n                                {payidType === \"OrganisationId\" &&\n                                    __(\"Enter your organisation identifier\", \"monoova-payments-for-woocommerce\")}\n                            </div>\n                        </div>\n                    </div>\n                )}\n\n                {/* BSB and Account Fields */}\n                {paymentMethod === \"bsb_account\" && (\n                    <div\n                        className=\"payto-fields-group\"\n                        style={{\n                            padding: \"10px\",\n                            backgroundColor: \"#fff\",\n                            borderRadius: \"3px\",\n                        }}>\n                        <h4 style={{ marginTop: 0, marginBottom: \"10px\", color: \"#333\" }}>\n                            {__(\"Enter your bank details\", \"monoova-payments-for-woocommerce\")}\n                        </h4>\n                        <div style={{ marginBottom: \"10px\" }}>\n                            <label style={{ display: \"block\", marginBottom: \"5px\", fontWeight: \"500\" }}>\n                                {__(\"Name associated with bank account\", \"monoova-payments-for-woocommerce\")} *\n                            </label>\n                            <input\n                                type=\"text\"\n                                value={accountName}\n                                onChange={e => setAccountName(e.target.value)}\n                                placeholder={__(\"Enter your name\", \"monoova-payments-for-woocommerce\")}\n                                style={{\n                                    width: \"100%\",\n                                    padding: \"8px\",\n                                    border: \"1px solid #ddd\",\n                                    borderRadius: \"3px\",\n                                    borderColor: errors.accountName ? \"#e74c3c\" : \"#ddd\",\n                                }}\n                            />\n                            {errors.accountName && (\n                                <div style={{ color: \"#e74c3c\", fontSize: \"12px\", marginTop: \"5px\" }}>\n                                    {errors.accountName}\n                                </div>\n                            )}\n                        </div>\n                        <div style={{ display: \"flex\", gap: \"10px\", marginBottom: \"10px\" }}>\n                            <div style={{ flex: 1 }}>\n                                <label style={{ display: \"block\", marginBottom: \"5px\", fontWeight: \"500\" }}>\n                                    {__(\"BSB\", \"monoova-payments-for-woocommerce\")} *\n                                </label>\n                                <input\n                                    type=\"text\"\n                                    value={bsb}\n                                    onChange={e => setBsb(e.target.value)}\n                                    placeholder={__(\"Enter BSB\", \"monoova-payments-for-woocommerce\")}\n                                    style={{\n                                        width: \"100%\",\n                                        padding: \"8px\",\n                                        border: \"1px solid #ddd\",\n                                        borderRadius: \"3px\",\n                                        borderColor: errors.bsb ? \"#e74c3c\" : \"#ddd\",\n                                    }}\n                                />\n                                {errors.bsb && (\n                                    <div style={{ color: \"#e74c3c\", fontSize: \"12px\", marginTop: \"5px\" }}>\n                                        {errors.bsb}\n                                    </div>\n                                )}\n                            </div>\n                            <div style={{ flex: 1 }}>\n                                <label style={{ display: \"block\", marginBottom: \"5px\", fontWeight: \"500\" }}>\n                                    {__(\"Account Number\", \"monoova-payments-for-woocommerce\")} *\n                                </label>\n                                <input\n                                    type=\"text\"\n                                    value={accountNumber}\n                                    onChange={e => setAccountNumber(e.target.value)}\n                                    placeholder={__(\"Enter your account number\", \"monoova-payments-for-woocommerce\")}\n                                    style={{\n                                        width: \"100%\",\n                                        padding: \"8px\",\n                                        border: \"1px solid #ddd\",\n                                        borderRadius: \"3px\",\n                                        borderColor: errors.accountNumber ? \"#e74c3c\" : \"#ddd\",\n                                    }}\n                                />\n                                {errors.accountNumber && (\n                                    <div style={{ color: \"#e74c3c\", fontSize: \"12px\", marginTop: \"5px\" }}>\n                                        {errors.accountNumber}\n                                    </div>\n                                )}\n                            </div>\n                        </div>\n                    </div>\n                )}\n\n                <div\n                    style={{\n                        marginTop: \"15px\",\n                        padding: \"10px\",\n                        backgroundColor: \"#e7f3ff\",\n                        borderRadius: \"3px\",\n                        fontSize: \"14px\",\n                    }}>\n                    <strong>{__(\"Authorise recurring payments\", \"monoova-payments-for-woocommerce\")}</strong>\n                    <p style={{ margin: \"5px 0 0 0\", color: \"#666\" }}>\n                        {__(\n                            \"Approve your recurring payment in your online banking or banking app!\",\n                            \"monoova-payments-for-woocommerce\"\n                        )}\n                    </p>\n                </div>\n            </div>\n        )\n    }\n\n    /**\n     * Content component for PayTo\n     */\n    const Content = function (props) {\n        return <PayToForm {...props} />\n    }\n\n    /**\n     * Label component with PayTo icon\n     */\n    const Label = props => {\n        return (\n            <div\n                style={{\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    alignItems: \"center\",\n                    width: \"100%\",\n                }}>\n                <span style={{ fontWeight: 600 }}>{label}</span>\n                <img\n                    src={`${\n                        settings.plugin_url || \"/wp-content/plugins/monoova-payments-for-woocommerce/\"\n                    }assets/images/payto-logo.svg`}\n                    alt=\"PayTo logo\"\n                    style={{\n                        height: \"24px\",\n                        width: \"auto\",\n                    }}\n                />\n            </div>\n        )\n    }\n\n    /**\n     * Monoova PayTo payment method config object\n     */\n    const MonoovaPayTo = {\n        name: \"monoova_payto\",\n        label: <Label />,\n        content: <Content />,\n        edit: <Content />,\n        canMakePayment: function () {\n            return true\n        },\n        ariaLabel: label,\n        supports: {\n            features: settings.supports || [],\n        },\n        icons: settings.icons || null,\n    }\n\n    // Register the payment method\n    try {\n        registerPaymentMethod(MonoovaPayTo)\n    } catch (error) {\n        console.error(\"Monoova PayTo Block: Failed to register payment method:\", error)\n    }\n}\n"], "names": ["registerPaymentMethod", "getSetting", "decodeEntities", "__", "useState", "console", "warn", "Object", "keys", "window", "wc", "settings", "error", "log", "length", "monoova_payto_blocks_params", "title", "description", "supports", "defaultLabel", "label", "PayToForm", "eventRegistration", "emitResponse", "paymentMethod", "setPaymentMethod", "payidType", "setPayidType", "payidValue", "setPayidValue", "accountName", "setAccountName", "bsb", "setBsb", "accountNumber", "setAccountNumber", "errors", "setErrors", "onPaymentSetup", "responseTypes", "noticeContexts", "formatMobileNumber", "value", "numbers", "replace", "startsWith", "slice", "validateForm", "newErrors", "trim", "test", "React", "useEffect", "unsubscribe", "type", "ERROR", "message", "messageContext", "PAYMENTS", "SUCCESS", "meta", "paymentMethodData", "payto_payment_method", "payto_payid_type", "payto_payid_value", "payto_account_name", "payto_bsb", "payto_account_number", "createElement", "className", "style", "padding", "border", "borderRadius", "backgroundColor", "margin", "marginBottom", "display", "fontWeight", "marginRight", "name", "checked", "onChange", "e", "target", "marginTop", "color", "width", "placeholder", "borderColor", "fontSize", "gap", "flex", "Content", "props", "Label", "justifyContent", "alignItems", "src", "plugin_url", "alt", "height", "MonoovaPayTo", "content", "edit", "canMakePayment", "aria<PERSON><PERSON><PERSON>", "features", "icons"], "sourceRoot": ""}