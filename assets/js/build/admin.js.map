{"version": 3, "file": "admin.js", "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA,CAAC,UAASA,CAAC,EAAE;EACT,YAAY;;EAEZ;AACJ;AACA;EACI,IAAIC,YAAY,GAAG;IACf;AACR;AACA;IACQC,IAAI,EAAE,SAAAA,CAAA,EAAW;MACb,IAAI,CAACC,gBAAgB,CAAC,CAAC;MACvB,IAAI,CAACC,mBAAmB,CAAC,CAAC;IAC9B,CAAC;IAED;AACR;AACA;IACQD,gBAAgB,EAAE,SAAAA,CAAA,EAAW;MACzBH,CAAC,CAAC,uBAAuB,CAAC,CAACK,EAAE,CAAC,OAAO,EAAE,YAAW;QAC9C,IAAIC,UAAU,GAAGN,CAAC,CAAC,IAAI,CAAC,CAACO,IAAI,CAAC,gBAAgB,CAAC;QAC/CN,YAAY,CAACO,eAAe,CAACF,UAAU,EAAEN,CAAC,CAAC,IAAI,CAAC,CAAC;MACrD,CAAC,CAAC;IACN,CAAC;IAED;AACR;AACA;AACA;AACA;AACA;IACQQ,eAAe,EAAE,SAAAA,CAASC,IAAI,EAAEC,OAAO,EAAE;MACrC;MACA,IAAIC,KAAK,GAAGX,CAAC,CAAC,SAAS,CAAC;MACxBA,CAAC,CAAC,MAAM,CAAC,CAACY,MAAM,CAACD,KAAK,CAAC;MACvBA,KAAK,CAACE,GAAG,CAACJ,IAAI,CAAC,CAACK,MAAM,CAAC,CAAC;;MAExB;MACA,IAAIC,OAAO,GAAGC,QAAQ,CAACC,WAAW,CAAC,MAAM,CAAC;MAC1CN,KAAK,CAACO,MAAM,CAAC,CAAC;;MAEd;MACA,IAAIH,OAAO,EAAE;QACT,IAAII,YAAY,GAAGT,OAAO,CAACD,IAAI,CAAC,CAAC;QAEjCC,OAAO,CAACD,IAAI,CAACW,oBAAoB,CAACC,IAAI,CAACN,OAAO,CAAC;QAE/CO,UAAU,CAAC,YAAW;UAClBZ,OAAO,CAACD,IAAI,CAACU,YAAY,CAAC;QAC9B,CAAC,EAAE,IAAI,CAAC;MACZ;IACJ,CAAC;IAED;AACR;AACA;IACQf,mBAAmB,EAAE,SAAAA,CAAA,EAAW;MAC5BJ,CAAC,CAAC,0BAA0B,CAAC,CAACK,EAAE,CAAC,OAAO,EAAE,UAASkB,CAAC,EAAE;QAClDA,CAAC,CAACC,cAAc,CAAC,CAAC;QAElB,IAAId,OAAO,GAAGV,CAAC,CAAC,IAAI,CAAC;QACrB,IAAIyB,gBAAgB,GAAGf,OAAO,CAACgB,OAAO,CAAC,IAAI,CAAC,CAACC,IAAI,CAAC,0BAA0B,CAAC;;QAE7E;QACAjB,OAAO,CAACkB,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;QAC9BlB,OAAO,CAACD,IAAI,CAACW,oBAAoB,CAACC,IAAI,CAACQ,OAAO,CAAC;QAC/CJ,gBAAgB,CAACK,IAAI,CAAC,EAAE,CAAC;;QAEzB;QACA,IAAIC,IAAI,GAAG/B,CAAC,CAAC,oCAAoC,CAAC,CAACgC,EAAE,CAAC,UAAU,CAAC,GAAG,MAAM,GAAG,MAAM;QACnF,IAAIC,MAAM,GAAGF,IAAI,KAAK,MAAM,GACtB/B,CAAC,CAAC,wCAAwC,CAAC,CAACa,GAAG,CAAC,CAAC,GACjDb,CAAC,CAAC,wCAAwC,CAAC,CAACa,GAAG,CAAC,CAAC;QAEvD,IAAI,CAACoB,MAAM,EAAE;UACTR,gBAAgB,CAACK,IAAI,CAAC,gEAAgE,CAAC;UACvFpB,OAAO,CAACkB,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC;UAC/BlB,OAAO,CAACD,IAAI,CAAC,qBAAqB,CAAC;UACnC;QACJ;;QAEA;QACAT,CAAC,CAACkC,IAAI,CAAC;UACHC,GAAG,EAAEf,oBAAoB,CAACgB,QAAQ;UAClCC,MAAM,EAAE,MAAM;UACd9B,IAAI,EAAE;YACF+B,MAAM,EAAE,6BAA6B;YACrCC,OAAO,EAAEN,MAAM;YACfF,IAAI,EAAEA,IAAI;YACVS,KAAK,EAAEpB,oBAAoB,CAACoB;UAChC,CAAC;UACDzB,OAAO,EAAE,SAAAA,CAAS0B,QAAQ,EAAE;YACxB,IAAIA,QAAQ,CAAC1B,OAAO,EAAE;cAClBU,gBAAgB,CAACK,IAAI,CAAC,oEAAoE,CAAC;YAC/F,CAAC,MAAM;cACHL,gBAAgB,CAACK,IAAI,CAAC,yDAAyD,GAAGW,QAAQ,CAAClC,IAAI,CAACmC,OAAO,GAAG,QAAQ,CAAC;YACvH;UACJ,CAAC;UACDC,KAAK,EAAE,SAAAA,CAAA,EAAW;YACdlB,gBAAgB,CAACK,IAAI,CAAC,yEAAyE,CAAC;UACpG,CAAC;UACDc,QAAQ,EAAE,SAAAA,CAAA,EAAW;YACjBlC,OAAO,CAACkB,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC;YAC/BlB,OAAO,CAACD,IAAI,CAAC,qBAAqB,CAAC;UACvC;QACJ,CAAC,CAAC;MACN,CAAC,CAAC;IACN;EACJ,CAAC;;EAED;EACAT,CAAC,CAACgB,QAAQ,CAAC,CAAC6B,KAAK,CAAC,YAAW;IACzB5C,YAAY,CAACC,IAAI,CAAC,CAAC;EACvB,CAAC,CAAC;AAEN,CAAC,EAAE4C,MAAM,CAAC,C", "sources": ["webpack://monoova-payments-for-woocommerce/./assets/js/admin.js"], "sourcesContent": ["/**\n * Monoova Admin JavaScript\n *\n * Handles interactions in the admin dashboard\n */\n(function($) {\n    'use strict';\n    \n    /**\n     * Monoova Admin object\n     */\n    var MonoovaAdmin = {\n        /**\n         * Initialize\n         */\n        init: function() {\n            this.setupCopyButtons();\n            this.setupAPITestButtons();\n        },\n        \n        /**\n         * Setup copy buttons\n         */\n        setupCopyButtons: function() {\n            $('.monoova-copy-webhook').on('click', function() {\n                var textToCopy = $(this).data('clipboard-text');\n                MonoovaAdmin.copyToClipboard(textToCopy, $(this));\n            });\n        },\n        \n        /**\n         * Copy text to clipboard\n         *\n         * @param {string} text     Text to copy\n         * @param {object} $button  jQuery button object\n         */\n        copyToClipboard: function(text, $button) {\n            // Create temporary input\n            var $temp = $(\"<input>\");\n            $(\"body\").append($temp);\n            $temp.val(text).select();\n            \n            // Execute copy command\n            var success = document.execCommand(\"copy\");\n            $temp.remove();\n            \n            // Show success/error message\n            if (success) {\n                var originalText = $button.text();\n                \n                $button.text(monoova_admin_params.i18n.success);\n                \n                setTimeout(function() {\n                    $button.text(originalText);\n                }, 2000);\n            }\n        },\n        \n        /**\n         * Setup API test buttons\n         */\n        setupAPITestButtons: function() {\n            $('.monoova-api-test-button').on('click', function(e) {\n                e.preventDefault();\n                \n                var $button = $(this);\n                var $resultContainer = $button.closest('tr').find('.monoova-api-test-result');\n                \n                // Disable button and show loading\n                $button.prop('disabled', true);\n                $button.text(monoova_admin_params.i18n.loading);\n                $resultContainer.html('');\n                \n                // API credentials from form\n                var mode = $('#woocommerce_monoova_card_testmode').is(':checked') ? 'test' : 'live';\n                var apiKey = mode === 'test' \n                    ? $('#woocommerce_monoova_card_test_api_key').val()\n                    : $('#woocommerce_monoova_card_live_api_key').val();\n                \n                if (!apiKey) {\n                    $resultContainer.html('<div class=\"monoova-test-error\">Please enter an API key.</div>');\n                    $button.prop('disabled', false);\n                    $button.text('Test API Connection');\n                    return;\n                }\n                \n                // Make AJAX call to test API\n                $.ajax({\n                    url: monoova_admin_params.ajax_url,\n                    method: 'POST',\n                    data: {\n                        action: 'monoova_test_api_connection',\n                        api_key: apiKey,\n                        mode: mode,\n                        nonce: monoova_admin_params.nonce\n                    },\n                    success: function(response) {\n                        if (response.success) {\n                            $resultContainer.html('<div class=\"monoova-test-success\">API connection successful!</div>');\n                        } else {\n                            $resultContainer.html('<div class=\"monoova-test-error\">API connection failed: ' + response.data.message + '</div>');\n                        }\n                    },\n                    error: function() {\n                        $resultContainer.html('<div class=\"monoova-test-error\">Request failed. Please try again.</div>');\n                    },\n                    complete: function() {\n                        $button.prop('disabled', false);\n                        $button.text('Test API Connection');\n                    }\n                });\n            });\n        },\n    };\n    \n    // Initialize on document ready\n    $(document).ready(function() {\n        MonoovaAdmin.init();\n    });\n    \n})(jQuery);"], "names": ["$", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "init", "setupCopyButtons", "setupAPITestButtons", "on", "textToCopy", "data", "copyToClipboard", "text", "$button", "$temp", "append", "val", "select", "success", "document", "execCommand", "remove", "originalText", "monoova_admin_params", "i18n", "setTimeout", "e", "preventDefault", "$resultContainer", "closest", "find", "prop", "loading", "html", "mode", "is", "<PERSON><PERSON><PERSON><PERSON>", "ajax", "url", "ajax_url", "method", "action", "api_key", "nonce", "response", "message", "error", "complete", "ready", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}