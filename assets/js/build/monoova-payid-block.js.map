{"version": 3, "file": "monoova-payid-block.js", "mappings": ";;;;;;;;;;;;;;;;AAAyD;;AAEzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAME,mBAAmB,GAAGA,CAAC;EAChCC,cAAc,GAAG,CAAC;EAClBC,WAAW;EACXC,OAAO,GAAG;AACd,CAAC,KAAK;EACF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGP,4DAAQ,CAACG,cAAc,CAAC;EAE1DF,6DAAS,CAAC,MAAM;IACZ,IAAIK,SAAS,IAAI,CAAC,EAAE;MAChB,IAAIF,WAAW,EAAE;QACbI,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAGN,WAAW;MACtC;MACA;IACJ;IAEA,MAAMO,KAAK,GAAGC,UAAU,CAAC,MAAM;MAC3BL,YAAY,CAACM,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IAClC,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMC,YAAY,CAACH,KAAK,CAAC;EACpC,CAAC,EAAE,CAACL,SAAS,EAAEF,WAAW,CAAC,CAAC;EAE5B,MAAMW,gBAAgB,GAAGV,OAAO,CAACW,OAAO,CAAC,aAAa,EAAEV,SAAS,CAACW,QAAQ,CAAC,CAAC,CAAC;EAE7E,oBACIC,KAAA,CAAAC,aAAA;IAAKC,SAAS,EAAC,8BAA8B;IAACC,KAAK,EAAE;MACjDC,SAAS,EAAE,MAAM;MACjBC,OAAO,EAAE,MAAM;MACfC,eAAe,EAAE,SAAS;MAC1BC,MAAM,EAAE,mBAAmB;MAC3BC,YAAY,EAAE,KAAK;MACnBC,SAAS,EAAE,QAAQ;MACnBC,QAAQ,EAAE,MAAM;MAChBC,KAAK,EAAE;IACX;EAAE,GACGd,gBACA,CAAC;AAEd,CAAC;;;;;;;;;;;;;;;;;;;AClDgE;AACJ;AACD;;AAE5D;AACA,MAAMkB,aAAa,GAAGA,CAAC;EAAEC,OAAO;EAAEC,IAAI,GAAG;AAAI,CAAC,KAAK;EAC/C,MAAMC,KAAK,GAAGN,0DAAM,CAAC,IAAI,CAAC;EAE1B7B,6DAAS,CAAC,MAAM;IACZ,IAAI,CAACiC,OAAO,IAAI,CAACE,KAAK,CAACC,OAAO,IAAI,OAAO7B,MAAM,CAAC8B,MAAM,KAAK,WAAW,EAAE;;IAExE;IACAF,KAAK,CAACC,OAAO,CAACE,SAAS,GAAG,EAAE;IAE5B,IAAI/B,MAAM,CAAC8B,MAAM,CAACF,KAAK,CAACC,OAAO,EAAE;MAC7BG,IAAI,EAAEN,OAAO;MACbO,KAAK,EAAEN,IAAI;MACXO,MAAM,EAAEP,IAAI;MACZQ,SAAS,EAAE,SAAS;MACpBC,UAAU,EAAE,SAAS;MACrBC,YAAY,EAAErC,MAAM,CAAC8B,MAAM,EAAEQ,YAAY,EAAEC;IAC/C,CAAC,CAAC;EACN,CAAC,EAAE,CAACb,OAAO,EAAEC,IAAI,CAAC,CAAC;EAEnB,oBAAOjB,KAAA,CAAAC,aAAA;IAAK6B,GAAG,EAAEZ,KAAM;IAACa,EAAE,EAAC;EAAiB,CAAE,CAAC;AACnD,CAAC;;AAED;AACA,MAAMC,UAAU,GAAGA,CAAC;EAAEV,IAAI;EAAEW;AAAO,CAAC,KAAK;EACrC,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGrD,4DAAQ,CAAC,KAAK,CAAC;EAE3C,MAAMsD,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACA,MAAMC,SAAS,CAACC,SAAS,CAACC,SAAS,CAACjB,IAAI,CAAC;MACzCa,SAAS,CAAC,IAAI,CAAC;MACfF,MAAM,IAAIA,MAAM,CAAC,CAAC;MAClBvC,UAAU,CAAC,MAAMyC,SAAS,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;IAC5C,CAAC,CAAC,OAAOK,GAAG,EAAE;MACVC,OAAO,CAACC,KAAK,CAAC,iBAAiB,EAAEF,GAAG,CAAC;IACzC;EACJ,CAAC;EAED,oBACIxC,KAAA,CAAAC,aAAA;IACI0C,IAAI,EAAC,QAAQ;IACbzC,SAAS,EAAC,qBAAqB;IAC/B0C,OAAO,EAAER,UAAW;IACpBS,KAAK,EAAEX,MAAM,GAAG,SAAS,GAAG;EAAO,gBAEnClC,KAAA,CAAAC,aAAA;IAAKsB,KAAK,EAAC,IAAI;IAACC,MAAM,EAAC,IAAI;IAACsB,OAAO,EAAC,WAAW;IAACC,IAAI,EAAC,MAAM;IAACC,KAAK,EAAC;EAA4B,gBAC1FhD,KAAA,CAAAC,aAAA;IAAMgD,CAAC,EAAC,sMAAsM;IAACC,MAAM,EAAC,SAAS;IAAC,gBAAa,KAAK;IAAC,kBAAe,OAAO;IAAC,mBAAgB;EAAO,CAAE,CAAC,eACpSlD,KAAA,CAAAC,aAAA;IAAMgD,CAAC,EAAC,4MAA4M;IAACC,MAAM,EAAC,SAAS;IAAC,gBAAa,KAAK;IAAC,kBAAe,OAAO;IAAC,mBAAgB;EAAO,CAAE,CACxS,CACD,CAAC;AAEjB,CAAC;;AAED;AACA,MAAMC,cAAc,GAAGA,CAAC;EAAEC,eAAe;EAAEC,SAAS;EAAEC;AAAQ,CAAC,KAAK;EAChE,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG1E,4DAAQ,CAAC,EAAE,CAAC;EAE5CC,6DAAS,CAAC,MAAM;IACZ,IAAI,CAACqE,eAAe,IAAIA,eAAe,IAAI,CAAC,EAAE;MAC1CI,WAAW,CAAC,EAAE,CAAC;MACf;IACJ;IAEA,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;MAC5B,MAAMC,QAAQ,GAAGN,eAAe,GAAG,IAAI,GAAG,IAAIO,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;MAE9D,IAAIF,QAAQ,GAAG,CAAC,EAAE;QACdF,WAAW,CAAC,EAAE,CAAC;QACfH,SAAS,IAAIA,SAAS,CAAC,CAAC;QACxB,OAAO,IAAI;MACf;MAEA,MAAMQ,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACL,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;MACzD,MAAMM,KAAK,GAAGF,IAAI,CAACC,KAAK,CAAEL,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,IAAK,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;MAC/E,MAAMO,OAAO,GAAGH,IAAI,CAACC,KAAK,CAAEL,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,IAAK,IAAI,GAAG,EAAE,CAAC,CAAC;MACvE,MAAMQ,OAAO,GAAGJ,IAAI,CAACC,KAAK,CAAEL,QAAQ,IAAI,IAAI,GAAG,EAAE,CAAC,GAAI,IAAI,CAAC;MAE3D,IAAIS,OAAO,GAAG,EAAE;MAChB,IAAIN,IAAI,GAAG,CAAC,EAAEM,OAAO,CAACC,IAAI,CAAC,GAAGP,IAAI,IAAIA,IAAI,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM,EAAE,CAAC;MACpE,IAAIG,KAAK,GAAG,CAAC,EAAEG,OAAO,CAACC,IAAI,CAAC,GAAGJ,KAAK,IAAIA,KAAK,KAAK,CAAC,GAAG,MAAM,GAAG,OAAO,EAAE,CAAC;MACzE,IAAIC,OAAO,GAAG,CAAC,EAAEE,OAAO,CAACC,IAAI,CAAC,GAAGH,OAAO,IAAIA,OAAO,KAAK,CAAC,GAAG,QAAQ,GAAG,SAAS,EAAE,CAAC;MACnF,IAAIC,OAAO,GAAG,CAAC,EAAEC,OAAO,CAACC,IAAI,CAAC,GAAGF,OAAO,IAAIA,OAAO,KAAK,CAAC,GAAG,QAAQ,GAAG,SAAS,EAAE,CAAC;MAEnF,OAAO,IAAIC,OAAO,CAACE,IAAI,CAAC,OAAO,CAAC,aAAa;IACjD,CAAC;;IAED;IACA,MAAMC,WAAW,GAAGb,iBAAiB,CAAC,CAAC;IACvC,IAAIa,WAAW,EAAE;MACbd,WAAW,CAACc,WAAW,CAAC;IAC5B;IAEA,MAAMC,QAAQ,GAAGC,WAAW,CAAC,MAAM;MAC/B,MAAMC,OAAO,GAAGhB,iBAAiB,CAAC,CAAC;MACnC,IAAIgB,OAAO,EAAE;QACTjB,WAAW,CAACiB,OAAO,CAAC;MACxB,CAAC,MAAM;QACHC,aAAa,CAACH,QAAQ,CAAC;MAC3B;IACJ,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMG,aAAa,CAACH,QAAQ,CAAC;EACxC,CAAC,EAAE,CAACnB,eAAe,EAAEC,SAAS,CAAC,CAAC;EAEhC,IAAI,CAACE,QAAQ,IAAIH,eAAe,GAAGO,IAAI,CAACgB,GAAG,CAAC,CAAC,GAAG,IAAI,EAAE,OAAO,IAAI;EAEjE,oBACI3E,KAAA,CAAAC,aAAA;IAAK8B,EAAE,EAAC,qBAAqB;IAAC7B,SAAS,EAAC;EAAqB,gBACzDF,KAAA,CAAAC,aAAA;IAAMC,SAAS,EAAC;EAAsB,GAAEoD,OAAO,CAACsB,UAAiB,CAAC,eAClE5E,KAAA,CAAAC,aAAA;IAAMC,SAAS,EAAC;EAAqB,gBACjCF,KAAA,CAAAC,aAAA,iBAAS,IAAI0D,IAAI,CAACP,eAAe,GAAG,IAAI,CAAC,CAACyB,cAAc,CAAC,CAAC,EAAC,GAAC,EAACtB,QAAiB,CAC5E,CACL,CAAC;AAEd,CAAC;;AAED;AACO,MAAMuB,0BAA0B,GAAGA,CAAC;EACvCC,YAAY;EACZC,QAAQ;EACRC,aAAa,GAAG,SAAS;EACzBC,UAAU;EACVC,mBAAmB,GAAG,IAAI;EAC1BC,qBAAqB;EACrBC,wBAAwB;EAAE;EAC1BC,OAAO,GAAG,IAAI,CAAC;AACnB,CAAC,KAAK;EACF,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAG1G,4DAAQ,CAAC,OAAO,CAAC;EAE7D,IAAI,CAACiG,YAAY,EAAE,OAAO,IAAI;EAE9B,MAAM;IAAEU,OAAO;IAAEC;EAAe,CAAC,GAAGX,YAAY;EAChD,MAAMY,SAAS,GAAGX,QAAQ,CAACY,aAAa,CAACC,QAAQ,CAAC,OAAO,CAAC,IAAIJ,OAAO,CAACK,WAAW;EACjF,MAAMC,QAAQ,GAAGf,QAAQ,CAACY,aAAa,CAACC,QAAQ,CAAC,eAAe,CAAC,IAAIJ,OAAO,CAACO,QAAQ,IAAIP,OAAO,CAACQ,mBAAmB;EACpH,MAAMC,kBAAkB,GAAGP,SAAS,IAAII,QAAQ;;EAEhD;EACAhH,6DAAS,CAAC,MAAM;IACZ,IAAI4G,SAAS,IAAIJ,cAAc,KAAK,OAAO,IAAIA,cAAc,KAAK,eAAe,EAAE;MAC/EC,iBAAiB,CAAC,OAAO,CAAC;IAC9B,CAAC,MAAM,IAAI,CAACG,SAAS,IAAII,QAAQ,EAAE;MAC/BP,iBAAiB,CAAC,eAAe,CAAC;IACtC;EACJ,CAAC,EAAE,CAACG,SAAS,EAAEI,QAAQ,EAAER,cAAc,CAAC,CAAC;EAEzC,MAAMY,aAAa,GAAGA,CAAA,KAAM;IACxBf,qBAAqB,IAAIA,qBAAqB,CAAC,SAAS,CAAC;EAC7D,CAAC;EAED,MAAMgB,mBAAmB,GAAGA,CAAA,KAAM;IAC9B,IAAIf,wBAAwB,EAAE;MAC1BA,wBAAwB,CAAC,CAAC;IAC9B;IACAD,qBAAqB,CAAC,SAAS,CAAC;EACpC,CAAC;EAED,MAAMiB,cAAc,GAAGA,CAAA,KAAM;IACzB;IACAjB,qBAAqB,CAAC,SAAS,CAAC;EACpC,CAAC;;EAED;EACA,MAAMkB,wBAAwB,GAAGA,CAAA,KAAM;IACnC,IAAIZ,cAAc,EAAE;MAChB,OAAOA,cAAc;IACzB;;IAEA;IACA,MAAMa,SAAS,GAAG,IAAIC,eAAe,CAAClH,MAAM,CAACC,QAAQ,CAACkH,MAAM,CAAC;IAC7D,MAAMC,QAAQ,GAAGH,SAAS,CAACI,GAAG,CAAC,KAAK,CAAC;;IAErC;IACA,IAAIC,gBAAgB,GAAG5B,QAAQ,CAAC6B,kBAAkB;IAClD,IAAID,gBAAgB,CAACf,QAAQ,CAAC,GAAG,CAAC,EAAE;MAChCe,gBAAgB,IAAI,mBAAmBtB,OAAO,EAAE;IACpD,CAAC,MAAM;MACHsB,gBAAgB,IAAI,mBAAmBtB,OAAO,EAAE;IACpD;IAEA,IAAIoB,QAAQ,EAAE;MACVE,gBAAgB,IAAI,QAAQF,QAAQ,EAAE;IAC1C;IAEA,OAAOE,gBAAgB;EAC3B,CAAC;EAED,MAAME,mBAAmB,GAAGA,CAAA,KAAM;IAC9B,QAAQ7B,aAAa;MACjB,KAAK,MAAM;QACP,oBACIjF,KAAA,CAAAC,aAAA;UAAK8B,EAAE,EAAC,2BAA2B;UAAC7B,SAAS,EAAC;QAAwB,gBAClEF,KAAA,CAAAC,aAAA,2BACID,KAAA,CAAAC,aAAA;UAAKsB,KAAK,EAAC,IAAI;UAACC,MAAM,EAAC,IAAI;UAACsB,OAAO,EAAC,WAAW;UAACC,IAAI,EAAC,MAAM;UAACC,KAAK,EAAC;QAA4B,gBAC1FhD,KAAA,CAAAC,aAAA;UAAM,aAAU,SAAS;UAAC,aAAU,SAAS;UAACgD,CAAC,EAAC,ijBAAijB;UAACF,IAAI,EAAC;QAAS,CAAC,CAChnB,CACJ,CAAC,eACN/C,KAAA,CAAAC,aAAA;UAAIC,SAAS,EAAC;QAAO,GAAE8E,QAAQ,CAAC1B,OAAO,CAACyD,iBAAsB,CAAC,eAC/D/G,KAAA,CAAAC,aAAA,YAAI+E,QAAQ,CAAC1B,OAAO,CAAC0D,yBAA6B,CAAC,eACnDhH,KAAA,CAAAC,aAAA;UAAKC,SAAS,EAAC;QAAgC,gBAC3CF,KAAA,CAAAC,aAAA,CAACa,yDAAM;UAACmG,OAAO,EAAC,SAAS;UAACzH,IAAI,EAAE8G,wBAAwB,CAAC;QAAE,GACtDtB,QAAQ,CAAC1B,OAAO,CAAC4D,kBACd,CACP,CAAC,eACNlH,KAAA,CAAAC,aAAA,CAACjB,qEAAmB;UAChBC,cAAc,EAAE,CAAE;UAClBC,WAAW,EAAEoH,wBAAwB,CAAC,CAAE;UACxCnH,OAAO,EAAE6F,QAAQ,CAAC1B,OAAO,CAAC6D;QAA0B,CACvD,CACA,CAAC;MAEd,KAAK,SAAS;QACV,oBACInH,KAAA,CAAAC,aAAA;UAAK8B,EAAE,EAAC,yBAAyB;UAAC7B,SAAS,EAAC;QAAwB,gBAChEF,KAAA,CAAAC,aAAA,2BACID,KAAA,CAAAC,aAAA;UAAKsB,KAAK,EAAC,IAAI;UAACC,MAAM,EAAC,IAAI;UAACsB,OAAO,EAAC,WAAW;UAACC,IAAI,EAAC,MAAM;UAACC,KAAK,EAAC;QAA4B,gBAC1FhD,KAAA,CAAAC,aAAA;UAAM,aAAU,SAAS;UAAC,aAAU,SAAS;UAACgD,CAAC,EAAC,unBAAunB;UAACF,IAAI,EAAC;QAAS,CAAC,CACtrB,CAEJ,CAAC,eACN/C,KAAA,CAAAC,aAAA;UAAIC,SAAS,EAAC;QAAO,GAAE8E,QAAQ,CAAC1B,OAAO,CAAC8D,eAAoB,CAAC,eAC7DpH,KAAA,CAAAC,aAAA,YAAI+E,QAAQ,CAAC1B,OAAO,CAAC+D,uBAA2B,CAAC,eACjDrH,KAAA,CAAAC,aAAA;UAAKC,SAAS,EAAC;QAAgC,gBAC3CF,KAAA,CAAAC,aAAA,CAACa,yDAAM;UAACmG,OAAO,EAAC,SAAS;UAACrE,OAAO,EAAEA,CAAA,KAAMwD,mBAAmB,CAAC;QAAE,GAC1DpB,QAAQ,CAAC1B,OAAO,CAACgE,eACd,CACP,CACJ,CAAC;MAEd,KAAK,QAAQ;QACT,oBACItH,KAAA,CAAAC,aAAA;UAAK8B,EAAE,EAAC,0BAA0B;UAAC7B,SAAS,EAAC;QAAwB,gBACjEF,KAAA,CAAAC,aAAA,2BACID,KAAA,CAAAC,aAAA;UAAKsB,KAAK,EAAC,IAAI;UAACC,MAAM,EAAC,IAAI;UAACsB,OAAO,EAAC,WAAW;UAACC,IAAI,EAAC,MAAM;UAACC,KAAK,EAAC;QAA4B,gBAC1FhD,KAAA,CAAAC,aAAA;UAAM,aAAU,SAAS;UAAC,aAAU,SAAS;UAACgD,CAAC,EAAC,unBAAunB;UAACF,IAAI,EAAC;QAAS,CAAC,CACtrB,CACJ,CAAC,eACN/C,KAAA,CAAAC,aAAA;UAAIC,SAAS,EAAC;QAAO,GAAE8E,QAAQ,CAAC1B,OAAO,CAACiE,cAAmB,CAAC,eAC5DvH,KAAA,CAAAC,aAAA,YAAI+E,QAAQ,CAAC1B,OAAO,CAACkE,sBAA0B,CAAC,eAChDxH,KAAA,CAAAC,aAAA;UAAG8B,EAAE,EAAC,0BAA0B;UAAC5B,KAAK,EAAE;YAAEsH,SAAS,EAAE,QAAQ;YAAErH,SAAS,EAAE;UAAG;QAAE,GAAC,UACpE,eAAAJ,KAAA,CAAAC,aAAA;UAAMyH,uBAAuB,EAAE;YAAEC,MAAM,EAAExC;UAAoB;QAAE,CAAE,CAC1E,CAAC,eACJnF,KAAA,CAAAC,aAAA;UAAKC,SAAS,EAAC;QAAgC,gBAC3CF,KAAA,CAAAC,aAAA,CAACa,yDAAM;UAACmG,OAAO,EAAC,SAAS;UAACrE,OAAO,EAAEA,CAAA,KAAMyD,cAAc,CAAC;QAAE,GACrDrB,QAAQ,CAAC1B,OAAO,CAACsE,SACd,CACP,CACJ,CAAC;MAEd;QACI,OAAO,IAAI;IACnB;EACJ,CAAC;EAED,IAAI3C,aAAa,KAAK,SAAS,IAAIA,aAAa,KAAK,SAAS,EAAE;IAC5D,oBACIjF,KAAA,CAAAC,aAAA;MAAKC,SAAS,EAAC;IAAkD,gBAC7DF,KAAA,CAAAC,aAAA;MAAKC,SAAS,EAAC;IAAgC,GAC1C4G,mBAAmB,CAAC,CACpB,CACJ,CAAC;EAEd;EAEA,oBACI9G,KAAA,CAAAC,aAAA;IAAKC,SAAS,EAAC;EAAkD,GAEzDgG,kBAAkB,iBACdlG,KAAA,CAAAC,aAAA;IAAKC,SAAS,EAAC;EAAsC,gBACjDF,KAAA,CAAAC,aAAA,aAAK+E,QAAQ,CAAC1B,OAAO,CAACuE,QAAa,CAAC,eACpC7H,KAAA,CAAAC,aAAA,CAACY,+DAAY;IACTiH,QAAQ,EAAEvC,cAAe;IACzBwC,OAAO,EAAE,CACL;MAAEC,KAAK,EAAEhD,QAAQ,CAAC1B,OAAO,CAAC2E,YAAY;MAAEC,KAAK,EAAE;IAAQ,CAAC,EACxD;MAAEF,KAAK,EAAEhD,QAAQ,CAAC1B,OAAO,CAAC6E,WAAW;MAAED,KAAK,EAAE;IAAgB,CAAC,CACjE;IACFE,QAAQ,EAAGC,CAAC,IAAK7C,iBAAiB,CAAC6C,CAAC;EAAE,CACzC,CACA,CACR,eAGLrI,KAAA,CAAAC,aAAA,cAAM+E,QAAQ,CAAC1B,OAAO,CAACgF,gCAAsC,CAAC,eAC9DtI,KAAA,CAAAC,aAAA;IAAKC,SAAS,EAAC;EAAgC,gBAC3CF,KAAA,CAAAC,aAAA;IAAK8B,EAAE,EAAC;EAAyB,GAG5BiD,QAAQ,CAACD,YAAY,iBAClB/E,KAAA,CAAAC,aAAA;IACIyH,uBAAuB,EAAE;MAAEC,MAAM,EAAE3C,QAAQ,CAACD;IAAa;EAAE,CAC9D,CACJ,EAGAQ,cAAc,KAAK,OAAO,IAAII,SAAS,iBACpC3F,KAAA,CAAAC,aAAA;IAAK8B,EAAE,EAAC;EAAoB,gBACxB/B,KAAA,CAAAC,aAAA;IAAKC,SAAS,EAAC;EAAkB,gBAE7BF,KAAA,CAAAC,aAAA,aAAK+E,QAAQ,CAAC1B,OAAO,CAACiF,QAAa,CAAC,eACpCvI,KAAA,CAAAC,aAAA;IACIC,SAAS,EAAC,QAAQ;IAClBwH,uBAAuB,EAAE;MAAEC,MAAM,EAAElC,OAAO,CAAC+C;IAAwB;EAAE,CACxE,CAEA,CAAC,eACNxI,KAAA,CAAAC,aAAA,YAAI+E,QAAQ,CAAC1B,OAAO,CAACmF,oBAAwB,CAAC,eAc9CzI,KAAA,CAAAC,aAAA;IAAKC,SAAS,EAAC;EAAoB,gBAC/BF,KAAA,CAAAC,aAAA;IACIyI,GAAG,EAAE,GAAG1D,QAAQ,CAAC2D,UAAU,8BAA+B;IAC1DC,GAAG,EAAC,OAAO;IACX1I,SAAS,EAAC;EAAY,CACzB,CAAC,eACFF,KAAA,CAAAC,aAAA;IAAMC,SAAS,EAAC,aAAa;IAAC,gBAAa;EAAO,GAC7CuF,OAAO,CAACK,WACP,CAAC,eACP9F,KAAA,CAAAC,aAAA,CAAC+B,UAAU;IAACV,IAAI,EAAEmE,OAAO,CAACK;EAAY,CAAE,CACvC,CAAC,EAELd,QAAQ,CAAC6D,oBAAoB,iBAC1B7I,KAAA,CAAAC,aAAA;IAAKC,SAAS,EAAC;EAAmC,gBAC9CF,KAAA,CAAAC,aAAA,aAAK+E,QAAQ,CAAC1B,OAAO,CAACwF,iBAAsB,CAAC,eAC7C9I,KAAA,CAAAC,aAAA,YAAI+E,QAAQ,CAAC1B,OAAO,CAACyF,8BAAkC,CAAC,eACxD/I,KAAA,CAAAC,aAAA;IAAKC,SAAS,EAAC;EAAoC,gBAC/CF,KAAA,CAAAC,aAAA;IAAKC,SAAS,EAAC;EAAa,GAAE8E,QAAQ,CAAC1B,OAAO,CAAC0F,SAAe,CAAC,eAC/DhJ,KAAA,CAAAC,aAAA;IAAKC,SAAS,EAAC;EAAqB,gBAChCF,KAAA,CAAAC,aAAA;IAAMC,SAAS,EAAC,aAAa;IAAC,gBAAa;EAAW,GACjDuF,OAAO,CAACuD,SAAS,GAAG,GAAG,EAAC,GACvB,CAAC,eACPhJ,KAAA,CAAAC,aAAA,CAAC+B,UAAU;IAACV,IAAI,EAAEmE,OAAO,CAACuD,SAAS,GAAG;EAAI,CAAE,CAC3C,CACJ,CACJ,CAER,CACR,EAGAzD,cAAc,KAAK,eAAe,IAAIQ,QAAQ,iBAC3C/F,KAAA,CAAAC,aAAA;IAAK8B,EAAE,EAAC;EAAmB,gBACvB/B,KAAA,CAAAC,aAAA,aAAK+E,QAAQ,CAAC1B,OAAO,CAAC2F,qBAA0B,CAAC,eAEjDjJ,KAAA,CAAAC,aAAA;IAAKC,SAAS,EAAC;EAAyB,gBACpCF,KAAA,CAAAC,aAAA;IAAKC,SAAS,EAAC;EAAoB,gBAC/BF,KAAA,CAAAC,aAAA;IAAKC,SAAS,EAAC;EAAa,GAAE8E,QAAQ,CAAC1B,OAAO,CAAC4F,YAAkB,CAAC,eAClElJ,KAAA,CAAAC,aAAA;IAAKC,SAAS,EAAC;EAAqB,gBAChCF,KAAA,CAAAC,aAAA;IAAMC,SAAS,EAAC,aAAa;IAAC,gBAAa;EAAc,GACpDuF,OAAO,CAAC0D,iBACP,CAEL,CACJ,CAAC,eAENnJ,KAAA,CAAAC,aAAA;IAAKC,SAAS,EAAC;EAAoB,gBAC/BF,KAAA,CAAAC,aAAA;IAAKC,SAAS,EAAC;EAAa,GAAE8E,QAAQ,CAAC1B,OAAO,CAAC8F,GAAS,CAAC,eACzDpJ,KAAA,CAAAC,aAAA;IAAKC,SAAS,EAAC;EAAqB,gBAChCF,KAAA,CAAAC,aAAA;IAAMC,SAAS,EAAC,aAAa;IAAC,gBAAa;EAAK,GAC3CuF,OAAO,CAACO,QACP,CAAC,eACPhG,KAAA,CAAAC,aAAA,CAAC+B,UAAU;IAACV,IAAI,EAAEmE,OAAO,CAACO;EAAS,CAAE,CACpC,CACJ,CACJ,CAAC,eACNhG,KAAA,CAAAC,aAAA;IAAKC,SAAS,EAAC;EAA+B,gBAC1CF,KAAA,CAAAC,aAAA;IAAKC,SAAS,EAAC;EAAa,GAAE8E,QAAQ,CAAC1B,OAAO,CAAC+F,cAAoB,CAAC,eACpErJ,KAAA,CAAAC,aAAA;IAAKC,SAAS,EAAC;EAAqB,gBAChCF,KAAA,CAAAC,aAAA;IAAMC,SAAS,EAAC,aAAa;IAAC,gBAAa;EAAgB,GACtDuF,OAAO,CAACQ,mBACP,CAAC,eACPjG,KAAA,CAAAC,aAAA,CAAC+B,UAAU;IAACV,IAAI,EAAEmE,OAAO,CAACQ;EAAoB,CAAE,CAC/C,CACJ,CAAC,EAELjB,QAAQ,CAAC6D,oBAAoB,iBAC1B7I,KAAA,CAAAC,aAAA;IAAKC,SAAS,EAAC;EAAmC,gBAC9CF,KAAA,CAAAC,aAAA,aAAK+E,QAAQ,CAAC1B,OAAO,CAACwF,iBAAsB,CAAC,eAC7C9I,KAAA,CAAAC,aAAA,YAAI+E,QAAQ,CAAC1B,OAAO,CAACyF,8BAAkC,CAAC,eACxD/I,KAAA,CAAAC,aAAA;IAAKC,SAAS,EAAC;EAA+B,gBAC1CF,KAAA,CAAAC,aAAA;IAAKC,SAAS,EAAC;EAAa,GAAE8E,QAAQ,CAAC1B,OAAO,CAAC0F,SAAe,CAAC,eAC/DhJ,KAAA,CAAAC,aAAA;IAAKC,SAAS,EAAC;EAAqB,gBAChCF,KAAA,CAAAC,aAAA;IAAMC,SAAS,EAAC,aAAa;IAAC,gBAAa;EAAW,GACjDuF,OAAO,CAACuD,SAAS,GAAG,GAAG,EAAC,GACvB,CAAC,eACPhJ,KAAA,CAAAC,aAAA,CAAC+B,UAAU;IAACV,IAAI,EAAEmE,OAAO,CAACuD,SAAS,GAAG;EAAI,CAAE,CAC3C,CACJ,CACJ,CAER,CACR,eAEDhJ,KAAA,CAAAC,aAAA;IAAKC,SAAS,EAAC;EAA2B,gBACtCF,KAAA,CAAAC,aAAA,YAAI+E,QAAQ,CAAC1B,OAAO,CAACgG,+BAAmC,CACvD,CAAC,EAELpE,UAAU,iBACPlF,KAAA,CAAAC,aAAA,CAACkD,cAAc;IACXC,eAAe,EAAE8B,UAAW;IAC5B7B,SAAS,EAAE8C,aAAc;IACzB7C,OAAO,EAAE0B,QAAQ,CAAC1B;EAAQ,CAC7B,CAEJ,CACJ,CACJ,CAAC;AAEd,CAAC;;;;;;;;;;;;;;;;;ACra6E;AACgB;AAEvF,MAAMmG,2BAA2B,GAAGA,CAAC;EACxCzE,QAAQ;EACR0E,OAAO;EACPC,YAAY;EACZrE,OAAO,EAAEsE,eAAe;EAAE;EAC1BC,WAAW,GAAG,8BAA8B;EAC5CC,eAAe,GAAG,eAAe;EACjCC,eAAe,GAAG,IAAI,CAAC;AAC3B,CAAC,KAAK;EACF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGnL,4DAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACiG,YAAY,EAAEmF,eAAe,CAAC,GAAGpL,4DAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC4D,KAAK,EAAEyH,QAAQ,CAAC,GAAGrL,4DAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACwG,OAAO,EAAE8E,UAAU,CAAC,GAAGtL,4DAAQ,CAAC8K,eAAe,IAAI,IAAI,CAAC,CAAC,CAAC;EACjE,MAAM,CAAC3E,aAAa,EAAEoF,gBAAgB,CAAC,GAAGvL,4DAAQ,CAAC,SAAS,CAAC;EAC7D,MAAM,CAACqG,mBAAmB,EAAEmF,sBAAsB,CAAC,GAAGxL,4DAAQ,CAAC,IAAI,CAAC;EACpE,MAAM,CAACoG,UAAU,EAAEqF,aAAa,CAAC,GAAGzL,4DAAQ,CAAC,IAAI,CAAC;;EAElD;EACA,MAAM0L,2BAA2B,GAAG5J,0DAAM,CAAC,KAAK,CAAC;EACjD,MAAM6J,kBAAkB,GAAG7J,0DAAM,CAAC,IAAI,CAAC;EACvC,MAAM8J,oBAAoB,GAAG9J,0DAAM,CAAC,IAAI,CAAC;;EAEzC;EACA,MAAM;IACF+J,SAAS;IACTC,gBAAgB;IAChBC,iBAAiB;IACjBC,sBAAsB;IACtBC,uBAAuB;IACvBC,YAAY;IACZC,YAAY;IACZC,cAAc;IAAE;IAChBC,aAAa;IACbC;EACJ,CAAC,GAAG5B,2GAAoC,CAACM,eAAe,EAAED,WAAW,CAAC;;EAEtE;EACA,MAAMwB,cAAc,GAAGJ,YAAY,CAAC,CAAC;EACrC,MAAMK,sBAAsB,GAAGD,cAAc,EAAEtG,YAAY;EAC3D,MAAMwG,iBAAiB,GAAGF,cAAc,EAAE/F,OAAO;;EAEjD;EACA,MAAMkG,6BAA6B,GAAGV,sBAAsB,IAAIQ,sBAAsB;EAEtF,MAAMG,WAAW,GAAGlC,+DAAW,CAAC,MAAM;IAClC,IAAIkB,kBAAkB,CAACtJ,OAAO,EAAE;MAC5BuD,aAAa,CAAC+F,kBAAkB,CAACtJ,OAAO,CAAC;MACzCsJ,kBAAkB,CAACtJ,OAAO,GAAG,IAAI;IACrC;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMuK,wBAAwB,GAAGnC,+DAAW,CAAC,OAAOoC,cAAc,GAAG,KAAK,KAAK;IAC3E1B,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,IAAI,CAAC;IACdK,2BAA2B,CAACrJ,OAAO,GAAG,IAAI;IAE1C,IAAI;MACAsB,OAAO,CAACmJ,GAAG,CAAC,uBAAuBD,cAAc,GAAG,cAAc,GAAG,YAAY,kCAAkC,EAAE/B,eAAe,CAAC;MAErI,MAAMiC,gBAAgB,GAAG,IAAIC,QAAQ,CAAC,CAAC;MACvCD,gBAAgB,CAACE,MAAM,CAAC,QAAQ,EAAE,2DAA2D,CAAC;MAC9FF,gBAAgB,CAACE,MAAM,CAAC,UAAU,EAAEnC,eAAe,CAAC;MACpDiC,gBAAgB,CAACE,MAAM,CAAC,OAAO,EAAE/G,QAAQ,CAACgH,2BAA2B,CAAC;MAEtE,IAAIL,cAAc,EAAE;QAChBE,gBAAgB,CAACE,MAAM,CAAC,YAAY,EAAE,MAAM,CAAC;MACjD;MAEA,MAAME,oBAAoB,GAAG,MAAMC,KAAK,CAAClH,QAAQ,CAACmH,QAAQ,EAAE;QACxDC,MAAM,EAAE,MAAM;QACdC,IAAI,EAAER;MACV,CAAC,CAAC;MAEF,MAAMS,kBAAkB,GAAG,MAAML,oBAAoB,CAACM,IAAI,CAAC,CAAC;MAE5D,IAAI,CAACD,kBAAkB,CAACE,OAAO,EAAE;QAC7B,MAAM,IAAIC,KAAK,CAACH,kBAAkB,CAACI,IAAI,EAAEvN,OAAO,IAAI,aAAawM,cAAc,GAAG,YAAY,GAAG,UAAU,uBAAuB,CAAC;MACvI;MAEA,MAAMgB,mBAAmB,GAAGL,kBAAkB,CAACI,IAAI;MACnDxC,eAAe,CAACyC,mBAAmB,CAAC;MACpCvC,UAAU,CAACR,eAAe,CAAC;MAE3B,IAAI+C,mBAAmB,CAACC,gBAAgB,EAAE;QACtCrC,aAAa,CAACoC,mBAAmB,CAACC,gBAAgB,CAAC;MACvD;MAEA5B,YAAY,CAACpB,eAAe,EAAE,IAAI,EAAE+C,mBAAmB,CAAC;MACxD5B,uBAAuB,CAAC,CAAC;IAE7B,CAAC,CAAC,OAAOrI,KAAK,EAAE;MACZD,OAAO,CAACC,KAAK,CAAC,6BAA6BiJ,cAAc,GAAG,cAAc,GAAG,YAAY,gBAAgB,EAAEjJ,KAAK,CAAC;MACjHyH,QAAQ,CAACzH,KAAK,CAACvD,OAAO,CAAC;IAC3B,CAAC,SAAS;MACN8K,YAAY,CAAC,KAAK,CAAC;MACnBO,2BAA2B,CAACrJ,OAAO,GAAG,KAAK;IAC/C;EACJ,CAAC,EAAE,CACCyI,eAAe,EACf5E,QAAQ,EACRgG,YAAY,EACZD,uBAAuB,CAC1B,CAAC;;EAEF;EACA,MAAM8B,2BAA2B,GAAGtD,+DAAW,CAAC,YAAY;IACxD,IAAIiB,2BAA2B,CAACrJ,OAAO,IAAI4D,YAAY,IAAIuG,sBAAsB,EAAE;MAC/E;IACJ;IAEA,IAAI,CAAC1B,eAAe,IAAI,CAACG,eAAe,EAAE;MACtC;IACJ;IAEA,MAAM2B,wBAAwB,CAAC,KAAK,CAAC;EACzC,CAAC,EAAE,CACC9B,eAAe,EACfG,eAAe,EACfhF,YAAY,EACZuG,sBAAsB,EACtBI,wBAAwB,CAC3B,CAAC;EAEF,MAAMoB,6BAA6B,GAAGvD,+DAAW,CAAC,YAAY;IAC1D;IACAkC,WAAW,CAAC,CAAC;IACb,IAAIf,oBAAoB,CAACvJ,OAAO,EAAE;MAC9BuD,aAAa,CAACgG,oBAAoB,CAACvJ,OAAO,CAAC;MAC3CuJ,oBAAoB,CAACvJ,OAAO,GAAG,IAAI;IACvC;;IAEA;IACA+I,eAAe,CAAC,IAAI,CAAC;IACrBG,gBAAgB,CAAC,SAAS,CAAC;IAC3BC,sBAAsB,CAAC,IAAI,CAAC;IAC5BC,aAAa,CAAC,IAAI,CAAC;;IAEnB;IACA,IAAIW,cAAc,EAAE;MAChBA,cAAc,CAAC,CAAC;IACpB;IAEA,MAAMQ,wBAAwB,CAAC,IAAI,CAAC;EACxC,CAAC,EAAE,CACC9B,eAAe,EACfsB,cAAc,EACdO,WAAW,EACXC,wBAAwB,CAC3B,CAAC;;EAEF;EACA,MAAMqB,kBAAkB,GAAGxD,+DAAW,CAAC,YAAY;IAC/C,MAAMyD,cAAc,GAAG1H,OAAO,IAAIiG,iBAAiB,IAAI3B,eAAe;IACtE,IAAI,CAACoD,cAAc,EAAE;;IAErB;IACA,MAAMC,mBAAmB,GAAGlI,YAAY,IAAIuG,sBAAsB;IAClE,MAAM5E,QAAQ,GAAGuG,mBAAmB,EAAExH,OAAO,EAAEyH,SAAS;IACxD,MAAMC,gBAAgB,GAAGF,mBAAmB,EAAEG,kBAAkB;IAEhE,IAAI,CAAC1G,QAAQ,IAAI,CAACyG,gBAAgB,EAAE;MAChC1K,OAAO,CAAC4K,IAAI,CAAC,iEAAiE,CAAC;MAC/E;IACJ;IAEA,IAAI;MACA,MAAMC,UAAU,GAAG,IAAIxB,QAAQ,CAAC,CAAC;MACjCwB,UAAU,CAACvB,MAAM,CAAC,QAAQ,EAAE,8BAA8B,CAAC;MAC3DuB,UAAU,CAACvB,MAAM,CAAC,UAAU,EAAEiB,cAAc,CAAC;MAC7CM,UAAU,CAACvB,MAAM,CAAC,WAAW,EAAErF,QAAQ,CAAC,CAAC,CAAC;MAC1C4G,UAAU,CAACvB,MAAM,CAAC,OAAO,EAAEoB,gBAAgB,CAAC,CAAC,CAAC;;MAG9C,MAAMI,QAAQ,GAAG,MAAMrB,KAAK,CAAClH,QAAQ,CAACmH,QAAQ,EAAE;QAC5CC,MAAM,EAAE,MAAM;QACdC,IAAI,EAAEiB;MACV,CAAC,CAAC;MAEF,MAAME,MAAM,GAAG,MAAMD,QAAQ,CAAChB,IAAI,CAAC,CAAC;MACpC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA,IAAIiB,MAAM,CAAChB,OAAO,IAAIgB,MAAM,CAACd,IAAI,CAACe,MAAM,KAAK,SAAS,EAAE;QACpDpD,gBAAgB,CAACmD,MAAM,CAACd,IAAI,CAACe,MAAM,CAAC;QACpCnD,sBAAsB,CAACkD,MAAM,CAACd,IAAI,CAACgB,MAAM,IAAI,IAAI,CAAC;QAClDjC,WAAW,CAAC,CAAC;MACjB;IACJ,CAAC,CAAC,OAAO/I,KAAK,EAAE;MACZD,OAAO,CAACC,KAAK,CAAC,oDAAoD,EAAEA,KAAK,CAAC;IAC9E;EACJ,CAAC,EAAE,CAAC4C,OAAO,EAAEiG,iBAAiB,EAAE3B,eAAe,EAAE7E,YAAY,EAAEuG,sBAAsB,EAAEtG,QAAQ,EAAEyG,WAAW,CAAC,CAAC;;EAE9G;EACA,MAAMkC,YAAY,GAAGpE,+DAAW,CAAC,MAAM;IACnC,IAAIkB,kBAAkB,CAACtJ,OAAO,EAAE;;IAEhC;IACA4L,kBAAkB,CAAC,CAAC;IACpB;IACAtC,kBAAkB,CAACtJ,OAAO,GAAGqD,WAAW,CAACuI,kBAAkB,EAAE,KAAK,CAAC;EACvE,CAAC,EAAE,CAACA,kBAAkB,CAAC,CAAC;;EAGxB;EACA,MAAMa,cAAc,GAAGrE,+DAAW,CAAC,MAAM;IACrC,IAAI,CAACrE,UAAU,IAAIwF,oBAAoB,CAACvJ,OAAO,EAAE;IAEjDuJ,oBAAoB,CAACvJ,OAAO,GAAGqD,WAAW,CAAC,MAAM;MAC7C,MAAMd,QAAQ,GAAGwB,UAAU,GAAG,IAAI,GAAG,IAAIvB,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;MACzD,IAAIF,QAAQ,GAAG,CAAC,EAAE;QACd2G,gBAAgB,CAAC,SAAS,CAAC;QAC3B3F,aAAa,CAACgG,oBAAoB,CAACvJ,OAAO,CAAC;QAC3CuJ,oBAAoB,CAACvJ,OAAO,GAAG,IAAI;QACnCsK,WAAW,CAAC,CAAC;MACjB;IACJ,CAAC,EAAE,IAAI,CAAC;EACZ,CAAC,EAAE,CAACvG,UAAU,EAAEuG,WAAW,CAAC,CAAC;;EAE7B;EACA,MAAMoC,kBAAkB,GAAGtE,+DAAW,CAAC,YAAY;IAC/C,MAAMyD,cAAc,GAAG1H,OAAO,IAAIiG,iBAAiB,IAAI3B,eAAe;IACtE,IAAI,CAACoD,cAAc,EAAE;MACjBvK,OAAO,CAAC4K,IAAI,CAAC,oEAAoE,CAAC;MAClF;IACJ;IAEA,IAAI;MACA;MACA,MAAMJ,mBAAmB,GAAGlI,YAAY,IAAIuG,sBAAsB;MAClE,MAAM5E,QAAQ,GAAGuG,mBAAmB,EAAExH,OAAO,EAAEyH,SAAS;MAExD,IAAI,CAACxG,QAAQ,EAAE;QACXjE,OAAO,CAAC4K,IAAI,CAAC,wDAAwD,CAAC;QACtE;MACJ;MAEA,MAAMS,SAAS,GAAG,IAAIhC,QAAQ,CAAC,CAAC;MAChCgC,SAAS,CAAC/B,MAAM,CAAC,QAAQ,EAAE,8BAA8B,CAAC;MAC1D+B,SAAS,CAAC/B,MAAM,CAAC,UAAU,EAAEiB,cAAc,CAAC;MAC5Cc,SAAS,CAAC/B,MAAM,CAAC,WAAW,EAAErF,QAAQ,CAAC;MACvCoH,SAAS,CAAC/B,MAAM,CAAC,OAAO,EAAE/G,QAAQ,CAAC+I,0BAA0B,CAAC;MAE9D,MAAMR,QAAQ,GAAG,MAAMrB,KAAK,CAAClH,QAAQ,CAACmH,QAAQ,EAAE;QAC5CC,MAAM,EAAE,MAAM;QACdC,IAAI,EAAEyB;MACV,CAAC,CAAC;MAEF,MAAMN,MAAM,GAAG,MAAMD,QAAQ,CAAChB,IAAI,CAAC,CAAC;MAEpC,IAAIiB,MAAM,CAAChB,OAAO,EAAE;QAChB;QACAnC,gBAAgB,CAAC,SAAS,CAAC;QAC3BC,sBAAsB,CAAC,IAAI,CAAC;QAE5B7H,OAAO,CAACmJ,GAAG,CAAC,uDAAuD,CAAC;MACxE,CAAC,MAAM;QACHnJ,OAAO,CAACC,KAAK,CAAC,qDAAqD,EAAE8K,MAAM,CAACd,IAAI,EAAEvN,OAAO,CAAC;QAC1F,MAAM,IAAIsN,KAAK,CAACe,MAAM,CAACd,IAAI,EAAEvN,OAAO,IAAI,gCAAgC,CAAC;MAC7E;IACJ,CAAC,CAAC,OAAOuD,KAAK,EAAE;MACZD,OAAO,CAACC,KAAK,CAAC,qDAAqD,EAAEA,KAAK,CAAC;MAC3EyH,QAAQ,CAACzH,KAAK,CAACvD,OAAO,CAAC;IAC3B;EACJ,CAAC,EAAE,CACCmG,OAAO,EACPiG,iBAAiB,EACjB3B,eAAe,EACf7E,YAAY,EACZuG,sBAAsB,EACtBtG,QAAQ,CACX,CAAC;;EAEF;EACA,MAAMgJ,WAAW,GAAGzE,+DAAW,CAAC,MAAM;IAClCW,eAAe,CAAC,IAAI,CAAC;IACrBE,UAAU,CAAC,IAAI,CAAC;IAChBD,QAAQ,CAAC,IAAI,CAAC;IACdF,YAAY,CAAC,KAAK,CAAC;IACnBI,gBAAgB,CAAC,SAAS,CAAC;IAC3BE,aAAa,CAAC,IAAI,CAAC;;IAEnB;IACAC,2BAA2B,CAACrJ,OAAO,GAAG,KAAK;;IAE3C;IACAsK,WAAW,CAAC,CAAC;IACb,IAAIf,oBAAoB,CAACvJ,OAAO,EAAE;MAC9BuD,aAAa,CAACgG,oBAAoB,CAACvJ,OAAO,CAAC;MAC3CuJ,oBAAoB,CAACvJ,OAAO,GAAG,IAAI;IACvC;EACJ,CAAC,EAAE,CAACsK,WAAW,CAAC,CAAC;;EAEjB;EACA1M,6DAAS,CAAC,MAAM;IACZ,MAAMkP,sBAAsB,GAAG,MAAAA,CAAA,KAAY;MACvC,IAAIzC,6BAA6B,EAAE;QAC/BtB,eAAe,CAACoB,sBAAsB,CAAC;QACvClB,UAAU,CAACmB,iBAAiB,IAAI3B,eAAe,CAAC;QAChDuB,aAAa,CAAC,CAAC;;QAEf;QACA,IAAIG,sBAAsB,EAAEsB,gBAAgB,EAAE;UAC1CrC,aAAa,CAACe,sBAAsB,CAACsB,gBAAgB,CAAC;QAC1D;QACA;MACJ;;MAEA;MACA;MACA,IAAIhD,eAAe,IACfG,eAAe,IACf,CAACC,SAAS,IACV,CAACjF,YAAY,IACb,CAACyF,2BAA2B,CAACrJ,OAAO,EAAE;QAEtC,MAAM0L,2BAA2B,CAAC,CAAC;QACnC1B,aAAa,CAAC,CAAC;MACnB;IACJ,CAAC;;IAED;IACA,MAAM+C,SAAS,GAAGxO,UAAU,CAACuO,sBAAsB,EAAE,GAAG,CAAC;IAEzD,OAAO,MAAMrO,YAAY,CAACsO,SAAS,CAAC;EACxC,CAAC,EAAE,CACCtE,eAAe,EACfG,eAAe;EAAE;EACjByB,6BAA6B,EAC7BF,sBAAsB,EACtBC,iBAAiB,EACjBvB,SAAS,EACTjF,YAAY,EACZ8H,2BAA2B,EAC3B1B,aAAa,CAChB,CAAC;;EAEF;EACApM,6DAAS,CAAC,MAAM;IACZ,IAAI,CAACgG,YAAY,IAAIuG,sBAAsB,KAAKrG,aAAa,KAAK,SAAS,EAAE;MACzE0I,YAAY,CAAC,CAAC;MACdC,cAAc,CAAC,CAAC;IACpB;IAEA,OAAO,MAAM;MACTnC,WAAW,CAAC,CAAC;MACb,IAAIf,oBAAoB,CAACvJ,OAAO,EAAE;QAC9BuD,aAAa,CAACgG,oBAAoB,CAACvJ,OAAO,CAAC;MAC/C;IACJ,CAAC;EACL,CAAC,EAAE,CAAC4D,YAAY,EAAEuG,sBAAsB,EAAErG,aAAa,EAAE0I,YAAY,EAAEC,cAAc,EAAEnC,WAAW,CAAC,CAAC;EAEpG,OAAO;IACHzB,SAAS;IACTjF,YAAY,EAAEA,YAAY,IAAIuG,sBAAsB;IACpD5I,KAAK;IACL4C,OAAO,EAAEA,OAAO,IAAIiG,iBAAiB,IAAI3B,eAAe;IAAE;IAC1D3E,aAAa;IACbE,mBAAmB;IACnBD,UAAU;IACV8I,WAAW;IACXlB,6BAA6B;IAAE;IAC/Be,kBAAkB;IAAE;IACpBM,YAAY,EAAExD,SAAS;IACvByD,aAAa,EAAE5C,6BAA6B,IAAI,CAAC,CAACzG;EACtD,CAAC;AACL,CAAC;;;;;;;;;;;;;;;;;AC3XsD;;AAEvD;AACA,MAAMsJ,qBAAqB,CAAC;EACxBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,UAAU,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC3B,IAAI,CAACC,gBAAgB,GAAG,IAAIC,GAAG,CAAC,CAAC;IACjC,IAAI,CAACN,aAAa,GAAG,KAAK;EAC9B;;EAEA;EACAO,oBAAoBA,CAAC7E,eAAe,EAAED,WAAW,EAAE;IAC/C,MAAM+E,YAAY,GAAG,GAAG9E,eAAe,IAAID,WAAW,EAAE;IAExD,IAAI,CAAC,IAAI,CAAC0E,UAAU,CAACM,GAAG,CAACD,YAAY,CAAC,EAAE;MACpC;MACA,MAAME,SAAS,GAAGC,QAAQ,CAAC9O,aAAa,CAAC,KAAK,CAAC;MAC/C6O,SAAS,CAAC/M,EAAE,GAAG8H,WAAW;MAC1BiF,SAAS,CAAC5O,SAAS,GAAG,sCAAsC;MAC5D4O,SAAS,CAAC3O,KAAK,CAAC6O,OAAO,GAAG;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;;MAED;MACAD,QAAQ,CAAC1C,IAAI,CAAC4C,WAAW,CAACH,SAAS,CAAC;MAEpC,IAAI,CAACP,UAAU,CAACW,GAAG,CAACN,YAAY,EAAE;QAC9BO,OAAO,EAAEL,SAAS;QAClBV,aAAa,EAAE,KAAK;QACpBgB,SAAS,EAAE,KAAK;QAChB9J,OAAO,EAAE,IAAI;QAAE;QACf+J,WAAW,EAAE,IAAI,CAAC;MACtB,CAAC,CAAC;IACN;IAEA,OAAO,IAAI,CAACd,UAAU,CAAC5H,GAAG,CAACiI,YAAY,CAAC;EAC5C;;EAEA;EACAzD,aAAaA,CAACrB,eAAe,EAAED,WAAW,EAAEyF,aAAa,EAAE;IACvD,MAAMV,YAAY,GAAG,GAAG9E,eAAe,IAAID,WAAW,EAAE;IACxD,MAAM0F,aAAa,GAAG,IAAI,CAAChB,UAAU,CAAC5H,GAAG,CAACiI,YAAY,CAAC;IAEvD,IAAIW,aAAa,IAAIA,aAAa,CAACJ,OAAO,EAAE;MACxC;MACA,IAAI,CAACK,iBAAiB,CAAC,CAAC;;MAExB;MACA,IAAIF,aAAa,EAAE;QACfA,aAAa,CAACL,WAAW,CAACM,aAAa,CAACJ,OAAO,CAAC;QAChDI,aAAa,CAACJ,OAAO,CAAChP,KAAK,CAAC6O,OAAO,GAAG;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;QACDO,aAAa,CAACH,SAAS,GAAG,IAAI;QAC9B,IAAI,CAACX,gBAAgB,CAACgB,GAAG,CAACb,YAAY,CAAC;MAC3C;IACJ;EACJ;;EAEA;EACAxD,aAAaA,CAACtB,eAAe,EAAED,WAAW,EAAE;IACxC,MAAM+E,YAAY,GAAG,GAAG9E,eAAe,IAAID,WAAW,EAAE;IACxD,MAAM0F,aAAa,GAAG,IAAI,CAAChB,UAAU,CAAC5H,GAAG,CAACiI,YAAY,CAAC;IAEvD,IAAIW,aAAa,IAAIA,aAAa,CAACJ,OAAO,EAAE;MACxC;MACAJ,QAAQ,CAAC1C,IAAI,CAAC4C,WAAW,CAACM,aAAa,CAACJ,OAAO,CAAC;MAChDI,aAAa,CAACJ,OAAO,CAAChP,KAAK,CAAC6O,OAAO,GAAG;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;MACDO,aAAa,CAACH,SAAS,GAAG,KAAK;MAC/B,IAAI,CAACX,gBAAgB,CAACiB,MAAM,CAACd,YAAY,CAAC;IAC9C;EACJ;;EAEA;EACAY,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACjB,UAAU,CAACoB,OAAO,CAAC,CAACJ,aAAa,EAAEX,YAAY,KAAK;MACrD,IAAIW,aAAa,CAACH,SAAS,EAAE;QACzB,IAAI,CAAChE,aAAa,CAACwD,YAAY,CAACgB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAEhB,YAAY,CAACgB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9E;IACJ,CAAC,CAAC;EACN;;EAEA;EACA1E,cAAcA,CAACpB,eAAe,EAAED,WAAW,EAAE;IACzC,MAAM+E,YAAY,GAAG,GAAG9E,eAAe,IAAID,WAAW,EAAE;IACxD,MAAM0F,aAAa,GAAG,IAAI,CAAChB,UAAU,CAAC5H,GAAG,CAACiI,YAAY,CAAC;IAEvD,IAAIW,aAAa,EAAE;MACfA,aAAa,CAACjK,OAAO,GAAG,IAAI;MAC5BiK,aAAa,CAACF,WAAW,GAAG,IAAI;MAChCE,aAAa,CAACxK,YAAY,GAAG,IAAI,CAAC,CAAC;MACnCwK,aAAa,CAACnB,aAAa,GAAG,KAAK,CAAC,CAAC;IACzC;EACJ;;EAEA;EACArD,uBAAuBA,CAACjB,eAAe,EAAED,WAAW,EAAE;IAClD,MAAM+E,YAAY,GAAG,GAAG9E,eAAe,IAAID,WAAW,EAAE;IACxD,MAAM0F,aAAa,GAAG,IAAI,CAAChB,UAAU,CAAC5H,GAAG,CAACiI,YAAY,CAAC;IACvD,IAAIW,aAAa,EAAE;MACfA,aAAa,CAACnB,aAAa,GAAG,IAAI;IACtC;EACJ;;EAEA;EACAtD,sBAAsBA,CAAChB,eAAe,EAAED,WAAW,EAAE;IACjD,MAAM+E,YAAY,GAAG,GAAG9E,eAAe,IAAID,WAAW,EAAE;IACxD,MAAM0F,aAAa,GAAG,IAAI,CAAChB,UAAU,CAAC5H,GAAG,CAACiI,YAAY,CAAC;IACvD,OAAOW,aAAa,GAAGA,aAAa,CAACnB,aAAa,GAAG,KAAK;EAC9D;;EAEA;EACApD,YAAYA,CAAClB,eAAe,EAAED,WAAW,EAAEvE,OAAO,EAAE+J,WAAW,EAAEtK,YAAY,GAAG,IAAI,EAAE;IAClF,MAAM6J,YAAY,GAAG,GAAG9E,eAAe,IAAID,WAAW,EAAE;IACxD,MAAM0F,aAAa,GAAG,IAAI,CAAChB,UAAU,CAAC5H,GAAG,CAACiI,YAAY,CAAC;IACvD,IAAIW,aAAa,EAAE;MACfA,aAAa,CAACjK,OAAO,GAAGA,OAAO;MAC/BiK,aAAa,CAACF,WAAW,GAAGA,WAAW;MACvCE,aAAa,CAACxK,YAAY,GAAGA,YAAY,CAAC,CAAC;IAC/C,CAAC,MAAM;MACHtC,OAAO,CAAC4K,IAAI,CAAC,qCAAqCuB,YAAY,mCAAmC,CAAC;IACtG;EACJ;;EAEA;EACA3D,YAAYA,CAACnB,eAAe,EAAED,WAAW,EAAE;IACvC,MAAM+E,YAAY,GAAG,GAAG9E,eAAe,IAAID,WAAW,EAAE;IACxD,MAAM0F,aAAa,GAAG,IAAI,CAAChB,UAAU,CAAC5H,GAAG,CAACiI,YAAY,CAAC;IACvD,MAAMpB,MAAM,GAAG+B,aAAa,GAAG;MAC3BjK,OAAO,EAAEiK,aAAa,CAACjK,OAAO;MAC9B+J,WAAW,EAAEE,aAAa,CAACF,WAAW;MACtCtK,YAAY,EAAEwK,aAAa,CAACxK,YAAY,CAAC;IAC7C,CAAC,GAAG;MAAEO,OAAO,EAAE,IAAI;MAAE+J,WAAW,EAAE,IAAI;MAAEtK,YAAY,EAAE;IAAK,CAAC;IAE5D,OAAOyI,MAAM;EACjB;;EAEA;EACAqC,mBAAmBA,CAAC/F,eAAe,EAAED,WAAW,EAAE;IAC9C,MAAM+E,YAAY,GAAG,GAAG9E,eAAe,IAAID,WAAW,EAAE;IACxD,MAAM0F,aAAa,GAAG,IAAI,CAAChB,UAAU,CAAC5H,GAAG,CAACiI,YAAY,CAAC;IACvD,OAAOW,aAAa,GAAGA,aAAa,CAACJ,OAAO,GAAG,IAAI;EACvD;;EAEA;EACAW,OAAOA,CAAA,EAAG;IACN,IAAI,CAACvB,UAAU,CAACoB,OAAO,CAAEJ,aAAa,IAAK;MACvC,IAAIA,aAAa,CAACJ,OAAO,IAAII,aAAa,CAACJ,OAAO,CAACY,UAAU,EAAE;QAC3DR,aAAa,CAACJ,OAAO,CAACY,UAAU,CAACC,WAAW,CAACT,aAAa,CAACJ,OAAO,CAAC;MACvE;IACJ,CAAC,CAAC;IACF,IAAI,CAACZ,UAAU,CAAC0B,KAAK,CAAC,CAAC;IACvB,IAAI,CAACxB,gBAAgB,CAACwB,KAAK,CAAC,CAAC;EACjC;AACJ;;AAEA;AACA,MAAMC,qBAAqB,GAAG,IAAI7B,qBAAqB,CAAC,CAAC;;AAEzD;AACO,MAAM7E,oCAAoC,GAAGA,CAACM,eAAe,EAAED,WAAW,KAAK;EAClF,MAAMc,SAAS,GAAG/J,0DAAM,CAAC,IAAI,CAAC;EAC9B,MAAMuP,WAAW,GAAGvP,0DAAM,CAAC,KAAK,CAAC;;EAEjC;EACA,MAAMwP,qBAAqB,GAAG,cAActG,eAAe,IAAID,WAAW,EAAE;EAE5E9K,6DAAS,CAAC,MAAM;IACZ;IACA,MAAMwQ,aAAa,GAAGW,qBAAqB,CAACvB,oBAAoB,CAAC7E,eAAe,EAAED,WAAW,CAAC;;IAE9F;IACA,IAAI0F,aAAa,CAACJ,OAAO,EAAE;MACvBI,aAAa,CAACJ,OAAO,CAACpN,EAAE,GAAGqO,qBAAqB;IACpD;;IAEA;IACA,IAAIzF,SAAS,CAACxJ,OAAO,IAAI,CAACgP,WAAW,CAAChP,OAAO,EAAE;MAC3C+O,qBAAqB,CAAC/E,aAAa,CAACrB,eAAe,EAAED,WAAW,EAAEc,SAAS,CAACxJ,OAAO,CAAC;MACpFgP,WAAW,CAAChP,OAAO,GAAG,IAAI;IAC9B;;IAEA;IACA,OAAO,MAAM;MACT,IAAIgP,WAAW,CAAChP,OAAO,EAAE;QACrB+O,qBAAqB,CAAC9E,aAAa,CAACtB,eAAe,EAAED,WAAW,CAAC;QACjEsG,WAAW,CAAChP,OAAO,GAAG,KAAK;MAC/B;IACJ,CAAC;EACL,CAAC,EAAE,CAAC2I,eAAe,EAAED,WAAW,CAAC,CAAC;EAElC,OAAO;IACHc,SAAS;IACTC,gBAAgB,EAAEsF,qBAAqB,CAACL,mBAAmB,CAAC/F,eAAe,EAAED,WAAW,CAAC;IACzFgB,iBAAiB,EAAEuF,qBAAqB;IAAE;IAC1CtF,sBAAsB,EAAEoF,qBAAqB,CAACpF,sBAAsB,CAAChB,eAAe,EAAED,WAAW,CAAC;IAClGkB,uBAAuB,EAAEA,CAAA,KAAMmF,qBAAqB,CAACnF,uBAAuB,CAACjB,eAAe,EAAED,WAAW,CAAC;IAC1G;IACAmB,YAAY,EAAEA,CAAC1F,OAAO,EAAE+J,WAAW,EAAEtK,YAAY,GAAG,IAAI,KAAKmL,qBAAqB,CAAClF,YAAY,CAAClB,eAAe,EAAED,WAAW,EAAEvE,OAAO,EAAE+J,WAAW,EAAEtK,YAAY,CAAC;IACjKkG,YAAY,EAAEA,CAAA,KAAMiF,qBAAqB,CAACjF,YAAY,CAACnB,eAAe,EAAED,WAAW,CAAC;IACpFqB,cAAc,EAAEA,CAAA,KAAM;MAClBgF,qBAAqB,CAAChF,cAAc,CAACpB,eAAe,EAAED,WAAW,CAAC;IACtE,CAAC;IACDsB,aAAa,EAAEA,CAAA,KAAM;MACjB,IAAIR,SAAS,CAACxJ,OAAO,IAAI,CAACgP,WAAW,CAAChP,OAAO,EAAE;QAC3C+O,qBAAqB,CAAC/E,aAAa,CAACrB,eAAe,EAAED,WAAW,EAAEc,SAAS,CAACxJ,OAAO,CAAC;QACpFgP,WAAW,CAAChP,OAAO,GAAG,IAAI;MAC9B;IACJ,CAAC;IACDiK,aAAa,EAAEA,CAAA,KAAM;MACjB,IAAI+E,WAAW,CAAChP,OAAO,EAAE;QACrB+O,qBAAqB,CAAC9E,aAAa,CAACtB,eAAe,EAAED,WAAW,CAAC;QACjEsG,WAAW,CAAChP,OAAO,GAAG,KAAK;MAC/B;IACJ;EACJ,CAAC;AACL,CAAC;;AAED;AACA,IAAI,OAAO7B,MAAM,KAAK,WAAW,EAAE;EAC/BA,MAAM,CAAC+Q,gBAAgB,CAAC,cAAc,EAAE,MAAM;IAC1CH,qBAAqB,CAACJ,OAAO,CAAC,CAAC;EACnC,CAAC,CAAC;AACN;AAEA,iEAAeI,qBAAqB;;;;;;;;;;ACxPpC;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;UCAA;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;;WCtBA;WACA;WACA;WACA;WACA;WACA,iCAAiC,WAAW;WAC5C;WACA;;;;;WCPA;WACA;WACA;WACA;WACA,yCAAyC,wCAAwC;WACjF;WACA;WACA;;;;;WCPA;;;;;WCAA;WACA;WACA;WACA,uDAAuD,iBAAiB;WACxE;WACA,gDAAgD,aAAa;WAC7D;;;;;;;;;;;;;;;;;;;;;;;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;;AAEqE;AAClB;AACO;AACrB;AACoB;AACb;AACiB;;AAE7D;AACmF;AACT;;AAE1E;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA,IAAI,OAAOI,+EAAqB,KAAK,UAAU,EAAE;EAC7C7N,OAAO,CAAC4K,IAAI,CAAC,8EAA8E,EAAEuD,MAAM,CAACC,IAAI,CAACvR,MAAM,CAACwR,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AAC9H,CAAC,MAAM;EACH;EACA,IAAI9L,QAAQ,GAAG,CAAC,CAAC;EACjB,IAAI,OAAOuL,6DAAU,KAAK,UAAU,EAAE;IAClC,IAAI;MACAvL,QAAQ,GAAGuL,iEAAU,CAAC,oBAAoB,EAAE,CAAC,CAAC,CAAC;IACnD,CAAC,CAAC,OAAO7N,KAAK,EAAE;MACZD,OAAO,CAACmJ,GAAG,CAAC,yCAAyC,EAAElJ,KAAK,CAAC;IACjE;EACJ;EAEA,MAAMqO,YAAY,GAAGN,mDAAE,CAAC,uBAAuB,EAAE,kCAAkC,CAAC;EACpF,MAAMzI,KAAK,GAAGwI,wEAAc,CAACxL,QAAQ,CAACnC,KAAK,CAAC,IAAIkO,YAAY;;EAE5D;AACJ;AACA;EACI,MAAMC,OAAO,GAAGA,CAAC;IACbtH,OAAO;IACPC,YAAY;IACZsH,cAAc;IACdhM,aAAa;IACbiM,iBAAiB;IACjBC;EACJ,CAAC,KAAK;IACF,MAAM,CAACC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGvS,4DAAQ,CAAC,SAAS,CAAC;;IAE3E;IACA,MAAM;MAAEwG;IAAQ,CAAC,GAAGoL,0DAAS,CAAEY,MAAM,IAAK;MACtC,MAAMC,KAAK,GAAGD,MAAM,CAACX,uEAAkB,CAAC;MACxC,OAAO;QACHrL,OAAO,EAAEiM,KAAK,CAACC,UAAU,CAAC;MAC9B,CAAC;IACL,CAAC,CAAC;;IAEF;IACA,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;MAC/B,MAAMC,cAAc,GAAGhI,OAAO,EAAEgI,cAAc;MAC9C,IAAI,CAACA,cAAc,EAAE,OAAO,KAAK;;MAEjC;MACA,OAAO,CAAC,EACJA,cAAc,CAACC,KAAK,IACpBD,cAAc,CAACE,UAAU,IACzBF,cAAc,CAACG,SAAS,IACxBH,cAAc,CAACI,SAAS,IACxBJ,cAAc,CAACK,IAAI,IACnBL,cAAc,CAACM,QAAQ,IACvBN,cAAc,CAACO,OAAO,CACzB;IACL,CAAC;;IAED;IACA,MAAM;MACFjI,SAAS;MACTjF,YAAY;MACZrC,KAAK;MACLuC,aAAa,EAAEiN,iBAAiB;MAChC/M,mBAAmB;MACnBD,UAAU;MACV8I,WAAW;MACXlB,6BAA6B;MAAE;MAC/Be,kBAAkB;MAAE;MACpBM,YAAY;MACZC;IACJ,CAAC,GAAG3E,+FAA2B,CAAC;MAC5BzE,QAAQ;MACR0E,OAAO;MACPC,YAAY;MACZrE,OAAO;MAAE;MACTuE,WAAW,EAAE,8BAA8B;MAC3CC,eAAe,EAAE,eAAe;MAChCC,eAAe,EAAE0H,oBAAoB,CAAC,CAAC,CAAC;IAC5C,CAAC,CAAC;IAEF1S,6DAAS,CAAC,MAAM;MACZ;MACA,MAAMoT,gBAAgB,GAAGpD,QAAQ,CAACqD,aAAa,CAAC,+FAA+F,CAAC;MAEhJ,IAAID,gBAAgB,EAAE;QAClB;QACAA,gBAAgB,CAAChS,KAAK,CAACkS,OAAO,GAAG,MAAM;MAC3C;;MAEA;MACA,OAAO,MAAM;QACT,IAAIF,gBAAgB,EAAE;UAClB;UACAA,gBAAgB,CAAChS,KAAK,CAACkS,OAAO,GAAG,EAAE;QACvC;MACJ,CAAC;IACL,CAAC,EAAE,EAAE,CAAC;;IAEN;IACAtT,6DAAS,CAAC,MAAM;MACZ,IAAImT,iBAAiB,KAAK,SAAS,EAAE;QACjCb,uBAAuB,CAACa,iBAAiB,CAAC;MAC9C;IACJ,CAAC,EAAE,CAACA,iBAAiB,CAAC,CAAC;IAEvB,MAAMI,WAAW,GAAG9B,wEAAc,CAACxL,QAAQ,CAACsN,WAAW,IAAI,EAAE,CAAC;;IAE9D;IACA,MAAMC,yBAAyB,GAAI9E,MAAM,IAAK;MAC1C,IAAIA,MAAM,KAAK,SAAS,EAAE;QACtB;QACA4D,uBAAuB,CAAC,SAAS,CAAC;QAClC;QACA,IAAIxD,kBAAkB,EAAE;UACpBA,kBAAkB,CAAC,CAAC;QACxB;MACJ,CAAC,MAAM;QACHwD,uBAAuB,CAAC5D,MAAM,CAAC;MACnC;IACJ,CAAC;;IAED;IACA,IAAIzD,SAAS,IAAI,CAACjF,YAAY,EAAE;MAC5B,oBACI/E,KAAA,CAAAC,aAAA;QAAKC,SAAS,EAAC;MAAuB,gBAClCF,KAAA,CAAAC,aAAA;QACIC,SAAS,EAAC,2BAA2B;QACrCwH,uBAAuB,EAAE;UAAEC,MAAM,EAAE2K;QAAY;MAAE,CACpD,CAAC,eACFtS,KAAA,CAAAC,aAAA;QAAKC,SAAS,EAAC;MAAuB,gBAClCF,KAAA,CAAAC,aAAA;QAAKC,SAAS,EAAC;MAAS,CAAM,CAAC,eAC/BF,KAAA,CAAAC,aAAA,YAAI+E,QAAQ,CAAC1B,OAAO,EAAEkP,OAAO,IAAI,oCAAwC,CACxE,CACJ,CAAC;IAEd;;IAEA;IACA,IAAI,CAACf,oBAAoB,CAAC,CAAC,IAAI,CAAC1M,YAAY,EAAE;MAC1C,oBACI/E,KAAA,CAAAC,aAAA;QAAKC,SAAS,EAAC;MAAuB,gBAClCF,KAAA,CAAAC,aAAA;QACIC,SAAS,EAAC,2BAA2B;QACrCwH,uBAAuB,EAAE;UAAEC,MAAM,EAAE2K;QAAY;MAAE,CACpD,CAAC,eACFtS,KAAA,CAAAC,aAAA;QAAKC,SAAS,EAAC;MAAoB,gBAC/BF,KAAA,CAAAC,aAAA,YAAG,kFAAmF,CAAC,eACvFD,KAAA,CAAAC,aAAA,gBAAO,6DAAkE,CACxE,CACJ,CAAC;IAEd;;IAEA;IACA,IAAIyC,KAAK,EAAE;MACP,oBACI1C,KAAA,CAAAC,aAAA;QAAKC,SAAS,EAAC;MAAuB,gBAClCF,KAAA,CAAAC,aAAA;QACIC,SAAS,EAAC,2BAA2B;QACrCwH,uBAAuB,EAAE;UAAEC,MAAM,EAAE2K;QAAY;MAAE,CACpD,CAAC,eACFtS,KAAA,CAAAC,aAAA;QAAKC,SAAS,EAAC;MAAqB,gBAChCF,KAAA,CAAAC,aAAA,YAAIyC,KAAS,CAAC,eACd1C,KAAA,CAAAC,aAAA;QAAQ0C,IAAI,EAAC,QAAQ;QAACC,OAAO,EAAEoL;MAAY,GAAC,WAEpC,CACP,CACJ,CAAC;IAEd;IAEA,oBACIhO,KAAA,CAAAC,aAAA;MAAKC,SAAS,EAAC;IAAuB,gBAClCF,KAAA,CAAAC,aAAA;MACIC,SAAS,EAAC,2BAA2B;MACrCwH,uBAAuB,EAAE;QAAEC,MAAM,EAAE2K;MAAY;IAAE,CACpD,CAAC,eAGFtS,KAAA,CAAAC,aAAA;MAAK6B,GAAG,EAAEqM,YAAa;MAACjO,SAAS,EAAC;IAAwB,GACrD6E,YAAY,iBACT/E,KAAA,CAAAC,aAAA,CAAC6E,mFAA0B;MACvBC,YAAY,EAAEA,YAAa;MAC3BC,QAAQ,EAAEA,QAAS;MACnBC,aAAa,EAAEmM,oBAAqB;MACpClM,UAAU,EAAEA,UAAW;MACvBC,mBAAmB,EAAEA,mBAAoB;MACzCC,qBAAqB,EAAEmN,yBAA0B;MACjDlN,wBAAwB,EAAEyH,6BAA8B;MACxDxH,OAAO,EAAEA;IAAQ,CACpB,CAEJ,CACJ,CAAC;EAEd,CAAC;;EAED;AACJ;AACA;EACI,MAAMmN,KAAK,GAAIC,KAAK,IAAK;IACrB,MAAM;MAAEC;IAAmB,CAAC,GAAGD,KAAK,CAACE,UAAU,IAAI,CAAC,CAAC;IACrD,oBACI5S,KAAA,CAAAC,aAAA;MAAKE,KAAK,EAAE;QACRkS,OAAO,EAAE,MAAM;QACfQ,cAAc,EAAE,eAAe;QAC/BC,UAAU,EAAE,QAAQ;QACpBvR,KAAK,EAAE;MACX;IAAE,gBACEvB,KAAA,CAAAC,aAAA;MAAME,KAAK,EAAE;QAAE4S,UAAU,EAAE;MAAI;IAAE,GAAE/K,KAAY,CAAC,eAChDhI,KAAA,CAAAC,aAAA;MACIyI,GAAG,EAAE,GAAG1D,QAAQ,CAAC2D,UAAU,IAAI,uDAAuD,8BAA+B;MACrHC,GAAG,EAAC,YAAY;MAChBzI,KAAK,EAAE;QACHqB,MAAM,EAAE,MAAM;QACdD,KAAK,EAAE;MACX;IAAE,CACL,CACA,CAAC;EAEd,CAAC;;EAED;AACJ;AACA;AACA;AACA;EACI,MAAMyR,IAAI,GAAGA,CAAA,KAAM;IACf,MAAMV,WAAW,GAAG9B,wEAAc,CAACxL,QAAQ,CAACsN,WAAW,IAAI,EAAE,CAAC;IAC9D,oBACItS,KAAA,CAAAC,aAAA;MAAKC,SAAS,EAAC;IAAuB,gBAClCF,KAAA,CAAAC,aAAA;MACIC,SAAS,EAAC,2BAA2B;MACrCwH,uBAAuB,EAAE;QAAEC,MAAM,EAAE2K;MAAY;IAAE,CACpD,CAAC,eACFtS,KAAA,CAAAC,aAAA;MAAKC,SAAS,EAAC;IAA4B,gBACvCF,KAAA,CAAAC,aAAA;MAAKE,KAAK,EAAE;QACRI,MAAM,EAAE,iBAAiB;QACzBF,OAAO,EAAE,MAAM;QACfI,SAAS,EAAE,QAAQ;QACnBD,YAAY,EAAE,KAAK;QACnBG,KAAK,EAAE,MAAM;QACbsS,MAAM,EAAE;MACZ;IAAE,gBACEjT,KAAA,CAAAC,aAAA,yBAAGD,KAAA,CAAAC,aAAA,iBAAQ,oCAA0C,CAAI,CAAC,eAC1DD,KAAA,CAAAC,aAAA,YAAG,uDAAwD,CAC1D,CACJ,CACJ,CAAC;EAEd,CAAC;;EAED;AACJ;AACA;EACI,MAAMiT,YAAY,GAAG;IACjBC,IAAI,EAAE,eAAe;IACrBnL,KAAK,eAAEhI,KAAA,CAAAC,aAAA,CAACwS,KAAK,MAAE,CAAC;IAChBW,OAAO,eAAEpT,KAAA,CAAAC,aAAA,CAAC+Q,OAAO,MAAE,CAAC;IACpBqC,IAAI,eAAErT,KAAA,CAAAC,aAAA,CAAC+S,IAAI,MAAE,CAAC;IACdM,cAAc,EAAE,SAAAA,CAAA,EAAW;MAAE,OAAO,IAAI;IAAE,CAAC;IAC3CC,SAAS,EAAEvL,KAAK;IAChBwL,QAAQ,EAAE;MACNC,QAAQ,EAAEzO,QAAQ,CAACwO,QAAQ,IAAI;IACnC,CAAC;IACDE,KAAK,EAAE1O,QAAQ,CAAC0O,KAAK,IAAI;EAC7B,CAAC;;EAED;EACA,IAAI;IACApD,mFAAqB,CAAC4C,YAAY,CAAC;EACvC,CAAC,CAAC,OAAOxQ,KAAK,EAAE;IACZD,OAAO,CAACC,KAAK,CAAC,yDAAyD,EAAEA,KAAK,CAAC;EACnF;AACJ,C", "sources": ["webpack://monoova-payments-for-woocommerce/./assets/js/src/blocks/NavigationCountdown.js", "webpack://monoova-payments-for-woocommerce/./assets/js/src/blocks/PayIDInstructionsContainer.js", "webpack://monoova-payments-for-woocommerce/./assets/js/src/hooks/usePayIDPaymentInstructions.js", "webpack://monoova-payments-for-woocommerce/./assets/js/src/hooks/usePersistentPaymentDetailsContainer.js", "webpack://monoova-payments-for-woocommerce/external var [\"wc\",\"wcBlocksData\"]", "webpack://monoova-payments-for-woocommerce/external var [\"wc\",\"wcBlocksRegistry\"]", "webpack://monoova-payments-for-woocommerce/external var [\"wc\",\"wcSettings\"]", "webpack://monoova-payments-for-woocommerce/external window [\"wp\",\"components\"]", "webpack://monoova-payments-for-woocommerce/external window [\"wp\",\"data\"]", "webpack://monoova-payments-for-woocommerce/external window [\"wp\",\"element\"]", "webpack://monoova-payments-for-woocommerce/external window [\"wp\",\"htmlEntities\"]", "webpack://monoova-payments-for-woocommerce/external window [\"wp\",\"i18n\"]", "webpack://monoova-payments-for-woocommerce/webpack/bootstrap", "webpack://monoova-payments-for-woocommerce/webpack/runtime/compat get default export", "webpack://monoova-payments-for-woocommerce/webpack/runtime/define property getters", "webpack://monoova-payments-for-woocommerce/webpack/runtime/hasOwnProperty shorthand", "webpack://monoova-payments-for-woocommerce/webpack/runtime/make namespace object", "webpack://monoova-payments-for-woocommerce/./assets/js/src/blocks/monoova-payid-block.js"], "sourcesContent": ["import { useState, useEffect } from '@wordpress/element';\n\n/**\n * Navigation Countdown Component\n * \n * Shows a countdown timer and redirects to a URL when it reaches 0\n * \n * @param {Object} props\n * @param {number} props.initialSeconds - Starting countdown time in seconds\n * @param {string} props.redirectUrl - URL to redirect to when countdown reaches 0\n * @param {string} props.message - Message template with {countdown} placeholder\n */\nexport const NavigationCountdown = ({ \n    initialSeconds = 5, \n    redirectUrl, \n    message = 'Redirecting in {countdown} seconds...' \n}) => {\n    const [countdown, setCountdown] = useState(initialSeconds);\n\n    useEffect(() => {\n        if (countdown <= 0) {\n            if (redirectUrl) {\n                window.location.href = redirectUrl;\n            }\n            return;\n        }\n\n        const timer = setTimeout(() => {\n            setCountdown(prev => prev - 1);\n        }, 1000);\n\n        return () => clearTimeout(timer);\n    }, [countdown, redirectUrl]);\n\n    const formattedMessage = message.replace('{countdown}', countdown.toString());\n\n    return (\n        <div className=\"monoova-navigation-countdown\" style={{ \n            marginTop: '15px', \n            padding: '10px',\n            backgroundColor: '#f8f9fa',\n            border: '1px solid #dee2e6',\n            borderRadius: '4px',\n            textAlign: 'center',\n            fontSize: '14px',\n            color: '#6c757d'\n        }}>\n            {formattedMessage}\n        </div>\n    );\n};\n", "import { useState, useEffect, useRef } from '@wordpress/element';\nimport { RadioControl, Button } from '@wordpress/components';\nimport { NavigationCountdown } from './NavigationCountdown';\n\n// QR Code generation component (simplified version)\nconst QRCodeDisplay = ({ payload, size = 180 }) => {\n    const qrRef = useRef(null);\n\n    useEffect(() => {\n        if (!payload || !qrRef.current || typeof window.QRCode === 'undefined') return;\n\n        // Clear any previous QR code\n        qrRef.current.innerHTML = '';\n        \n        new window.QRCode(qrRef.current, {\n            text: payload,\n            width: size,\n            height: size,\n            colorDark: '#000000',\n            colorLight: '#ffffff',\n            correctLevel: window.QRCode?.CorrectLevel?.H\n        });\n    }, [payload, size]);\n\n    return <div ref={qrRef} id=\"monoova-qr-code\" />;\n};\n\n// Copy button component\nconst CopyButton = ({ text, onCopy }) => {\n    const [copied, setCopied] = useState(false);\n\n    const handleCopy = async () => {\n        try {\n            await navigator.clipboard.writeText(text);\n            setCopied(true);\n            onCopy && onCopy();\n            setTimeout(() => setCopied(false), 2000);\n        } catch (err) {\n            console.error('Failed to copy:', err);\n        }\n    };\n\n    return (\n        <button \n            type=\"button\"\n            className=\"monoova-copy-button\" \n            onClick={handleCopy}\n            title={copied ? 'Copied!' : 'Copy'}\n        >\n            <svg width=\"19\" height=\"18\" viewBox=\"0 0 19 18\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                <path d=\"M12.4868 9.675V12.825C12.4868 15.45 11.4368 16.5 8.81182 16.5H5.66182C3.03682 16.5 1.98682 15.45 1.98682 12.825V9.675C1.98682 7.05 3.03682 6 5.66182 6H8.81182C11.4368 6 12.4868 7.05 12.4868 9.675Z\" stroke=\"#2CB5C5\" stroke-width=\"1.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" />\n                <path d=\"M16.9868 5.175V8.325C16.9868 10.95 15.9368 12 13.3118 12H12.4868V9.675C12.4868 7.05 11.4368 6 8.81182 6H6.48682V5.175C6.48682 2.55 7.53682 1.5 10.1618 1.5H13.3118C15.9368 1.5 16.9868 2.55 16.9868 5.175Z\" stroke=\"#2CB5C5\" stroke-width=\"1.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" />\n            </svg>\n        </button>\n    );\n};\n\n// Countdown timer component\nconst CountdownTimer = ({ expiryTimestamp, onExpired, strings }) => {\n    const [timeLeft, setTimeLeft] = useState('');\n\n    useEffect(() => {\n        if (!expiryTimestamp || expiryTimestamp <= 0) {\n            setTimeLeft('');\n            return;\n        }\n\n        const calculateTimeLeft = () => {\n            const distance = expiryTimestamp * 1000 - new Date().getTime();\n            \n            if (distance < 0) {\n                setTimeLeft('');\n                onExpired && onExpired();\n                return null;\n            }\n\n            const days = Math.floor(distance / (1000 * 60 * 60 * 24));\n            const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));\n            const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));\n            const seconds = Math.floor((distance % (1000 * 60)) / 1000);\n\n            let timeArr = [];\n            if (days > 0) timeArr.push(`${days} ${days === 1 ? \"day\" : \"days\"}`);\n            if (hours > 0) timeArr.push(`${hours} ${hours === 1 ? \"hour\" : \"hours\"}`);\n            if (minutes > 0) timeArr.push(`${minutes} ${minutes === 1 ? \"minute\" : \"minutes\"}`);\n            if (seconds > 0) timeArr.push(`${seconds} ${seconds === 1 ? \"second\" : \"seconds\"}`);\n            \n            return `(${timeArr.join(\" and \")} remaining)`;\n        };\n\n        // Set initial value\n        const initialTime = calculateTimeLeft();\n        if (initialTime) {\n            setTimeLeft(initialTime);\n        }\n\n        const interval = setInterval(() => {\n            const newTime = calculateTimeLeft();\n            if (newTime) {\n                setTimeLeft(newTime);\n            } else {\n                clearInterval(interval);\n            }\n        }, 1000);\n\n        return () => clearInterval(interval);\n    }, [expiryTimestamp, onExpired]);\n\n    if (!timeLeft || expiryTimestamp < Date.now() / 1000) return null;\n\n    return (\n        <div id=\"monoova-expiry-info\" className=\"monoova-expiry-info\">\n            <span className=\"monoova-expiry-label\">{strings.expires_in}</span>\n            <span className=\"monoova-expiry-time\">\n                <strong>{new Date(expiryTimestamp * 1000).toLocaleString()} {timeLeft}</strong>\n            </span>\n        </div>\n    );\n};\n\n// Main PayID instructions component\nexport const PayIDInstructionsContainer = ({ \n    instructions, \n    settings, \n    paymentStatus = 'pending',\n    expiryTime,\n    paymentFailedReason = null,\n    onPaymentStatusChange,\n    onRegenerateInstructions, // New prop for regeneration\n    orderId = null // Order ID for generating order received URL\n}) => {\n    const [selectedMethod, setSelectedMethod] = useState('payid');\n    \n    if (!instructions) return null;\n\n    const { details, view_order_url } = instructions;\n    const showPayID = settings.payment_types.includes('payid') && details.payid_value;\n    const showBank = settings.payment_types.includes('bank_transfer') && details.bank_bsb && details.bank_account_number;\n    const showMethodSwitcher = showPayID && showBank;\n\n    // Default to first available method\n    useEffect(() => {\n        if (showPayID && selectedMethod !== 'payid' && selectedMethod !== 'bank_transfer') {\n            setSelectedMethod('payid');\n        } else if (!showPayID && showBank) {\n            setSelectedMethod('bank_transfer');\n        }\n    }, [showPayID, showBank, selectedMethod]);\n\n    const handleExpired = () => {\n        onPaymentStatusChange && onPaymentStatusChange('expired');\n    };\n\n    const handlePlaceNewOrder = () => {\n        if (onRegenerateInstructions) {\n            onRegenerateInstructions();\n        }\n        onPaymentStatusChange('pending');\n    };\n\n    const handlePayAgain = () => {\n        // Just reset the UI to show the instructions again without regenerating\n        onPaymentStatusChange('pending');\n    };\n\n    // Generate order received URL with order ID and key\n    const generateOrderReceivedUrl = () => {\n        if (view_order_url) {\n            return view_order_url;\n        }\n        \n        // Get order key from the current URL if available\n        const urlParams = new URLSearchParams(window.location.search);\n        const orderKey = urlParams.get('key');\n        \n        // Construct the order received URL with order ID and key\n        let orderReceivedUrl = settings.order_received_url;\n        if (orderReceivedUrl.includes('?')) {\n            orderReceivedUrl += `&order-received=${orderId}`;\n        } else {\n            orderReceivedUrl += `?order-received=${orderId}`;\n        }\n        \n        if (orderKey) {\n            orderReceivedUrl += `&key=${orderKey}`;\n        }\n        \n        return orderReceivedUrl;\n    };\n\n    const renderPaymentStatus = () => {\n        switch (paymentStatus) {\n            case 'paid':\n                return (\n                    <div id=\"monoova-payment-confirmed\" className=\"monoova-payment-status\">\n                        <div>\n                            <svg width=\"45\" height=\"46\" viewBox=\"0 0 45 46\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M31.8167 18.8513L21.0413 29.6267C20.6983 29.9697 20.2527 30.14 19.8047 30.14C19.3543 30.14 18.9087 29.9697 18.5657 29.6267L13.178 24.239C12.4943 23.5553 12.4943 22.447 13.178 21.7633C13.8617 21.0797 14.9677 21.0797 15.6513 21.7633L19.8047 25.9143L29.341 16.3757C30.0247 15.692 31.133 15.692 31.8167 16.3757C32.5003 17.0593 32.5003 18.1677 31.8167 18.8513ZM22.4997 0.833344C10.2777 0.833344 0.333008 10.778 0.333008 23C0.333008 35.2243 10.2777 45.1667 22.4997 45.1667C34.7217 45.1667 44.6663 35.2243 44.6663 23C44.6663 10.778 34.7217 0.833344 22.4997 0.833344Z\" fill=\"#2CB5C5\"/>\n                            </svg>\n                        </div>\n                        <h3 className=\"title\">{settings.strings.payment_confirmed}</h3>\n                        <p>{settings.strings.payment_confirmed_message}</p>\n                        <div className=\"monoova-payment-status-actions\">\n                            <Button variant=\"primary\" href={generateOrderReceivedUrl()}>\n                                {settings.strings.view_order_details}\n                            </Button>\n                        </div>\n                        <NavigationCountdown \n                            initialSeconds={5}\n                            redirectUrl={generateOrderReceivedUrl()}\n                            message={settings.strings.redirecting_to_order_page}\n                        />\n                    </div>\n                );\n            case 'expired':\n                return (\n                    <div id=\"monoova-payment-expired\" className=\"monoova-payment-status\">\n                        <div>\n                            <svg width=\"45\" height=\"46\" viewBox=\"0 0 45 46\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M24.2647 31.617C24.2647 32.583 23.4807 33.367 22.5147 33.367C21.5487 33.367 20.7647 32.583 20.7647 31.617V21.586C20.7647 20.62 21.5487 19.836 22.5147 19.836C23.4807 19.836 24.2647 20.62 24.2647 21.586V31.617ZM20.7507 14.3456C20.7507 13.3796 21.5347 12.5956 22.5007 12.5956C23.4667 12.5956 24.2507 13.3796 24.2507 14.3456C24.2507 15.3116 23.4667 16.1703 22.5007 16.1703C21.5347 16.1703 20.7507 15.4586 20.7507 14.4926V14.3456ZM22.5007 0.833313C10.2787 0.833313 0.333984 10.7756 0.333984 23C0.333984 35.222 10.2787 45.1666 22.5007 45.1666C34.7227 45.1666 44.6673 35.222 44.6673 23C44.6673 10.7756 34.7227 0.833313 22.5007 0.833313Z\" fill=\"#FF782A\"/>\n                            </svg>\n\n                        </div>\n                        <h3 className=\"title\">{settings.strings.payment_expired}</h3>\n                        <p>{settings.strings.payment_expired_message}</p>\n                        <div className=\"monoova-payment-status-actions\">\n                            <Button variant=\"primary\" onClick={() => handlePlaceNewOrder()}>\n                                {settings.strings.place_new_order}\n                            </Button>\n                        </div>\n                    </div>\n                );\n            case 'failed':\n                return (\n                    <div id=\"monoova-payment-rejected\" className=\"monoova-payment-status\">\n                        <div>\n                            <svg width=\"45\" height=\"46\" viewBox=\"0 0 45 46\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M24.2647 31.617C24.2647 32.583 23.4807 33.367 22.5147 33.367C21.5487 33.367 20.7647 32.583 20.7647 31.617V21.586C20.7647 20.62 21.5487 19.836 22.5147 19.836C23.4807 19.836 24.2647 20.62 24.2647 21.586V31.617ZM20.7507 14.3456C20.7507 13.3796 21.5347 12.5956 22.5007 12.5956C23.4667 12.5956 24.2507 13.3796 24.2507 14.3456C24.2507 15.3116 23.4667 16.1703 22.5007 16.1703C21.5347 16.1703 20.7507 15.4586 20.7507 14.4926V14.3456ZM22.5007 0.833313C10.2787 0.833313 0.333984 10.7756 0.333984 23C0.333984 35.222 10.2787 45.1666 22.5007 45.1666C34.7227 45.1666 44.6673 35.222 44.6673 23C44.6673 10.7756 34.7227 0.833313 22.5007 0.833313Z\" fill=\"#FF0000\"/>\n                            </svg>\n                        </div>\n                        <h3 className=\"title\">{settings.strings.payment_failed}</h3>\n                        <p>{settings.strings.payment_failed_message}</p>\n                        <p id=\"monoova-rejection-reason\" style={{ fontStyle: 'italic', marginTop: 15 }}>\n                            Reason: <span dangerouslySetInnerHTML={{ __html: paymentFailedReason }} />\n                        </p>\n                        <div className=\"monoova-payment-status-actions\">\n                            <Button variant=\"primary\" onClick={() => handlePayAgain()}>\n                                {settings.strings.pay_again}\n                            </Button>\n                        </div>\n                    </div>\n                );\n            default:\n                return null;\n        }\n    };\n\n    if (paymentStatus !== 'pending' && paymentStatus !== 'initial') {\n        return (\n            <div className=\"monoova-payid-bank-transfer-instructions-wrapper\">\n                <div className=\"monoova-instructions-container\">\n                    {renderPaymentStatus()}\n                </div>\n            </div>\n        );\n    }\n\n    return (\n        <div className=\"monoova-payid-bank-transfer-instructions-wrapper\">\n            {\n                showMethodSwitcher && (\n                    <div className=\"monoova-instruction-method-selection\">\n                        <h4>{settings.strings.pay_with}</h4>\n                        <RadioControl\n                            selected={selectedMethod}\n                            options={[\n                                { label: settings.strings.payid_method, value: 'payid' },\n                                { label: settings.strings.bank_method, value: 'bank_transfer' },\n                            ]}\n                            onChange={(v) => setSelectedMethod(v)}\n                        />\n                    </div>\n                )\n            }\n\n            <div>{settings.strings.payment_instructions_description}</div>\n            <div className=\"monoova-instructions-container\">\n                <div id=\"monoova-payment-pending\">\n\n                    {/* Instructions */}\n                    {settings.instructions && (\n                        <p\n                            dangerouslySetInnerHTML={{ __html: settings.instructions }}\n                        />\n                    )}\n\n                    {/* PayID View */}\n                    {selectedMethod === 'payid' && showPayID && (\n                        <div id=\"monoova-payid-view\">\n                            <div className=\"monoova-scan-pay\">\n                                \n                                <h3>{settings.strings.scan_pay}</h3>\n                                <div \n                                    className=\"amount\"\n                                    dangerouslySetInnerHTML={{ __html: details.amount_to_pay_formatted }}\n                                />\n                                \n                            </div>\n                            <p>{settings.strings.scan_pay_description}</p>\n                            {/* {details.qr_code_payload && (\n                                <QRCodeDisplay payload={details.qr_code_payload} />\n                            )} */}\n\n                            {/* {settings.show_reference_field && (\n                                <div className=\"monoova-reference-area\">\n                                    <span className=\"ref-label\">{settings.strings.reference}:</span>\n                                    <span className=\"ref-value\">{details.reference + 'P'}</span>\n                                </div>\n                            )} */}\n\n                            {/* <div className=\"monoova-divider\">{settings.strings.or_divider}</div> */}\n\n                            <div className=\"monoova-manual-pay\">\n                                <img \n                                    src={`${settings.plugin_url}assets/images/payid-logo.svg`} \n                                    alt=\"PayID\" \n                                    className=\"payid-logo\" \n                                />\n                                <span className=\"copy-target\" data-copy-id=\"payid\">\n                                    {details.payid_value}\n                                </span>\n                                <CopyButton text={details.payid_value} />\n                            </div>\n\n                            {settings.show_reference_field && (\n                                <div className=\"monoova-payment-reference-section\">\n                                    <h4>{settings.strings.payment_reference}</h4>\n                                    <p>{settings.strings.include_reference_with_payment}</p>\n                                    <div className=\"monoova-reference-field full-width\">\n                                        <div className=\"field-label\">{settings.strings.reference}</div>\n                                        <div className=\"field-value-wrapper\">\n                                            <span className=\"copy-target\" data-copy-id=\"reference\">\n                                                {details.reference + 'P'} {/* Append 'P' for PayID */}\n                                            </span>\n                                            <CopyButton text={details.reference + 'P'} />\n                                        </div>\n                                    </div>\n                                </div>\n                            )}\n                        </div>\n                    )}\n\n                    {/* Bank View */}\n                    {selectedMethod === 'bank_transfer' && showBank && (\n                        <div id=\"monoova-bank-view\">\n                            <h3>{settings.strings.bank_transfer_details}</h3>\n                            \n                            <div className=\"monoova-bank-field-grid\">\n                                <div className=\"monoova-bank-field\">\n                                    <div className=\"field-label\">{settings.strings.account_name}</div>\n                                    <div className=\"field-value-wrapper\">\n                                        <span className=\"copy-target\" data-copy-id=\"account-name\">\n                                            {details.bank_account_name}\n                                        </span>\n                                        {/* <CopyButton text={details.bank_account_name} /> */}\n                                    </div>\n                                </div>\n\n                                <div className=\"monoova-bank-field\">\n                                    <div className=\"field-label\">{settings.strings.bsb}</div>\n                                    <div className=\"field-value-wrapper\">\n                                        <span className=\"copy-target\" data-copy-id=\"bsb\">\n                                            {details.bank_bsb}\n                                        </span>\n                                        <CopyButton text={details.bank_bsb} />\n                                    </div>\n                                </div>\n                            </div>\n                            <div className=\"monoova-bank-field full-width\">\n                                <div className=\"field-label\">{settings.strings.account_number}</div>\n                                <div className=\"field-value-wrapper\">\n                                    <span className=\"copy-target\" data-copy-id=\"account-number\">\n                                        {details.bank_account_number}\n                                    </span>\n                                    <CopyButton text={details.bank_account_number} />\n                                </div>\n                            </div>\n\n                            {settings.show_reference_field && (\n                                <div className=\"monoova-payment-reference-section\">\n                                    <h4>{settings.strings.payment_reference}</h4>\n                                    <p>{settings.strings.include_reference_with_payment}</p>\n                                    <div className=\"monoova-bank-field full-width\">\n                                        <div className=\"field-label\">{settings.strings.reference}</div>\n                                        <div className=\"field-value-wrapper\">\n                                            <span className=\"copy-target\" data-copy-id=\"reference\">\n                                                {details.reference + 'B'} {/* Append 'B' for Bank Transfer */}\n                                            </span>\n                                            <CopyButton text={details.reference + 'B'} />\n                                        </div>\n                                    </div>\n                                </div>\n                            )}\n                        </div>\n                    )}\n\n                    <div className=\"monoova-confirmation-text\">\n                        <p>{settings.strings.payment_confirmed_automatically}</p>\n                    </div>\n\n                    {expiryTime && (\n                        <CountdownTimer \n                            expiryTimestamp={expiryTime}\n                            onExpired={handleExpired}\n                            strings={settings.strings}\n                        />\n                    )}\n                </div>\n            </div>\n        </div>\n    );\n};\n", "import { useState, useEffect, useCallback, useRef } from '@wordpress/element';\nimport { usePersistentPaymentDetailsContainer } from './usePersistentPaymentDetailsContainer';\n\nexport const usePayIDPaymentInstructions = ({\n    settings,\n    billing,\n    shippingData,\n    orderId: existingOrderId, // Accept existing order ID from props\n    containerId = \"payid-instructions-container\",\n    paymentMethodId = \"monoova_payid\",\n    hasRequiredInfo = true // New parameter to control API calling\n}) => {\n    const [isLoading, setIsLoading] = useState(false);\n    const [instructions, setInstructions] = useState(null);\n    const [error, setError] = useState(null);\n    const [orderId, setOrderId] = useState(existingOrderId || null); // Initialize with existing order ID\n    const [paymentStatus, setPaymentStatus] = useState('pending');\n    const [paymentFailedReason, setPaymentFailedReason] = useState(null);\n    const [expiryTime, setExpiryTime] = useState(null);\n    \n    // Use refs to prevent duplicate API calls\n    const isGeneratingInstructionsRef = useRef(false);\n    const pollingIntervalRef = useRef(null);\n    const countdownIntervalRef = useRef(null);\n    \n    // Use persistent container management\n    const {\n        targetRef,\n        containerElement,\n        containerIdActive,\n        isContainerInitialized,\n        setContainerInitialized,\n        setOrderData,\n        getOrderData,\n        clearOrderData, // Make sure this is available from the hook\n        showContainer,\n        hideContainer\n    } = usePersistentPaymentDetailsContainer(paymentMethodId, containerId);\n    \n    // Get persistent instruction data\n    const persistentData = getOrderData();\n    const persistentInstructions = persistentData?.instructions;\n    const persistentOrderId = persistentData?.orderId;\n    \n    // Check if we should use existing instructions\n    const shouldUseExistingInstructions = isContainerInitialized && persistentInstructions;\n\n    const stopPolling = useCallback(() => {\n        if (pollingIntervalRef.current) {\n            clearInterval(pollingIntervalRef.current);\n            pollingIntervalRef.current = null;\n        }\n    }, []);\n\n    // Internal function to fetch instructions from the backend\n    const fetchPaymentInstructions = useCallback(async (isRegeneration = false) => {\n        setIsLoading(true);\n        setError(null);\n        isGeneratingInstructionsRef.current = true;\n\n        try {\n            console.log(`PayID Instructions: ${isRegeneration ? 'Regenerating' : 'Generating'} payment instructions for order:`, existingOrderId);\n            \n            const instructionsData = new FormData();\n            instructionsData.append('action', 'monoova_generate_payment_instructions_in_blocked_checkout');\n            instructionsData.append('order_id', existingOrderId);\n            instructionsData.append('nonce', settings.generate_instructions_nonce);\n\n            if (isRegeneration) {\n                instructionsData.append('regenerate', 'true');\n            }\n\n            const instructionsResponse = await fetch(settings.ajax_url, {\n                method: 'POST',\n                body: instructionsData\n            });\n\n            const instructionsResult = await instructionsResponse.json();\n            \n            if (!instructionsResult.success) {\n                throw new Error(instructionsResult.data?.message || `Failed to ${isRegeneration ? 'regenerate' : 'generate'} payment instructions`);\n            }\n\n            const paymentInstructions = instructionsResult.data;\n            setInstructions(paymentInstructions);\n            setOrderId(existingOrderId);\n            \n            if (paymentInstructions.expiry_timestamp) {\n                setExpiryTime(paymentInstructions.expiry_timestamp);\n            }\n\n            setOrderData(existingOrderId, null, paymentInstructions);\n            setContainerInitialized();\n\n        } catch (error) {\n            console.error(`PayID Instructions: Error ${isRegeneration ? 'regenerating' : 'generating'} instructions:`, error);\n            setError(error.message);\n        } finally {\n            setIsLoading(false);\n            isGeneratingInstructionsRef.current = false;\n        }\n    }, [\n        existingOrderId,\n        settings,\n        setOrderData,\n        setContainerInitialized\n    ]);\n\n    // Generate payment instructions for existing order\n    const generatePaymentInstructions = useCallback(async () => {\n        if (isGeneratingInstructionsRef.current || instructions || persistentInstructions) {\n            return;\n        }\n\n        if (!existingOrderId || !hasRequiredInfo) {\n            return;\n        }\n\n        await fetchPaymentInstructions(false);\n    }, [\n        existingOrderId,\n        hasRequiredInfo,\n        instructions, \n        persistentInstructions,\n        fetchPaymentInstructions\n    ]);\n\n    const regeneratePaymentInstructions = useCallback(async () => {\n        // Stop any ongoing polling and timers\n        stopPolling();\n        if (countdownIntervalRef.current) {\n            clearInterval(countdownIntervalRef.current);\n            countdownIntervalRef.current = null;\n        }\n\n        // Reset all relevant states\n        setInstructions(null);\n        setPaymentStatus('pending');\n        setPaymentFailedReason(null);\n        setExpiryTime(null);\n        \n        // Clear persistent data\n        if (clearOrderData) {\n            clearOrderData();\n        }\n        \n        await fetchPaymentInstructions(true);\n    }, [\n        existingOrderId,\n        clearOrderData,\n        stopPolling,\n        fetchPaymentInstructions\n    ]);\n\n    // Payment status polling\n    const checkPaymentStatus = useCallback(async () => {\n        const currentOrderId = orderId || persistentOrderId || existingOrderId;\n        if (!currentOrderId) return;\n\n        // Get the order key and status check nonce from the current instructions data\n        const currentInstructions = instructions || persistentInstructions;\n        const orderKey = currentInstructions?.details?.order_key;\n        const statusCheckNonce = currentInstructions?.status_check_nonce;\n        \n        if (!orderKey || !statusCheckNonce) {\n            console.warn('PayID Instructions: Missing order key or nonce for status check');\n            return;\n        }\n\n        try {\n            const statusData = new FormData();\n            statusData.append('action', 'monoova_check_payment_status');\n            statusData.append('order_id', currentOrderId);\n            statusData.append('order_key', orderKey); // Add order key parameter\n            statusData.append('nonce', statusCheckNonce); // Use the nonce from backend\n\n\n            const response = await fetch(settings.ajax_url, {\n                method: 'POST',\n                body: statusData\n            });\n\n            const result = await response.json();\n            // mock data with a delay 10s\n            // const result = await new Promise((resolve) => {\n            //     setTimeout(() => {\n            //         resolve({\n            //             success: true,\n            //             data: {\n            //                 status: 'failed',\n            //                 reason: 'Insufficient funds',\n            //             }\n            //         });\n            //     }, 10000);\n            // });\n\n            if (result.success && result.data.status !== 'pending') {\n                setPaymentStatus(result.data.status);\n                setPaymentFailedReason(result.data.reason || null);\n                stopPolling();\n            }\n        } catch (error) {\n            console.error('PayID Instructions: Error checking payment status:', error);\n        }\n    }, [orderId, persistentOrderId, existingOrderId, instructions, persistentInstructions, settings, stopPolling]);\n\n    // Start/stop polling\n    const startPolling = useCallback(() => {\n        if (pollingIntervalRef.current) return;\n        \n        // Initial check\n        checkPaymentStatus();\n        // Poll every 10 seconds\n        pollingIntervalRef.current = setInterval(checkPaymentStatus, 10000);\n    }, [checkPaymentStatus]);\n\n\n    // Countdown timer\n    const startCountdown = useCallback(() => {\n        if (!expiryTime || countdownIntervalRef.current) return;\n        \n        countdownIntervalRef.current = setInterval(() => {\n            const distance = expiryTime * 1000 - new Date().getTime();\n            if (distance < 0) {\n                setPaymentStatus('expired');\n                clearInterval(countdownIntervalRef.current);\n                countdownIntervalRef.current = null;\n                stopPolling();\n            }\n        }, 1000);\n    }, [expiryTime, stopPolling]);\n\n    // Function to reset just the payment status to restart polling\n    const resetPaymentStatus = useCallback(async () => {\n        const currentOrderId = orderId || persistentOrderId || existingOrderId;\n        if (!currentOrderId) {\n            console.warn('PayID Instructions: No order ID available for payment status reset');\n            return;\n        }\n\n        try {\n            // Get the order key from current instructions for security\n            const currentInstructions = instructions || persistentInstructions;\n            const orderKey = currentInstructions?.details?.order_key;\n            \n            if (!orderKey) {\n                console.warn('PayID Instructions: Missing order key for status reset');\n                return;\n            }\n\n            const resetData = new FormData();\n            resetData.append('action', 'monoova_reset_payment_status');\n            resetData.append('order_id', currentOrderId);\n            resetData.append('order_key', orderKey);\n            resetData.append('nonce', settings.reset_payment_status_nonce);\n\n            const response = await fetch(settings.ajax_url, {\n                method: 'POST',\n                body: resetData\n            });\n\n            const result = await response.json();\n            \n            if (result.success) {\n                // Reset the payment status to pending to restart polling\n                setPaymentStatus('pending');\n                setPaymentFailedReason(null);\n                \n                console.log('PayID Instructions: Payment status reset successfully');\n            } else {\n                console.error('PayID Instructions: Failed to reset payment status:', result.data?.message);\n                throw new Error(result.data?.message || 'Failed to reset payment status');\n            }\n        } catch (error) {\n            console.error('PayID Instructions: Error resetting payment status:', error);\n            setError(error.message);\n        }\n    }, [\n        orderId, \n        persistentOrderId, \n        existingOrderId, \n        instructions, \n        persistentInstructions, \n        settings\n    ]);\n\n    // Reset function\n    const resetStates = useCallback(() => {\n        setInstructions(null);\n        setOrderId(null);\n        setError(null);\n        setIsLoading(false);\n        setPaymentStatus('pending');\n        setExpiryTime(null);\n        \n        // Reset refs\n        isGeneratingInstructionsRef.current = false;\n        \n        // Stop timers\n        stopPolling();\n        if (countdownIntervalRef.current) {\n            clearInterval(countdownIntervalRef.current);\n            countdownIntervalRef.current = null;\n        }\n    }, [stopPolling]);\n\n    // Initialize payment instructions when conditions are met\n    useEffect(() => {\n        const initializeInstructions = async () => {\n            if (shouldUseExistingInstructions) {\n                setInstructions(persistentInstructions);\n                setOrderId(persistentOrderId || existingOrderId);\n                showContainer();\n                \n                // Start polling and countdown if we have persistent instructions\n                if (persistentInstructions?.expiry_timestamp) {\n                    setExpiryTime(persistentInstructions.expiry_timestamp);\n                }\n                return;\n            }\n\n            // Check if we have required data to generate instructions\n            // Only proceed if guest has provided all required information\n            if (existingOrderId &&\n                hasRequiredInfo && \n                !isLoading && \n                !instructions &&\n                !isGeneratingInstructionsRef.current) {\n                \n                await generatePaymentInstructions();\n                showContainer();\n            }\n        };\n\n        // Use a small delay to prevent rapid successive calls\n        const timeoutId = setTimeout(initializeInstructions, 100);\n        \n        return () => clearTimeout(timeoutId);\n    }, [\n        existingOrderId,\n        hasRequiredInfo, // Replace individual billing field dependencies with this\n        shouldUseExistingInstructions,\n        persistentInstructions,\n        persistentOrderId,\n        isLoading,\n        instructions,\n        generatePaymentInstructions,\n        showContainer\n    ]);\n\n    // Start polling when we have instructions\n    useEffect(() => {\n        if ((instructions || persistentInstructions) && paymentStatus === 'pending') {\n            startPolling();\n            startCountdown();\n        }\n\n        return () => {\n            stopPolling();\n            if (countdownIntervalRef.current) {\n                clearInterval(countdownIntervalRef.current);\n            }\n        };\n    }, [instructions, persistentInstructions, paymentStatus, startPolling, startCountdown, stopPolling]);\n\n    return {\n        isLoading,\n        instructions: instructions || persistentInstructions,\n        error,\n        orderId: orderId || persistentOrderId || existingOrderId, // Return existing order ID if available\n        paymentStatus,\n        paymentFailedReason,\n        expiryTime,\n        resetStates,\n        regeneratePaymentInstructions, // Expose the new function\n        resetPaymentStatus, // Expose the status reset function\n        containerRef: targetRef,\n        isInitialized: shouldUseExistingInstructions || !!instructions\n    };\n};\n", "import { useEffect, useRef } from '@wordpress/element';\n\n// Global container management for persistent Primer checkout\nclass PrimerCheckoutManager {\n    constructor() {\n        this.containers = new Map();\n        this.activeContainers = new Set();\n        this.isInitialized = false;\n    }\n\n    // Create or get existing container\n    getOrCreateContainer(paymentMethodId, containerId) {\n        const containerKey = `${paymentMethodId}_${containerId}`;\n        \n        if (!this.containers.has(containerKey)) {\n            // Create container element\n            const container = document.createElement('div');\n            container.id = containerId;\n            container.className = 'primer-checkout-persistent-container';\n            container.style.cssText = `\n                position: absolute;\n                top: -9999px;\n                left: -9999px;\n                width: 100%;\n                visibility: hidden;\n                opacity: 0;\n                pointer-events: none;\n                transition: all 0.3s ease;\n            `;\n            \n            // Append to body to keep it persistent\n            document.body.appendChild(container);\n            \n            this.containers.set(containerKey, {\n                element: container,\n                isInitialized: false,\n                isVisible: false,\n                orderId: null, // Store orderId for payment completion\n                clientToken: null // Store clientToken as well\n            });\n        }\n        \n        return this.containers.get(containerKey);\n    }\n\n    // Show container in target element\n    showContainer(paymentMethodId, containerId, targetElement) {\n        const containerKey = `${paymentMethodId}_${containerId}`;\n        const containerInfo = this.containers.get(containerKey);\n        \n        if (containerInfo && containerInfo.element) {\n            // Hide all other containers first\n            this.hideAllContainers();\n            \n            // Move container to target and show it\n            if (targetElement) {\n                targetElement.appendChild(containerInfo.element);\n                containerInfo.element.style.cssText = `\n                    position: relative;\n                    top: auto;\n                    left: auto;\n                    width: 100%;\n                    visibility: visible;\n                    opacity: 1;\n                    pointer-events: auto;\n                    transition: all 0.3s ease;\n                `;\n                containerInfo.isVisible = true;\n                this.activeContainers.add(containerKey);\n            }\n        }\n    }\n\n    // Hide container\n    hideContainer(paymentMethodId, containerId) {\n        const containerKey = `${paymentMethodId}_${containerId}`;\n        const containerInfo = this.containers.get(containerKey);\n        \n        if (containerInfo && containerInfo.element) {\n            // Move back to body and hide\n            document.body.appendChild(containerInfo.element);\n            containerInfo.element.style.cssText = `\n                position: absolute;\n                top: -9999px;\n                left: -9999px;\n                width: 100%;\n                visibility: hidden;\n                opacity: 0;\n                pointer-events: none;\n                transition: all 0.3s ease;\n            `;\n            containerInfo.isVisible = false;\n            this.activeContainers.delete(containerKey);\n        }\n    }\n\n    // Hide all containers\n    hideAllContainers() {\n        this.containers.forEach((containerInfo, containerKey) => {\n            if (containerInfo.isVisible) {\n                this.hideContainer(containerKey.split('_')[0], containerKey.split('_')[1]);\n            }\n        });\n    }\n\n    // Clear order data for a specific container\n    clearOrderData(paymentMethodId, containerId) {\n        const containerKey = `${paymentMethodId}_${containerId}`;\n        const containerInfo = this.containers.get(containerKey);\n\n        if (containerInfo) {\n            containerInfo.orderId = null;\n            containerInfo.clientToken = null;\n            containerInfo.instructions = null; // Also clear instructions\n            containerInfo.isInitialized = false; // Reset initialization state\n        }\n    }\n\n    // Set initialization status\n    setContainerInitialized(paymentMethodId, containerId) {\n        const containerKey = `${paymentMethodId}_${containerId}`;\n        const containerInfo = this.containers.get(containerKey);\n        if (containerInfo) {\n            containerInfo.isInitialized = true;\n        }\n    }\n\n    // Check if container is initialized\n    isContainerInitialized(paymentMethodId, containerId) {\n        const containerKey = `${paymentMethodId}_${containerId}`;\n        const containerInfo = this.containers.get(containerKey);\n        return containerInfo ? containerInfo.isInitialized : false;\n    }\n\n    // Set order and token data\n    setOrderData(paymentMethodId, containerId, orderId, clientToken, instructions = null) {\n        const containerKey = `${paymentMethodId}_${containerId}`;\n        const containerInfo = this.containers.get(containerKey);\n        if (containerInfo) {\n            containerInfo.orderId = orderId;\n            containerInfo.clientToken = clientToken;\n            containerInfo.instructions = instructions; // Add instructions storage (using for PayID/Bank Transfer)\n        } else {\n            console.warn(`[PrimerCheckoutManager] Container ${containerKey} not found for setting order data`);\n        }\n    }\n\n    // Get order data\n    getOrderData(paymentMethodId, containerId) {\n        const containerKey = `${paymentMethodId}_${containerId}`;\n        const containerInfo = this.containers.get(containerKey);\n        const result = containerInfo ? {\n            orderId: containerInfo.orderId,\n            clientToken: containerInfo.clientToken,\n            instructions: containerInfo.instructions // Include instructions in returned data\n        } : { orderId: null, clientToken: null, instructions: null };\n        \n        return result;\n    }\n\n    // Get container element\n    getContainerElement(paymentMethodId, containerId) {\n        const containerKey = `${paymentMethodId}_${containerId}`;\n        const containerInfo = this.containers.get(containerKey);\n        return containerInfo ? containerInfo.element : null;\n    }\n\n    // Cleanup\n    cleanup() {\n        this.containers.forEach((containerInfo) => {\n            if (containerInfo.element && containerInfo.element.parentNode) {\n                containerInfo.element.parentNode.removeChild(containerInfo.element);\n            }\n        });\n        this.containers.clear();\n        this.activeContainers.clear();\n    }\n}\n\n// Global instance\nconst primerCheckoutManager = new PrimerCheckoutManager();\n\n// Hook for persistent container management\nexport const usePersistentPaymentDetailsContainer = (paymentMethodId, containerId) => {\n    const targetRef = useRef(null);\n    const isActiveRef = useRef(false);\n    \n    // Generate persistent container ID\n    const persistentContainerId = `persistent-${paymentMethodId}-${containerId}`;\n\n    useEffect(() => {\n        // Create or get container\n        const containerInfo = primerCheckoutManager.getOrCreateContainer(paymentMethodId, containerId);\n        \n        // Update the container's ID to match what Primer expects\n        if (containerInfo.element) {\n            containerInfo.element.id = persistentContainerId;\n        }\n        \n        // Show container when component mounts\n        if (targetRef.current && !isActiveRef.current) {\n            primerCheckoutManager.showContainer(paymentMethodId, containerId, targetRef.current);\n            isActiveRef.current = true;\n        }\n\n        // Cleanup on unmount\n        return () => {\n            if (isActiveRef.current) {\n                primerCheckoutManager.hideContainer(paymentMethodId, containerId);\n                isActiveRef.current = false;\n            }\n        };\n    }, [paymentMethodId, containerId]);\n\n    return {\n        targetRef,\n        containerElement: primerCheckoutManager.getContainerElement(paymentMethodId, containerId),\n        containerIdActive: persistentContainerId, // Return the active container ID for Primer\n        isContainerInitialized: primerCheckoutManager.isContainerInitialized(paymentMethodId, containerId),\n        setContainerInitialized: () => primerCheckoutManager.setContainerInitialized(paymentMethodId, containerId),\n        // Order data persistence methods\n        setOrderData: (orderId, clientToken, instructions = null) => primerCheckoutManager.setOrderData(paymentMethodId, containerId, orderId, clientToken, instructions),\n        getOrderData: () => primerCheckoutManager.getOrderData(paymentMethodId, containerId),\n        clearOrderData: () => {\n            primerCheckoutManager.clearOrderData(paymentMethodId, containerId);\n        },\n        showContainer: () => {\n            if (targetRef.current && !isActiveRef.current) {\n                primerCheckoutManager.showContainer(paymentMethodId, containerId, targetRef.current);\n                isActiveRef.current = true;\n            }\n        },\n        hideContainer: () => {\n            if (isActiveRef.current) {\n                primerCheckoutManager.hideContainer(paymentMethodId, containerId);\n                isActiveRef.current = false;\n            }\n        }\n    };\n};\n\n// Cleanup function for page unload\nif (typeof window !== 'undefined') {\n    window.addEventListener('beforeunload', () => {\n        primerCheckoutManager.cleanup();\n    });\n}\n\nexport default primerCheckoutManager;\n", "module.exports = wc.wcBlocksData;", "module.exports = wc.wcBlocksRegistry;", "module.exports = wc.wcSettings;", "module.exports = window[\"wp\"][\"components\"];", "module.exports = window[\"wp\"][\"data\"];", "module.exports = window[\"wp\"][\"element\"];", "module.exports = window[\"wp\"][\"htmlEntities\"];", "module.exports = window[\"wp\"][\"i18n\"];", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "/**\n * Monoova PayID Block for WooCommerce Blocks\n * \n * Uses ES6 imports with defensive programming to handle cases where WooCommerce dependencies \n * may not be available (edit mode, missing plugins, etc.)\n */\n\nimport { registerPaymentMethod } from '@woocommerce/blocks-registry';\nimport { getSetting } from '@woocommerce/settings';\nimport { decodeEntities } from '@wordpress/html-entities';\nimport { __ } from '@wordpress/i18n';\nimport { useState, useEffect } from '@wordpress/element';\nimport { useSelect } from '@wordpress/data';\nimport { CHECKOUT_STORE_KEY } from '@woocommerce/block-data';\n\n// Import custom hooks and components\nimport { usePayIDPaymentInstructions } from '../hooks/usePayIDPaymentInstructions';\nimport { PayIDInstructionsContainer } from './PayIDInstructionsContainer';\n\n// Load QR Code library\n// if (typeof window !== 'undefined' && !window.QRCode) {\n//     const script = document.createElement('script');\n//     script.src = 'https://cdnjs.cloudflare.com/ajax/libs/qrcodejs/1.0.0/qrcode.min.js';\n//     script.async = true;\n//     document.head.appendChild(script);\n// }\n\n\n// If registerPaymentMethod is not available, we can't register the payment method\nif (typeof registerPaymentMethod !== 'function') {\n    console.warn('Monoova PayID Block: registerPaymentMethod not available. Available globals:', Object.keys(window.wc || {}));\n} else {\n    // Try to get settings\n    let settings = {};\n    if (typeof getSetting === 'function') {\n        try {\n            settings = getSetting('monoova_payid_data', {});\n        } catch (error) {\n            console.log('Monoova PayID Block: getSetting failed:', error);\n        }\n    }\n\n    const defaultLabel = __('PayID / Bank Transfer', 'monoova-payments-for-woocommerce');\n    const label = decodeEntities(settings.title) || defaultLabel;\n\n    /**\n     * Content component for PayID with payment instructions\n     */\n    const Content = ({ \n        billing, \n        shippingData, \n        checkoutStatus, \n        paymentStatus, \n        eventRegistration, \n        emitResponse \n    }) => {\n        const [currentPaymentStatus, setCurrentPaymentStatus] = useState('pending');\n        \n        // Get the existing order ID from WooCommerce checkout store\n        const { orderId } = useSelect((select) => {\n            const store = select(CHECKOUT_STORE_KEY);\n            return {\n                orderId: store.getOrderId(),\n            };\n        });\n        \n        // Helper function to check if required guest information is available\n        const hasRequiredGuestInfo = () => {\n            const billingAddress = billing?.billingAddress;\n            if (!billingAddress) return false;\n            \n            // Required fields for guest customers to generate PayID instructions\n            return !!(\n                billingAddress.email &&\n                billingAddress.first_name &&\n                billingAddress.last_name &&\n                billingAddress.address_1 &&\n                billingAddress.city &&\n                billingAddress.postcode &&\n                billingAddress.country\n            );\n        };\n        \n        // Use the PayID payment instructions hook\n        const {\n            isLoading,\n            instructions,\n            error,\n            paymentStatus: hookPaymentStatus,\n            paymentFailedReason,\n            expiryTime,\n            resetStates,\n            regeneratePaymentInstructions, // Get the regeneration function\n            resetPaymentStatus, // Get the status reset function\n            containerRef,\n            isInitialized\n        } = usePayIDPaymentInstructions({\n            settings,\n            billing,\n            shippingData,\n            orderId, // Pass the existing order ID from checkout store\n            containerId: \"payid-instructions-container\",\n            paymentMethodId: \"monoova_payid\",\n            hasRequiredInfo: hasRequiredGuestInfo() // Pass the validation result\n        });\n\n        useEffect(() => {\n            // Selector for the block checkout's \"Place Order\" button\n            const placeOrderButton = document.querySelector('.wc-block-components-button.wp-element-button.wc-block-components-checkout-place-order-button');\n\n            if (placeOrderButton) {\n                // Hide the button when Monoova Card is selected\n                placeOrderButton.style.display = 'none';\n            }\n\n            // Cleanup function: runs when component unmounts (e.g., user selects another payment method)\n            return () => {\n                if (placeOrderButton) {\n                    // Restore the button's default display style\n                    placeOrderButton.style.display = '';\n                }\n            };\n        }, []);\n\n        // Update payment status\n        useEffect(() => {\n            if (hookPaymentStatus !== 'pending') {\n                setCurrentPaymentStatus(hookPaymentStatus);\n            }\n        }, [hookPaymentStatus]);\n\n        const description = decodeEntities(settings.description || '');\n\n        // Handle payment status changes from the component\n        const handlePaymentStatusChange = (status) => {\n            if (status === 'pending') {\n                // This will just change the UI view\n                setCurrentPaymentStatus('pending');\n                // This will restart the polling in the hook\n                if (resetPaymentStatus) {\n                    resetPaymentStatus();\n                }\n            } else {\n                setCurrentPaymentStatus(status);\n            }\n        };\n\n        // Loading state\n        if (isLoading && !instructions) {\n            return (\n                <div className=\"monoova-payid-content\">\n                    <div\n                        className=\"monoova-payid-description\"\n                        dangerouslySetInnerHTML={{ __html: description }}\n                    />\n                    <div className=\"monoova-payid-loading\">\n                        <div className=\"spinner\"></div>\n                        <p>{settings.strings?.loading || 'Generating payment instructions...'}</p>\n                    </div>\n                </div>\n            );\n        }\n\n        // Show message when required information is missing\n        if (!hasRequiredGuestInfo() && !instructions) {\n            return (\n                <div className=\"monoova-payid-content\">\n                    <div\n                        className=\"monoova-payid-description\"\n                        dangerouslySetInnerHTML={{ __html: description }}\n                    />\n                    <div className=\"monoova-payid-info\">\n                        <p>Please complete your billing information to generate PayID payment instructions.</p>\n                        <small>Required: Email, Name, Address, City, Postcode, and Country</small>\n                    </div>\n                </div>\n            );\n        }\n\n        // Error state\n        if (error) {\n            return (\n                <div className=\"monoova-payid-content\">\n                    <div\n                        className=\"monoova-payid-description\"\n                        dangerouslySetInnerHTML={{ __html: description }}\n                    />\n                    <div className=\"monoova-payid-error\">\n                        <p>{error}</p>\n                        <button type=\"button\" onClick={resetStates}>\n                            Try Again\n                        </button>\n                    </div>\n                </div>\n            );\n        }\n\n        return (\n            <div className=\"monoova-payid-content\">\n                <div\n                    className=\"monoova-payid-description\"\n                    dangerouslySetInnerHTML={{ __html: description }}\n                />\n                \n                {/* Target container for persistent PayID instructions */}\n                <div ref={containerRef} className=\"payid-container-target\">\n                    {instructions && (\n                        <PayIDInstructionsContainer\n                            instructions={instructions}\n                            settings={settings}\n                            paymentStatus={currentPaymentStatus}\n                            expiryTime={expiryTime}\n                            paymentFailedReason={paymentFailedReason}\n                            onPaymentStatusChange={handlePaymentStatusChange}\n                            onRegenerateInstructions={regeneratePaymentInstructions}\n                            orderId={orderId}\n                        />\n                    )}\n                </div>\n            </div>\n        );\n    };\n\n    /**\n     * Label component\n     */\n    const Label = (props) => {\n        const { PaymentMethodLabel } = props.components || {};\n        return (\n            <div style={{\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center',\n                width: '100%'\n            }}>\n                <span style={{ fontWeight: 600 }}>{label}</span>\n                <img\n                    src={`${settings.plugin_url || '/wp-content/plugins/monoova-payments-for-woocommerce/'}assets/images/payid-logo.svg`}\n                    alt=\"PayID logo\"\n                    style={{\n                        height: '24px',\n                        width: 'auto'\n                    }}\n                />\n            </div>\n        );\n    };\n\n    /**\n     * Monoova PayID payment method config object\n    /**\n     * Edit component (for block editor - simplified view)\n     */\n    const Edit = () => {\n        const description = decodeEntities(settings.description || '');\n        return (\n            <div className=\"monoova-payid-content\">\n                <div\n                    className=\"monoova-payid-description\"\n                    dangerouslySetInnerHTML={{ __html: description }}\n                />\n                <div className=\"payid-instructions-preview\">\n                    <div style={{\n                        border: '1px dashed #ccc',\n                        padding: '20px',\n                        textAlign: 'center',\n                        borderRadius: '8px',\n                        color: '#666',\n                        margin: '15px 0'\n                    }}>\n                        <p><strong>PayID / Bank Transfer Instructions</strong></p>\n                        <p>Payment instructions will appear here during checkout</p>\n                    </div>\n                </div>\n            </div>\n        );\n    };\n\n    /**\n     * Monoova PayID payment method config object\n     */\n    const MonoovaPayID = {\n        name: 'monoova_payid',\n        label: <Label />,\n        content: <Content />,\n        edit: <Edit />,\n        canMakePayment: function() { return true; },\n        ariaLabel: label,\n        supports: {\n            features: settings.supports || []\n        },\n        icons: settings.icons || null\n    };\n\n    // Register the payment method\n    try {\n        registerPaymentMethod(MonoovaPayID);\n    } catch (error) {\n        console.error('Monoova PayID Block: Failed to register payment method:', error);\n    }\n}"], "names": ["useState", "useEffect", "NavigationCountdown", "initialSeconds", "redirectUrl", "message", "countdown", "setCountdown", "window", "location", "href", "timer", "setTimeout", "prev", "clearTimeout", "formattedMessage", "replace", "toString", "React", "createElement", "className", "style", "marginTop", "padding", "backgroundColor", "border", "borderRadius", "textAlign", "fontSize", "color", "useRef", "RadioControl", "<PERSON><PERSON>", "QRCodeDisplay", "payload", "size", "qrRef", "current", "QRCode", "innerHTML", "text", "width", "height", "colorDark", "colorLight", "correctLevel", "CorrectLevel", "H", "ref", "id", "Copy<PERSON><PERSON><PERSON>", "onCopy", "copied", "setCopied", "handleCopy", "navigator", "clipboard", "writeText", "err", "console", "error", "type", "onClick", "title", "viewBox", "fill", "xmlns", "d", "stroke", "CountdownTimer", "expiryTimestamp", "onExpired", "strings", "timeLeft", "setTimeLeft", "calculateTimeLeft", "distance", "Date", "getTime", "days", "Math", "floor", "hours", "minutes", "seconds", "timeArr", "push", "join", "initialTime", "interval", "setInterval", "newTime", "clearInterval", "now", "expires_in", "toLocaleString", "PayIDInstructionsContainer", "instructions", "settings", "paymentStatus", "expiryTime", "paymentFailedReason", "onPaymentStatusChange", "onRegenerateInstructions", "orderId", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedMethod", "details", "view_order_url", "showPayID", "payment_types", "includes", "payid_value", "showBank", "bank_bsb", "bank_account_number", "showMethodSwitcher", "handleExpired", "handlePlaceNewOrder", "handlePayAgain", "generateOrderReceivedUrl", "urlParams", "URLSearchParams", "search", "orderKey", "get", "orderReceivedUrl", "order_received_url", "renderPaymentStatus", "payment_confirmed", "payment_confirmed_message", "variant", "view_order_details", "redirecting_to_order_page", "payment_expired", "payment_expired_message", "place_new_order", "payment_failed", "payment_failed_message", "fontStyle", "dangerouslySetInnerHTML", "__html", "pay_again", "pay_with", "selected", "options", "label", "payid_method", "value", "bank_method", "onChange", "v", "payment_instructions_description", "scan_pay", "amount_to_pay_formatted", "scan_pay_description", "src", "plugin_url", "alt", "show_reference_field", "payment_reference", "include_reference_with_payment", "reference", "bank_transfer_details", "account_name", "bank_account_name", "bsb", "account_number", "payment_confirmed_automatically", "useCallback", "usePersistentPaymentDetailsContainer", "usePayIDPaymentInstructions", "billing", "shippingData", "existingOrderId", "containerId", "paymentMethodId", "hasRequiredInfo", "isLoading", "setIsLoading", "setInstructions", "setError", "setOrderId", "setPaymentStatus", "setPaymentFailedReason", "setExpiryTime", "isGeneratingInstructionsRef", "pollingIntervalRef", "countdownIntervalRef", "targetRef", "containerElement", "containerIdActive", "isContainerInitialized", "setContainerInitialized", "setOrderData", "getOrderData", "clearOrderData", "showContainer", "<PERSON><PERSON><PERSON><PERSON>", "persistentData", "persistentInstructions", "persistentOrderId", "shouldUseExistingInstructions", "stopPolling", "fetchPaymentInstructions", "isRegeneration", "log", "instructionsData", "FormData", "append", "generate_instructions_nonce", "instructionsResponse", "fetch", "ajax_url", "method", "body", "instructionsResult", "json", "success", "Error", "data", "paymentInstructions", "expiry_timestamp", "generatePaymentInstructions", "regeneratePaymentInstructions", "checkPaymentStatus", "currentOrderId", "currentInstructions", "order_key", "statusCheckNonce", "status_check_nonce", "warn", "statusData", "response", "result", "status", "reason", "startPolling", "startCountdown", "resetPaymentStatus", "resetData", "reset_payment_status_nonce", "resetStates", "initializeInstructions", "timeoutId", "containerRef", "isInitialized", "PrimerCheckoutManager", "constructor", "containers", "Map", "activeContainers", "Set", "getOrCreateContainer", "containerKey", "has", "container", "document", "cssText", "append<PERSON><PERSON><PERSON>", "set", "element", "isVisible", "clientToken", "targetElement", "containerInfo", "hideAllContainers", "add", "delete", "for<PERSON>ach", "split", "getContainerElement", "cleanup", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "clear", "primerCheckoutManager", "isActiveRef", "persistentContainerId", "addEventListener", "registerPaymentMethod", "getSetting", "decodeEntities", "__", "useSelect", "CHECKOUT_STORE_KEY", "Object", "keys", "wc", "defaultLabel", "Content", "checkoutStatus", "eventRegistration", "emitResponse", "currentPaymentStatus", "setCurrentPaymentStatus", "select", "store", "getOrderId", "hasRequiredGuestInfo", "billing<PERSON><PERSON>ress", "email", "first_name", "last_name", "address_1", "city", "postcode", "country", "hookPaymentStatus", "placeOrderButton", "querySelector", "display", "description", "handlePaymentStatusChange", "loading", "Label", "props", "PaymentMethodLabel", "components", "justifyContent", "alignItems", "fontWeight", "Edit", "margin", "MonoovaPayID", "name", "content", "edit", "canMakePayment", "aria<PERSON><PERSON><PERSON>", "supports", "features", "icons"], "sourceRoot": ""}