/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "./assets/js/src/blocks/primer-checkout-container.js":
/*!***********************************************************!*\
  !*** ./assets/js/src/blocks/primer-checkout-container.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   PrimerCheckoutContainer: () => (/* binding */ PrimerCheckoutContainer)
/* harmony export */ });
const PrimerCheckoutContainer = ({
  containerId,
  isLoading = false,
  isInitialized = false,
  containerRef = null
}) => {
  return /*#__PURE__*/React.createElement("div", {
    className: "embedded-card-form",
    style: {
      marginTop: '20px'
    }
  }, isLoading && !isInitialized && /*#__PURE__*/React.createElement("div", {
    className: "primer-loading-indicator",
    style: {
      padding: '20px',
      textAlign: 'center',
      color: '#666'
    }
  }, "Loading payment form..."), /*#__PURE__*/React.createElement("div", {
    ref: containerRef,
    className: "primer-container-target"
  }, /*#__PURE__*/React.createElement("div", {
    id: containerId
  })));
};

/***/ }),

/***/ "./assets/js/src/hooks/usePersistentPaymentDetailsContainer.js":
/*!*********************************************************************!*\
  !*** ./assets/js/src/hooks/usePersistentPaymentDetailsContainer.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__),
/* harmony export */   usePersistentPaymentDetailsContainer: () => (/* binding */ usePersistentPaymentDetailsContainer)
/* harmony export */ });
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/element */ "@wordpress/element");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__);


// Global container management for persistent Primer checkout
class PrimerCheckoutManager {
  constructor() {
    this.containers = new Map();
    this.activeContainers = new Set();
    this.isInitialized = false;
  }

  // Create or get existing container
  getOrCreateContainer(paymentMethodId, containerId) {
    const containerKey = `${paymentMethodId}_${containerId}`;
    if (!this.containers.has(containerKey)) {
      // Create container element
      const container = document.createElement('div');
      container.id = containerId;
      container.className = 'primer-checkout-persistent-container';
      container.style.cssText = `
                position: absolute;
                top: -9999px;
                left: -9999px;
                width: 100%;
                visibility: hidden;
                opacity: 0;
                pointer-events: none;
                transition: all 0.3s ease;
            `;

      // Append to body to keep it persistent
      document.body.appendChild(container);
      this.containers.set(containerKey, {
        element: container,
        isInitialized: false,
        isVisible: false,
        orderId: null,
        // Store orderId for payment completion
        clientToken: null // Store clientToken as well
      });
    }
    return this.containers.get(containerKey);
  }

  // Show container in target element
  showContainer(paymentMethodId, containerId, targetElement) {
    const containerKey = `${paymentMethodId}_${containerId}`;
    const containerInfo = this.containers.get(containerKey);
    if (containerInfo && containerInfo.element) {
      // Hide all other containers first
      this.hideAllContainers();

      // Move container to target and show it
      if (targetElement) {
        targetElement.appendChild(containerInfo.element);
        containerInfo.element.style.cssText = `
                    position: relative;
                    top: auto;
                    left: auto;
                    width: 100%;
                    visibility: visible;
                    opacity: 1;
                    pointer-events: auto;
                    transition: all 0.3s ease;
                `;
        containerInfo.isVisible = true;
        this.activeContainers.add(containerKey);
      }
    }
  }

  // Hide container
  hideContainer(paymentMethodId, containerId) {
    const containerKey = `${paymentMethodId}_${containerId}`;
    const containerInfo = this.containers.get(containerKey);
    if (containerInfo && containerInfo.element) {
      // Move back to body and hide
      document.body.appendChild(containerInfo.element);
      containerInfo.element.style.cssText = `
                position: absolute;
                top: -9999px;
                left: -9999px;
                width: 100%;
                visibility: hidden;
                opacity: 0;
                pointer-events: none;
                transition: all 0.3s ease;
            `;
      containerInfo.isVisible = false;
      this.activeContainers.delete(containerKey);
    }
  }

  // Hide all containers
  hideAllContainers() {
    this.containers.forEach((containerInfo, containerKey) => {
      if (containerInfo.isVisible) {
        this.hideContainer(containerKey.split('_')[0], containerKey.split('_')[1]);
      }
    });
  }

  // Clear order data for a specific container
  clearOrderData(paymentMethodId, containerId) {
    const containerKey = `${paymentMethodId}_${containerId}`;
    const containerInfo = this.containers.get(containerKey);
    if (containerInfo) {
      containerInfo.orderId = null;
      containerInfo.clientToken = null;
      containerInfo.instructions = null; // Also clear instructions
      containerInfo.isInitialized = false; // Reset initialization state
    }
  }

  // Set initialization status
  setContainerInitialized(paymentMethodId, containerId) {
    const containerKey = `${paymentMethodId}_${containerId}`;
    const containerInfo = this.containers.get(containerKey);
    if (containerInfo) {
      containerInfo.isInitialized = true;
    }
  }

  // Check if container is initialized
  isContainerInitialized(paymentMethodId, containerId) {
    const containerKey = `${paymentMethodId}_${containerId}`;
    const containerInfo = this.containers.get(containerKey);
    return containerInfo ? containerInfo.isInitialized : false;
  }

  // Set order and token data
  setOrderData(paymentMethodId, containerId, orderId, clientToken, instructions = null) {
    const containerKey = `${paymentMethodId}_${containerId}`;
    const containerInfo = this.containers.get(containerKey);
    if (containerInfo) {
      containerInfo.orderId = orderId;
      containerInfo.clientToken = clientToken;
      containerInfo.instructions = instructions; // Add instructions storage (using for PayID/Bank Transfer)
    } else {
      console.warn(`[PrimerCheckoutManager] Container ${containerKey} not found for setting order data`);
    }
  }

  // Get order data
  getOrderData(paymentMethodId, containerId) {
    const containerKey = `${paymentMethodId}_${containerId}`;
    const containerInfo = this.containers.get(containerKey);
    const result = containerInfo ? {
      orderId: containerInfo.orderId,
      clientToken: containerInfo.clientToken,
      instructions: containerInfo.instructions // Include instructions in returned data
    } : {
      orderId: null,
      clientToken: null,
      instructions: null
    };
    return result;
  }

  // Get container element
  getContainerElement(paymentMethodId, containerId) {
    const containerKey = `${paymentMethodId}_${containerId}`;
    const containerInfo = this.containers.get(containerKey);
    return containerInfo ? containerInfo.element : null;
  }

  // Cleanup
  cleanup() {
    this.containers.forEach(containerInfo => {
      if (containerInfo.element && containerInfo.element.parentNode) {
        containerInfo.element.parentNode.removeChild(containerInfo.element);
      }
    });
    this.containers.clear();
    this.activeContainers.clear();
  }
}

// Global instance
const primerCheckoutManager = new PrimerCheckoutManager();

// Hook for persistent container management
const usePersistentPaymentDetailsContainer = (paymentMethodId, containerId) => {
  const targetRef = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);
  const isActiveRef = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);

  // Generate persistent container ID
  const persistentContainerId = `persistent-${paymentMethodId}-${containerId}`;
  (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {
    // Create or get container
    const containerInfo = primerCheckoutManager.getOrCreateContainer(paymentMethodId, containerId);

    // Update the container's ID to match what Primer expects
    if (containerInfo.element) {
      containerInfo.element.id = persistentContainerId;
    }

    // Show container when component mounts
    if (targetRef.current && !isActiveRef.current) {
      primerCheckoutManager.showContainer(paymentMethodId, containerId, targetRef.current);
      isActiveRef.current = true;
    }

    // Cleanup on unmount
    return () => {
      if (isActiveRef.current) {
        primerCheckoutManager.hideContainer(paymentMethodId, containerId);
        isActiveRef.current = false;
      }
    };
  }, [paymentMethodId, containerId]);
  return {
    targetRef,
    containerElement: primerCheckoutManager.getContainerElement(paymentMethodId, containerId),
    containerIdActive: persistentContainerId,
    // Return the active container ID for Primer
    isContainerInitialized: primerCheckoutManager.isContainerInitialized(paymentMethodId, containerId),
    setContainerInitialized: () => primerCheckoutManager.setContainerInitialized(paymentMethodId, containerId),
    // Order data persistence methods
    setOrderData: (orderId, clientToken, instructions = null) => primerCheckoutManager.setOrderData(paymentMethodId, containerId, orderId, clientToken, instructions),
    getOrderData: () => primerCheckoutManager.getOrderData(paymentMethodId, containerId),
    clearOrderData: () => {
      primerCheckoutManager.clearOrderData(paymentMethodId, containerId);
    },
    showContainer: () => {
      if (targetRef.current && !isActiveRef.current) {
        primerCheckoutManager.showContainer(paymentMethodId, containerId, targetRef.current);
        isActiveRef.current = true;
      }
    },
    hideContainer: () => {
      if (isActiveRef.current) {
        primerCheckoutManager.hideContainer(paymentMethodId, containerId);
        isActiveRef.current = false;
      }
    }
  };
};

// Cleanup function for page unload
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    primerCheckoutManager.cleanup();
  });
}
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (primerCheckoutManager);

/***/ }),

/***/ "./assets/js/src/hooks/usePrimerCheckoutInitialization.js":
/*!****************************************************************!*\
  !*** ./assets/js/src/hooks/usePrimerCheckoutInitialization.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   usePrimerCheckoutInitialization: () => (/* binding */ usePrimerCheckoutInitialization)
/* harmony export */ });
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/element */ "@wordpress/element");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _usePersistentPaymentDetailsContainer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./usePersistentPaymentDetailsContainer */ "./assets/js/src/hooks/usePersistentPaymentDetailsContainer.js");


const usePrimerCheckoutInitialization = ({
  settings,
  billing,
  shippingData,
  orderId: existingOrderId,
  // Accept existing order ID from props
  containerId = "in-checkout-primer-sdk-container",
  paymentMethodId = "monoova_card",
  hasRequiredInfo = true // New parameter to control API calling
}) => {
  const [isInitialized, setIsInitialized] = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useState)(false);
  const [clientToken, setClientToken] = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useState)(null);
  const [orderId, setOrderId] = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useState)(existingOrderId || null); // Initialize with existing order ID
  const [isLoading, setIsLoading] = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useState)(false);
  const [billingRef, setBillingRef] = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useState)(null);

  // Use persistent container management
  const {
    targetRef,
    containerElement,
    containerIdActive,
    // This will be the actual ID of the persistent container
    isContainerInitialized,
    setContainerInitialized,
    setOrderData,
    // Store order data in persistent container
    getOrderData,
    // Retrieve order data from persistent container
    showContainer,
    hideContainer
  } = (0,_usePersistentPaymentDetailsContainer__WEBPACK_IMPORTED_MODULE_1__.usePersistentPaymentDetailsContainer)(paymentMethodId, containerId);

  // Use refs to prevent duplicate API calls
  const isCreatingOrderRef = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);
  const isInitializingPrimerRef = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);

  // Check if we should use existing initialized container
  const shouldUseExistingContainer = isContainerInitialized && containerElement;

  // Get persistent order data
  const persistentOrderData = getOrderData();
  const persistentOrderId = persistentOrderData?.orderId;
  const persistentClientToken = persistentOrderData?.clientToken;

  // Reset function for retry scenarios
  const resetStates = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {
    setIsInitialized(false);
    setClientToken(null);
    setOrderId(null);
    setIsLoading(false);
    setBillingRef(null);

    // Reset refs to allow new initialization
    isCreatingOrderRef.current = false;
    isInitializingPrimerRef.current = false;

    // Note: We don't reset the persistent container here as it should remain available
  }, []);
  const loadPrimerSDK = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {
    return new Promise((resolve, reject) => {
      // check if Primer and 2 CDNs are already loaded
      if (window.Primer && document.querySelector('link[href="https://sdk.primer.io/web/v2.54.5/Checkout.css"]') && document.querySelector('script[src="https://sdk.primer.io/web/v2.54.5/Primer.min.js"]')) {
        resolve();
        return;
      }
      // need to load 2 CDNs below:
      // <link rel="stylesheet" href="https://sdk.primer.io/web/v2.54.5/Checkout.css" />
      //<script src="https://sdk.primer.io/web/v2.54.5/Primer.min.js" integrity="sha384-yUCj6Q8h0Q6zFc35iT7v7pFoqlgpBD/xVr5SQxOgAnu2Cq286mf7IAyrFBJ8OsIa" crossorigin="anonymous"></script>

      // load css
      const link = document.createElement('link');
      link.rel = 'stylesheet';
      link.href = 'https://sdk.primer.io/web/v2.54.5/Checkout.css';
      document.head.appendChild(link);
      const script = document.createElement('script');
      script.src = 'https://sdk.primer.io/web/v2.54.5/Primer.min.js';
      script.crossOrigin = 'anonymous';
      script.onload = resolve;
      script.onerror = reject;
      document.head.appendChild(script);
    });
  }, []);
  const createOrderAndGetToken = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async () => {
    // Prevent duplicate API calls
    if (isCreatingOrderRef.current) {
      return null;
    }

    // If we already have a token, don't create another one
    if (clientToken || persistentClientToken) {
      const existingToken = clientToken || persistentClientToken;
      const finalOrderId = existingOrderId || orderId || persistentOrderId;

      // Update local state if using persistent data
      if (!clientToken && persistentClientToken) {
        setClientToken(persistentClientToken);
      }
      if (!orderId && finalOrderId) {
        setOrderId(finalOrderId);
      }
      return {
        token: existingToken,
        orderId: finalOrderId,
        checkoutData: null
      };
    }

    // Use existing order ID if available (WooCommerce checkout draft order)
    const finalOrderId = existingOrderId || orderId || persistentOrderId;
    if (!finalOrderId) {
      throw new Error('No order ID available for token generation');
    }
    try {
      isCreatingOrderRef.current = true;

      // Format address data with fallbacks for checkout page
      const formatAddress = address => ({
        first_name: address?.first_name || '',
        last_name: address?.last_name || '',
        company: address?.company || '',
        address_1: address?.address_1 || '',
        address_2: address?.address_2 || '',
        city: address?.city || '',
        state: address?.state || '',
        postcode: address?.postcode || '',
        country: address?.country || '',
        email: address?.email || '',
        phone: address?.phone || ''
      });

      // Use billing address or create empty object for checkout
      const billingAddress = billing?.billingAddress || {};

      // Generate client token for existing order via AJAX
      const response = await fetch(settings.ajax_url, {
        method: 'POST',
        body: new URLSearchParams({
          action: 'monoova_get_client_token',
          // Use the client token endpoint instead of express checkout
          _wpnonce: settings.generate_token_nonce,
          order_id: finalOrderId,
          // Pass the existing order ID,
          is_in_checkout: !!document.body.classList.contains('woocommerce-checkout'),
          billingAddress: JSON.stringify(formatAddress(billingAddress)),
          // Include billing address as fallback
          card_type: 'visa' // Default card type
        })
      });
      const result = await response.json();
      if (!result.success) {
        throw new Error(result.data?.message || 'Failed to generate client token');
      }
      const {
        clientToken: token
      } = result.data;
      setClientToken(token);
      setOrderId(finalOrderId);

      // Store in persistent container for later use
      setOrderData(finalOrderId, token);
      return {
        token,
        orderId: finalOrderId,
        checkoutData: result.data
      };
    } catch (error) {
      console.error('Error generating client token:', error);
      throw error;
    } finally {
      isCreatingOrderRef.current = false;
    }
  }, [settings, billing, shippingData, clientToken, orderId, existingOrderId, persistentClientToken, persistentOrderId]);

  // Handle payment completion
  const handlePaymentComplete = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (data, checkoutData) => {
    try {
      const primerPaymentId = data?.payment?.id;
      if (!primerPaymentId) {
        throw new Error('Payment ID not received from Primer');
      }

      // Get the most current order data at payment completion time
      const currentOrderData = getOrderData();
      const currentOrderId = orderId || currentOrderData?.orderId;
      const currentClientToken = clientToken || currentOrderData?.clientToken;
      if (!currentOrderId) {
        throw new Error('Order ID not available for payment completion');
      }

      // Complete payment via AJAX
      const response = await fetch(settings.ajax_url, {
        method: 'POST',
        body: new URLSearchParams({
          action: settings.ajax_complete_express_action,
          nonce: settings.express_checkout_nonce,
          orderId: currentOrderId,
          primerPaymentId: primerPaymentId,
          clientRef: checkoutData?.clientTransactionUniqueReference || '',
          is_in_checkout: !!document.body.classList.contains('woocommerce-checkout')
        })
      });
      const result = await response.json();
      if (result.success) {
        // Payment successful - redirect to success page
        window.location.href = result.data.redirect_url;
      } else {
        throw new Error(result.data?.message || 'Payment completion failed');
      }
    } catch (error) {
      console.error('Error completing payment:', error);
      alert(error.message || settings.i18n?.generic_error || 'Payment completion failed');
      resetStates();
    }
  }, [settings, orderId, clientToken, getOrderData, resetStates]);

  // Initialize Primer Universal Checkout with token
  const initializePrimerCheckout = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (token, checkoutData) => {
    // Prevent duplicate Primer initialization
    if (isInitializingPrimerRef.current) {
      return;
    }

    // If already initialized, don't initialize again
    if (isContainerInitialized || isInitialized) {
      return;
    }
    try {
      isInitializingPrimerRef.current = true;

      // Load Primer SDK
      await loadPrimerSDK();
      const {
        Primer
      } = window;
      if (!Primer) {
        throw new Error('Primer SDK not loaded');
      }
      const primerOptions = {
        container: `#${containerIdActive || containerId}`,
        // Use persistent container ID or fallback to original
        clientSessionCachingEnabled: true,
        style: {
          inputLabel: {
            fontFamily: settings.checkout_ui_styles?.input_label?.font_family || 'Helvetica, Arial, sans-serif',
            fontSize: settings.checkout_ui_styles?.input_label?.font_size || '14px',
            fontWeight: settings.checkout_ui_styles?.input_label?.font_weight || 'normal',
            color: settings.checkout_ui_styles?.input_label?.color || '#000000'
          },
          input: {
            base: {
              fontFamily: settings.checkout_ui_styles?.input?.font_family || 'Helvetica, Arial, sans-serif',
              fontSize: settings.checkout_ui_styles?.input?.font_size || '14px',
              fontWeight: settings.checkout_ui_styles?.input?.font_weight || 'normal',
              background: settings.checkout_ui_styles?.input?.background_color || '#FAFAFA',
              borderColor: settings.checkout_ui_styles?.input?.border_color || '#E8E8E8',
              borderRadius: settings.checkout_ui_styles?.input?.border_radius || '8px',
              color: settings.checkout_ui_styles?.input?.text_color || '#000000'
            },
            focus: {
              borderColor: '#2ab5c4',
              boxShadow: '0 0 0 2px rgba(42, 181, 196, 0.2)'
            },
            error: {
              borderColor: '#d63638',
              color: '#d63638'
            }
          },
          submitButton: {
            base: {
              color: settings.checkout_ui_styles?.submit_button?.text_color || '#000000',
              background: settings.checkout_ui_styles?.submit_button?.background || '#2ab5c4',
              borderRadius: settings.checkout_ui_styles?.submit_button?.border_radius || '10px',
              borderColor: settings.checkout_ui_styles?.submit_button?.border_color || '#2ab5c4',
              fontFamily: settings.checkout_ui_styles?.submit_button?.font_family || 'Helvetica, Arial, sans-serif',
              fontSize: settings.checkout_ui_styles?.submit_button?.font_size || '17px',
              fontWeight: settings.checkout_ui_styles?.submit_button?.font_weight || 'bold',
              boxShadow: 'none'
            },
            disabled: {
              color: '#9b9b9b',
              background: '#e1deda'
            }
          },
          loadingScreen: {
            color: settings.checkout_ui_styles?.submit_button?.background || '#2ab5c4'
          }
        },
        errorMessage: {
          disabled: true
        },
        successScreen: false,
        // Disable success screen as we handle it manually
        onCheckoutComplete: data => {
          console.log('Primer checkout complete:', data);
          handlePaymentComplete(data, checkoutData);
        },
        onCheckoutFail: (error, {
          payment
        }) => {
          console.error('Primer checkout failed:', error, payment);
          let errorMessage = settings.i18n?.generic_error || 'Payment failed';
          if (error && error.message) {
            errorMessage = error.message;
          } else if (payment && payment.processor && payment.processor.message) {
            errorMessage = payment.processor.message;
          }
          alert(errorMessage);
          resetStates();
        },
        onCheckoutCancel: () => {
          console.log('Primer checkout cancelled');
          resetStates();
        }
      };

      // Initialize Primer Universal Checkout - this will show the form with loading indicator
      await Primer.showUniversalCheckout(token, primerOptions);

      // Mark as initialized
      setIsInitialized(true);
      setContainerInitialized(); // Mark persistent container as initialized
      setIsLoading(false);
    } catch (error) {
      console.error('Error initializing Primer checkout:', error);
      setIsLoading(false);
      alert(error.message || settings.i18n?.generic_error || 'Failed to initialize payment form');
    } finally {
      isInitializingPrimerRef.current = false;
    }
  }, [settings, containerId, loadPrimerSDK, handlePaymentComplete, resetStates, isInitialized, containerIdActive, setContainerInitialized]);

  // Initialize payment when conditions are met - with persistent container support
  (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {
    const initializePayment = async () => {
      // If we have an existing initialized container, just show it and return
      if (shouldUseExistingContainer) {
        setIsInitialized(true);
        setIsLoading(false);

        // Restore persistent data to local state
        if (persistentClientToken && !clientToken) {
          setClientToken(persistentClientToken);
        }
        if (persistentOrderId && !orderId) {
          setOrderId(persistentOrderId);
        }
        showContainer();
        return;
      }

      // Prevent any initialization if already done or in progress
      if (isInitialized || isLoading || isCreatingOrderRef.current || isInitializingPrimerRef.current) {
        return;
      }

      // Check if we have an existing order ID (required for token generation)
      if (!existingOrderId) {
        return;
      }

      // Check if guest has provided all required information before proceeding
      if (!hasRequiredInfo) {
        return;
      }

      // Create unique reference for billing data to prevent duplicate initialization
      const currentBillingRef = billing?.billingAddress ? `${billing.billingAddress.email}_${billing.billingAddress.first_name}_${billing.billingAddress.last_name}` : null;

      // We should initialize if we haven't already, and we have billing info or are on checkout page
      const hasBillingData = billing?.billingAddress || document.body.classList.contains('woocommerce-checkout');
      const billingDataChanged = currentBillingRef !== billingRef;
      if (!hasBillingData || !billingDataChanged) {
        return;
      }
      try {
        setIsLoading(true);
        setBillingRef(currentBillingRef); // Set ref to prevent re-initialization with same data

        const result = await createOrderAndGetToken();
        if (result && result.token) {
          await initializePrimerCheckout(result.token, result.checkoutData);
        } else {
          console.warn('No token received from order creation');
          setIsLoading(false);
        }
      } catch (error) {
        console.error('Error during payment initialization:', error);
        setIsLoading(false);
        alert(error.message || 'Failed to initialize payment form.');
      }
    };

    // Use a small delay to prevent rapid successive calls during React hydration
    const timeoutId = setTimeout(initializePayment, 100);
    return () => clearTimeout(timeoutId);
  }, [existingOrderId,
  // Include existing order ID to trigger when it becomes available
  hasRequiredInfo,
  // Replace individual billing field dependencies with this
  shouldUseExistingContainer, showContainer, persistentOrderId, persistentClientToken]); // Only depend on required info validation and container state

  return {
    isInitialized: isInitialized || shouldUseExistingContainer,
    isLoading,
    clientToken: clientToken || persistentClientToken,
    // Return persistent token if available
    orderId: existingOrderId || orderId || persistentOrderId,
    // Prioritize existing order ID
    resetStates,
    containerRef: targetRef // Return ref for the target container
  };
};

/***/ }),

/***/ "@woocommerce/block-data":
/*!**************************************!*\
  !*** external ["wc","wcBlocksData"] ***!
  \**************************************/
/***/ ((module) => {

module.exports = wc.wcBlocksData;

/***/ }),

/***/ "@woocommerce/blocks-registry":
/*!******************************************!*\
  !*** external ["wc","wcBlocksRegistry"] ***!
  \******************************************/
/***/ ((module) => {

module.exports = wc.wcBlocksRegistry;

/***/ }),

/***/ "@woocommerce/settings":
/*!************************************!*\
  !*** external ["wc","wcSettings"] ***!
  \************************************/
/***/ ((module) => {

module.exports = wc.wcSettings;

/***/ }),

/***/ "@wordpress/data":
/*!******************************!*\
  !*** external ["wp","data"] ***!
  \******************************/
/***/ ((module) => {

module.exports = window["wp"]["data"];

/***/ }),

/***/ "@wordpress/element":
/*!*********************************!*\
  !*** external ["wp","element"] ***!
  \*********************************/
/***/ ((module) => {

module.exports = window["wp"]["element"];

/***/ }),

/***/ "@wordpress/html-entities":
/*!**************************************!*\
  !*** external ["wp","htmlEntities"] ***!
  \**************************************/
/***/ ((module) => {

module.exports = window["wp"]["htmlEntities"];

/***/ }),

/***/ "@wordpress/i18n":
/*!******************************!*\
  !*** external ["wp","i18n"] ***!
  \******************************/
/***/ ((module) => {

module.exports = window["wp"]["i18n"];

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/compat get default export */
/******/ 	(() => {
/******/ 		// getDefaultExport function for compatibility with non-harmony modules
/******/ 		__webpack_require__.n = (module) => {
/******/ 			var getter = module && module.__esModule ?
/******/ 				() => (module['default']) :
/******/ 				() => (module);
/******/ 			__webpack_require__.d(getter, { a: getter });
/******/ 			return getter;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/************************************************************************/
var __webpack_exports__ = {};
// This entry needs to be wrapped in an IIFE because it needs to be isolated against other modules in the chunk.
(() => {
/*!****************************************************!*\
  !*** ./assets/js/src/blocks/monoova-card-block.js ***!
  \****************************************************/
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _woocommerce_blocks_registry__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @woocommerce/blocks-registry */ "@woocommerce/blocks-registry");
/* harmony import */ var _woocommerce_blocks_registry__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_woocommerce_blocks_registry__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _wordpress_html_entities__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @wordpress/html-entities */ "@wordpress/html-entities");
/* harmony import */ var _wordpress_html_entities__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_wordpress_html_entities__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _woocommerce_settings__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @woocommerce/settings */ "@woocommerce/settings");
/* harmony import */ var _woocommerce_settings__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_woocommerce_settings__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @wordpress/i18n */ "@wordpress/i18n");
/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @wordpress/element */ "@wordpress/element");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var _wordpress_data__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @wordpress/data */ "@wordpress/data");
/* harmony import */ var _wordpress_data__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_wordpress_data__WEBPACK_IMPORTED_MODULE_5__);
/* harmony import */ var _woocommerce_block_data__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @woocommerce/block-data */ "@woocommerce/block-data");
/* harmony import */ var _woocommerce_block_data__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_woocommerce_block_data__WEBPACK_IMPORTED_MODULE_6__);
/* harmony import */ var _primer_checkout_container__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./primer-checkout-container */ "./assets/js/src/blocks/primer-checkout-container.js");
/* harmony import */ var _hooks_usePrimerCheckoutInitialization__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../hooks/usePrimerCheckoutInitialization */ "./assets/js/src/hooks/usePrimerCheckoutInitialization.js");









/**
 * Internal dependencies
 */
// We might need specific components for card fields later
// import CardFields from './components/card-fields';

// Defensive check for WooCommerce availability
if (typeof _woocommerce_blocks_registry__WEBPACK_IMPORTED_MODULE_0__.registerPaymentMethod === 'undefined') {
  console.warn('WooCommerce Blocks registerPaymentMethod is not available. This may be due to edit mode or missing dependencies.');
} else {
  // Main logic - only execute if registerPaymentMethod is available

  // Try to get settings from WooCommerce settings registry first, then fallback to global variable
  let settings = {};
  try {
    // Check if getSetting is available before using it
    if (typeof _woocommerce_settings__WEBPACK_IMPORTED_MODULE_2__.getSetting !== 'undefined') {
      // The key should match the payment method name (monoova_card), not monoova_card_data
      settings = (0,_woocommerce_settings__WEBPACK_IMPORTED_MODULE_2__.getSetting)('monoova_card_data', {});
    } else {
      throw new Error('getSetting function not available');
    }
  } catch (error) {
    console.error('getSetting not available, trying fallback:', error);
    // Fallback to global variable if getSetting is not available (e.g., in edit mode)
    settings = window.monoova_card_blocks_params || {};
  }
  if (!settings || typeof settings !== 'object' || Object.keys(settings).length === 0) {
    console.warn("Monoova Card settings not found or empty, using defaults");
    settings = {
      title: 'Credit / Debit Card',
      description: 'Accept payments via Mastercard, Visa, Apple pay and Google pay',
      supports: []
    };
  }
  const defaultLabel = (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)('Credit / Debit Card', 'monoova-payments-for-woocommerce');
  const label = (0,_wordpress_html_entities__WEBPACK_IMPORTED_MODULE_1__.decodeEntities)(settings.title) || defaultLabel;

  /**
   * Content component with Primer Universal Checkout
   */
  const Content = ({
    billing,
    shippingData,
    checkoutStatus,
    paymentStatus,
    eventRegistration,
    emitResponse
  }) => {
    const description = (0,_wordpress_html_entities__WEBPACK_IMPORTED_MODULE_1__.decodeEntities)(settings.description || '');

    // Get the existing order ID from WooCommerce checkout store
    const {
      orderId
    } = (0,_wordpress_data__WEBPACK_IMPORTED_MODULE_5__.useSelect)(select => {
      const store = select(_woocommerce_block_data__WEBPACK_IMPORTED_MODULE_6__.CHECKOUT_STORE_KEY);
      return {
        orderId: store.getOrderId()
      };
    });

    // Helper function to check if required guest information is available
    const hasRequiredGuestInfo = () => {
      const billingAddress = billing?.billingAddress;
      if (!billingAddress) return false;

      // Required fields for guest customers to generate client token
      return !!(billingAddress.email && billingAddress.first_name && billingAddress.last_name && billingAddress.address_1 && billingAddress.city && billingAddress.postcode && billingAddress.country && billingAddress.state && billingAddress.city);
    };

    // Use the custom hook for Primer checkout initialization
    const {
      isInitialized,
      isLoading,
      clientToken,
      orderId: hookOrderId,
      resetStates,
      containerRef
    } = (0,_hooks_usePrimerCheckoutInitialization__WEBPACK_IMPORTED_MODULE_8__.usePrimerCheckoutInitialization)({
      settings,
      billing,
      shippingData,
      orderId,
      // Pass the existing order ID from checkout store
      containerId: "in-checkout-primer-sdk-container",
      paymentMethodId: "monoova_card",
      hasRequiredInfo: hasRequiredGuestInfo() // Pass the validation result
    });
    (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_4__.useEffect)(() => {
      // Selector for the block checkout's "Place Order" button
      const placeOrderButton = document.querySelector('.wc-block-components-button.wp-element-button.wc-block-components-checkout-place-order-button');
      if (placeOrderButton) {
        // Hide the button when Monoova Card is selected
        placeOrderButton.style.display = 'none';
      }

      // Cleanup function: runs when component unmounts (e.g., user selects another payment method)
      return () => {
        if (placeOrderButton) {
          // Restore the button's default display style
          placeOrderButton.style.display = '';
        }
      };
    }, []);
    return /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("div", {
      className: "monoova-card-content",
      dangerouslySetInnerHTML: {
        __html: description
      }
    }), !hasRequiredGuestInfo() && !isInitialized && !isLoading && /*#__PURE__*/React.createElement("div", {
      className: "monoova-card-info"
    }, /*#__PURE__*/React.createElement("p", null, "Please complete your billing information to initialize the payment form."), /*#__PURE__*/React.createElement("small", null, "Required: Email, Name, Address, City, Postcode, and Country")), /*#__PURE__*/React.createElement(_primer_checkout_container__WEBPACK_IMPORTED_MODULE_7__.PrimerCheckoutContainer, {
      containerId: "in-checkout-primer-sdk-container",
      isInitialized: isInitialized,
      isLoading: isLoading,
      containerRef: containerRef
    }));
  };

  /**
   * Label component
   *
   * @param {Object} props Props from payment API.
   */
  const Label = props => {
    const {
      PaymentMethodLabel
    } = props.components || {};
    return /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        width: '100%'
      }
    }, /*#__PURE__*/React.createElement("span", {
      style: {
        fontWeight: 600
      }
    }, label), /*#__PURE__*/React.createElement("img", {
      src: `${settings.plugin_url || '/wp-content/plugins/monoova-payments-for-woocommerce/'}assets/images/card-payment-method-types.svg`,
      alt: "Card payment methods",
      style: {
        height: '32px',
        maxHeight: '32px',
        width: 'auto'
      }
    }));
  };

  /**
   * Edit component (for block editor - don't initialize payment)
   */
  const Edit = () => {
    const description = (0,_wordpress_html_entities__WEBPACK_IMPORTED_MODULE_1__.decodeEntities)(settings.description || '');
    return /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("div", {
      className: "monoova-card-content",
      dangerouslySetInnerHTML: {
        __html: description
      }
    }), /*#__PURE__*/React.createElement("div", {
      className: "embedded-card-form"
    }, /*#__PURE__*/React.createElement("div", {
      id: "in-checkout-primer-sdk-container"
    })));
  };

  /**
   * Monoova Card payment method config object.
   */
  const MonoovaCard = {
    name: "monoova_card",
    // Matches the name in PHP
    label: /*#__PURE__*/React.createElement(Label, null),
    content: /*#__PURE__*/React.createElement(Content, null),
    edit: /*#__PURE__*/React.createElement(Edit, null),
    canMakePayment: () => true,
    // Basic check, might need refinement
    ariaLabel: label,
    supports: {
      features: settings?.supports ?? []
      // Add other supported features like savePaymentInfo if applicable
    },
    // Add icons if available in settings
    icons: settings?.icons ?? null
  };

  // Register the payment method
  try {
    (0,_woocommerce_blocks_registry__WEBPACK_IMPORTED_MODULE_0__.registerPaymentMethod)(MonoovaCard);
  } catch (error) {
    console.error('Failed to register Monoova Card payment method:', error);
  }
}
})();

/******/ })()
;
//# sourceMappingURL=monoova-card-block.js.map