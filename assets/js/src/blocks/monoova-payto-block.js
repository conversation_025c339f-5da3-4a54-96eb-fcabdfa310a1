/**
 * Monoova PayTo Block for WooCommerce Blocks
 *
 * Uses ES6 imports with defensive programming to handle cases where WooCommerce dependencies
 * may not be available (edit mode, missing plugins, etc.)
 */

import { registerPaymentMethod } from "@woocommerce/blocks-registry"
import { getSetting } from "@woocommerce/settings"
import { decodeEntities } from "@wordpress/html-entities"
import { __ } from "@wordpress/i18n"
import { useState } from "@wordpress/element"

// If registerPaymentMethod is not available, we can't register the payment method
if (typeof registerPaymentMethod !== "function") {
    console.warn(
        "Monoova PayTo Block: registerPaymentMethod not available. Available globals:",
        Object.keys(window.wc || {})
    )
} else {
    // Try to get settings
    let settings = {}
    if (typeof getSetting === "function") {
        try {
            settings = getSetting("monoova_payto_data", {})
        } catch (error) {
            console.log("Monoova PayTo Block: getSetting failed:", error)
        }
    }

    // Fallback to global variable if getSetting didn't work
    if (!settings || Object.keys(settings).length === 0) {
        settings = window.monoova_payto_blocks_params || {}
        console.log("Monoova PayTo Block: Using fallback settings:", settings)
    }

    // Set defaults if no settings available
    if (!settings || Object.keys(settings).length === 0) {
        console.warn("Monoova PayTo Block: No settings found, using defaults")
        settings = {
            title: "PayTo",
            description: "Set up PayTo directly from your bank using BSB and Account Number or PayID.",
            supports: [],
        }
    }

    const defaultLabel = __("PayTo", "monoova-payments-for-woocommerce")
    const label = decodeEntities(settings.title) || defaultLabel

    /**
     * PayTo Payment Form Component
     */
    const PayToForm = ({ eventRegistration, emitResponse }) => {
        const [paymentMethod, setPaymentMethod] = useState("payid")
        const [payidType, setPayidType] = useState("PhoneNumber")
        const [payidValue, setPayidValue] = useState("")
        const [accountName, setAccountName] = useState("")
        const [bsb, setBsb] = useState("")
        const [accountNumber, setAccountNumber] = useState("")
        const [errors, setErrors] = useState({})

        const { onPaymentSetup } = eventRegistration
        const { responseTypes, noticeContexts } = emitResponse

        // Format mobile number input
        const formatMobileNumber = value => {
            // Remove all non-digits
            const numbers = value.replace(/\D/g, "")

            // Handle different input patterns
            if (numbers.startsWith("61")) {
                // Already has country code
                return "+61" + numbers.slice(2)
            } else if (numbers.startsWith("0")) {
                // Australian format starting with 0
                return numbers
            } else if (numbers.length <= 9 && !numbers.startsWith("0")) {
                // Could be without leading 0, add it
                return "0" + numbers
            }

            return value
        }

        // Validation function
        const validateForm = () => {
            const newErrors = {}

            if (paymentMethod === "payid") {
                if (!payidValue.trim()) {
                    newErrors.payidValue = __("PayID is required.", "monoova-payments-for-woocommerce")
                } else {
                    // Validate based on PayID type
                    const value = payidValue.trim()
                    switch (payidType) {
                        case "PhoneNumber":
                            if (!/^(\+61\s?[4-5]\d{8}|0[4-5]\d{8})$/.test(value)) {
                                newErrors.payidValue = __(
                                    "Please enter a valid Australian mobile number (e.g., 0412345678 or +***********).",
                                    "monoova-payments-for-woocommerce"
                                )
                            }
                            break
                        case "Email":
                            if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
                                newErrors.payidValue = __(
                                    "Please enter a valid email address.",
                                    "monoova-payments-for-woocommerce"
                                )
                            }
                            break
                        case "ABN":
                            if (!/^\d{11}$/.test(value.replace(/\s/g, ""))) {
                                newErrors.payidValue = __(
                                    "Please enter a valid 11-digit ABN.",
                                    "monoova-payments-for-woocommerce"
                                )
                            }
                            break
                        case "ACN":
                            if (!/^\d{9}$/.test(value.replace(/\s/g, ""))) {
                                newErrors.payidValue = __(
                                    "Please enter a valid 9-digit ACN.",
                                    "monoova-payments-for-woocommerce"
                                )
                            }
                            break
                        case "OrganisationId":
                            if (!value || value.length < 2) {
                                newErrors.payidValue = __(
                                    "Please enter a valid Organisation ID.",
                                    "monoova-payments-for-woocommerce"
                                )
                            }
                            break
                    }
                }
            } else {
                if (!accountName.trim()) {
                    newErrors.accountName = __("Account name is required.", "monoova-payments-for-woocommerce")
                }
                if (!bsb.trim()) {
                    newErrors.bsb = __("BSB is required.", "monoova-payments-for-woocommerce")
                } else if (!/^\d{3}-?\d{3}$/.test(bsb.trim())) {
                    newErrors.bsb = __("Please enter a valid BSB (6 digits).", "monoova-payments-for-woocommerce")
                }
                if (!accountNumber.trim()) {
                    newErrors.accountNumber = __("Account number is required.", "monoova-payments-for-woocommerce")
                }
            }

            setErrors(newErrors)
            return Object.keys(newErrors).length === 0
        }

        // Register payment setup handler
        React.useEffect(() => {
            const unsubscribe = onPaymentSetup(() => {
                if (!validateForm()) {
                    return {
                        type: responseTypes.ERROR,
                        message: __("Please correct the errors in the PayTo form.", "monoova-payments-for-woocommerce"),
                        messageContext: noticeContexts.PAYMENTS,
                    }
                }

                // Return payment data
                return {
                    type: responseTypes.SUCCESS,
                    meta: {
                        paymentMethodData: {
                            payto_payment_method: paymentMethod,
                            payto_payid_type: paymentMethod === "payid" ? payidType : "",
                            payto_payid_value: paymentMethod === "payid" ? payidValue : "",
                            payto_account_name: paymentMethod === "bsb_account" ? accountName : "",
                            payto_bsb: paymentMethod === "bsb_account" ? bsb : "",
                            payto_account_number: paymentMethod === "bsb_account" ? accountNumber : "",
                        },
                    },
                }
            })

            return unsubscribe
        }, [onPaymentSetup, paymentMethod, payidType, payidValue, accountName, bsb, accountNumber, validateForm])

        return (
            <div
                className="monoova-payto-form"
                style={{
                    padding: "15px",
                    border: "1px solid #ddd",
                    borderRadius: "5px",
                    backgroundColor: "#f9f9f9",
                    margin: "10px 0",
                }}>
                {/* Payment method selection */}
                <div className="payto-payment-method-selection" style={{ marginBottom: "15px" }}>
                    <label style={{ display: "block", marginBottom: "10px", fontWeight: "500" }}>
                        {__("Pay with", "monoova-payments-for-woocommerce")}
                    </label>
                    <div>
                        <label style={{ marginRight: "20px", fontWeight: "normal" }}>
                            <input
                                type="radio"
                                name="payto_payment_method"
                                value="payid"
                                checked={paymentMethod === "payid"}
                                onChange={e => setPaymentMethod(e.target.value)}
                                style={{ marginRight: "8px" }}
                            />
                            {__("PayID", "monoova-payments-for-woocommerce")}
                        </label>
                        <label style={{ fontWeight: "normal" }}>
                            <input
                                type="radio"
                                name="payto_payment_method"
                                value="bsb_account"
                                checked={paymentMethod === "bsb_account"}
                                onChange={e => setPaymentMethod(e.target.value)}
                                style={{ marginRight: "8px" }}
                            />
                            {__("BSB and account number", "monoova-payments-for-woocommerce")}
                        </label>
                    </div>
                </div>

                {/* PayID Fields */}
                {paymentMethod === "payid" && (
                    <div
                        className="payto-fields-group"
                        style={{
                            padding: "10px",
                            backgroundColor: "#fff",
                            borderRadius: "3px",
                        }}>
                        <h4 style={{ marginTop: 0, marginBottom: "10px", color: "#333" }}>
                            {__("Enter your PayID details", "monoova-payments-for-woocommerce")}
                        </h4>

                        {/* PayID Type Selection */}
                        <div style={{ marginBottom: "15px" }}>
                            <label style={{ display: "block", marginBottom: "5px", fontWeight: "500" }}>
                                {__("PayID Type", "monoova-payments-for-woocommerce")} *
                            </label>
                            <select
                                value={payidType}
                                onChange={e => {
                                    setPayidType(e.target.value)
                                    setPayidValue("") // Clear value when type changes
                                }}
                                style={{
                                    width: "100%",
                                    padding: "8px",
                                    border: "1px solid #ddd",
                                    borderRadius: "3px",
                                }}>
                                <option value="PhoneNumber">
                                    {__("Mobile Number", "monoova-payments-for-woocommerce")}
                                </option>
                                <option value="Email">{__("Email Address", "monoova-payments-for-woocommerce")}</option>
                                <option value="ABN">
                                    {__("ABN (Australian Business Number)", "monoova-payments-for-woocommerce")}
                                </option>
                                <option value="ACN">
                                    {__("ACN (Australian Company Number)", "monoova-payments-for-woocommerce")}
                                </option>
                                <option value="OrganisationId">
                                    {__("Organisation ID", "monoova-payments-for-woocommerce")}
                                </option>
                            </select>
                        </div>

                        {/* PayID Value Input */}
                        <div style={{ marginBottom: "10px" }}>
                            <label style={{ display: "block", marginBottom: "5px", fontWeight: "500" }}>
                                {payidType === "PhoneNumber" && __("Mobile Number", "monoova-payments-for-woocommerce")}
                                {payidType === "Email" && __("Email Address", "monoova-payments-for-woocommerce")}
                                {payidType === "ABN" && __("ABN", "monoova-payments-for-woocommerce")}
                                {payidType === "ACN" && __("ACN", "monoova-payments-for-woocommerce")}
                                {payidType === "OrganisationId" &&
                                    __("Organisation ID", "monoova-payments-for-woocommerce")}
                                {" *"}
                            </label>
                            <input
                                type={payidType === "Email" ? "email" : payidType === "PhoneNumber" ? "tel" : "text"}
                                value={payidValue}
                                onChange={e => {
                                    let value = e.target.value
                                    // Format mobile number if it's a phone number
                                    if (payidType === "PhoneNumber") {
                                        value = formatMobileNumber(value)
                                    }
                                    setPayidValue(value)
                                }}
                                placeholder={
                                    payidType === "PhoneNumber"
                                        ? __("0412345678 or +***********", "monoova-payments-for-woocommerce")
                                        : payidType === "Email"
                                        ? __("<EMAIL>", "monoova-payments-for-woocommerce")
                                        : payidType === "ABN"
                                        ? __("*********01", "monoova-payments-for-woocommerce")
                                        : payidType === "ACN"
                                        ? __("*********", "monoova-payments-for-woocommerce")
                                        : __("Enter Organisation ID", "monoova-payments-for-woocommerce")
                                }
                                style={{
                                    width: "100%",
                                    padding: "8px",
                                    border: "1px solid #ddd",
                                    borderRadius: "3px",
                                    borderColor: errors.payidValue ? "#e74c3c" : "#ddd",
                                }}
                            />
                            {errors.payidValue && (
                                <div style={{ color: "#e74c3c", fontSize: "12px", marginTop: "5px" }}>
                                    {errors.payidValue}
                                </div>
                            )}
                            <div style={{ color: "#666", fontSize: "12px", marginTop: "5px" }}>
                                {payidType === "PhoneNumber" &&
                                    __(
                                        "Enter your mobile number with or without country code",
                                        "monoova-payments-for-woocommerce"
                                    )}
                                {payidType === "Email" &&
                                    __(
                                        "Use the email address registered as your PayID",
                                        "monoova-payments-for-woocommerce"
                                    )}
                                {payidType === "ABN" &&
                                    __("Enter your 11-digit ABN", "monoova-payments-for-woocommerce")}
                                {payidType === "ACN" &&
                                    __("Enter your 9-digit ACN", "monoova-payments-for-woocommerce")}
                                {payidType === "OrganisationId" &&
                                    __("Enter your organisation identifier", "monoova-payments-for-woocommerce")}
                            </div>
                        </div>
                    </div>
                )}

                {/* BSB and Account Fields */}
                {paymentMethod === "bsb_account" && (
                    <div
                        className="payto-fields-group"
                        style={{
                            padding: "10px",
                            backgroundColor: "#fff",
                            borderRadius: "3px",
                        }}>
                        <h4 style={{ marginTop: 0, marginBottom: "10px", color: "#333" }}>
                            {__("Enter your bank details", "monoova-payments-for-woocommerce")}
                        </h4>
                        <div style={{ marginBottom: "10px" }}>
                            <label style={{ display: "block", marginBottom: "5px", fontWeight: "500" }}>
                                {__("Name associated with bank account", "monoova-payments-for-woocommerce")} *
                            </label>
                            <input
                                type="text"
                                value={accountName}
                                onChange={e => setAccountName(e.target.value)}
                                placeholder={__("Enter your name", "monoova-payments-for-woocommerce")}
                                style={{
                                    width: "100%",
                                    padding: "8px",
                                    border: "1px solid #ddd",
                                    borderRadius: "3px",
                                    borderColor: errors.accountName ? "#e74c3c" : "#ddd",
                                }}
                            />
                            {errors.accountName && (
                                <div style={{ color: "#e74c3c", fontSize: "12px", marginTop: "5px" }}>
                                    {errors.accountName}
                                </div>
                            )}
                        </div>
                        <div style={{ display: "flex", gap: "10px", marginBottom: "10px" }}>
                            <div style={{ flex: 1 }}>
                                <label style={{ display: "block", marginBottom: "5px", fontWeight: "500" }}>
                                    {__("BSB", "monoova-payments-for-woocommerce")} *
                                </label>
                                <input
                                    type="text"
                                    value={bsb}
                                    onChange={e => setBsb(e.target.value)}
                                    placeholder={__("Enter BSB", "monoova-payments-for-woocommerce")}
                                    style={{
                                        width: "100%",
                                        padding: "8px",
                                        border: "1px solid #ddd",
                                        borderRadius: "3px",
                                        borderColor: errors.bsb ? "#e74c3c" : "#ddd",
                                    }}
                                />
                                {errors.bsb && (
                                    <div style={{ color: "#e74c3c", fontSize: "12px", marginTop: "5px" }}>
                                        {errors.bsb}
                                    </div>
                                )}
                            </div>
                            <div style={{ flex: 1 }}>
                                <label style={{ display: "block", marginBottom: "5px", fontWeight: "500" }}>
                                    {__("Account Number", "monoova-payments-for-woocommerce")} *
                                </label>
                                <input
                                    type="text"
                                    value={accountNumber}
                                    onChange={e => setAccountNumber(e.target.value)}
                                    placeholder={__("Enter your account number", "monoova-payments-for-woocommerce")}
                                    style={{
                                        width: "100%",
                                        padding: "8px",
                                        border: "1px solid #ddd",
                                        borderRadius: "3px",
                                        borderColor: errors.accountNumber ? "#e74c3c" : "#ddd",
                                    }}
                                />
                                {errors.accountNumber && (
                                    <div style={{ color: "#e74c3c", fontSize: "12px", marginTop: "5px" }}>
                                        {errors.accountNumber}
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                )}

                <div
                    style={{
                        marginTop: "15px",
                        padding: "10px",
                        backgroundColor: "#e7f3ff",
                        borderRadius: "3px",
                        fontSize: "14px",
                    }}>
                    <strong>{__("Authorise recurring payments", "monoova-payments-for-woocommerce")}</strong>
                    <p style={{ margin: "5px 0 0 0", color: "#666" }}>
                        {__(
                            "Approve your recurring payment in your online banking or banking app!",
                            "monoova-payments-for-woocommerce"
                        )}
                    </p>
                </div>
            </div>
        )
    }

    /**
     * Content component for PayTo
     */
    const Content = function (props) {
        return <PayToForm {...props} />
    }

    /**
     * Label component with PayTo icon
     */
    const Label = props => {
        return (
            <div
                style={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    width: "100%",
                }}>
                <span style={{ fontWeight: 600 }}>{label}</span>
                <img
                    src={`${
                        settings.plugin_url || "/wp-content/plugins/monoova-payments-for-woocommerce/"
                    }assets/images/payto-logo.svg`}
                    alt="PayTo logo"
                    style={{
                        height: "24px",
                        width: "auto",
                    }}
                />
            </div>
        )
    }

    /**
     * Monoova PayTo payment method config object
     */
    const MonoovaPayTo = {
        name: "monoova_payto",
        label: <Label />,
        content: <Content />,
        edit: <Content />,
        canMakePayment: function () {
            return true
        },
        ariaLabel: label,
        supports: {
            features: settings.supports || [],
        },
        icons: settings.icons || null,
    }

    // Register the payment method
    try {
        registerPaymentMethod(MonoovaPayTo)
    } catch (error) {
        console.error("Monoova PayTo Block: Failed to register payment method:", error)
    }
}
