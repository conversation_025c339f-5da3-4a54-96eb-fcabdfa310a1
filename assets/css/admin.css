/**
 * Monoova Payments for WooCommerce - Admin Styles
 */

/* Dashboard Layout */
.monoova-dashboard {
    margin: 20px 0;
}

.monoova-dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 3px;
}

.monoova-dashboard-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: 20px;
    margin-top: 20px;
}

@media screen and (min-width: 768px) {
    .monoova-dashboard-content {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .monoova-dashboard-content .monoova-card:first-child {
        grid-column: 1 / -1;
    }
}

/* Dashboard Wrapper */
.monoova-admin-wrap {
    margin: 20px 20px 20px 0;
}

/* Header */
.monoova-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e2e4e7;
}

.monoova-header h1 {
    margin: 0;
    padding: 0;
}

.monoova-header-actions {
    display: flex;
    gap: 10px;
}

/* Notices */
.monoova-notice {
    padding: 10px 12px;
    margin: 0 0 20px;
    border-radius: 3px;
    border-left: 4px solid #72aee6;
    background-color: #f0f6fc;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
}

.monoova-notice.notice-success {
    border-color: #46b450;
    background-color: #ecf7ed;
}

.monoova-notice.notice-warning {
    border-color: #ffb900;
    background-color: #fff8e5;
}

.monoova-notice.notice-error {
    border-color: #dc3232;
    background-color: #fbeaea;
}

/* Cards */
.monoova-card {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
    flex: 1;
    min-width: 300px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.monoova-card-header {
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
    background-color: #f9f9f9;
}

.monoova-card-header h2 {
    margin: 0;
    font-size: 16px;
    line-height: 1.4;
}

.monoova-card-content {
    padding: 15px;
}

.monoova-card-body {
    padding: 20px;
    flex-grow: 1;
}

.monoova-card-footer {
    padding: 15px 20px;
    border-top: 1px solid #eee;
    background-color: #f8f9fa;
    text-align: right;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* Card Colors */
.monoova-card-today {
    border-top: 3px solid #2271b1;
}

.monoova-card-month {
    border-top: 3px solid #0096c7;
}

.monoova-card-total {
    border-top: 3px solid #6366f1;
}

.monoova-card-refunds {
    border-top: 3px solid #f43f5e;
}

.monoova-card-transactions {
    width: 100%;
    flex-basis: 100%;
}

/* Stats */
.monoova-stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    margin-bottom: 15px;
}

@media screen and (min-width: 768px) {
    .monoova-stats-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

.monoova-stats-amounts {
    display: grid;
    grid-template-columns: repeat(1, 1fr);
    gap: 15px;
    margin-bottom: 15px;
}

@media screen and (min-width: 768px) {
    .monoova-stats-amounts {
        grid-template-columns: repeat(2, 1fr);
    }
}

.monoova-payment-methods {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
}

.monoova-stat-box {
    padding: 15px;
    background-color: #f9f9f9;
    border: 1px solid #eee;
    border-radius: 3px;
    text-align: center;
}

.monoova-stat-box.monoova-wide {
    grid-column: span 1;
}

@media screen and (min-width: 768px) {
    .monoova-stat-box.monoova-wide {
        grid-column: span 1;
    }
}

.monoova-stat-title {
    display: block;
    font-size: 12px;
    font-weight: 600;
    color: #777;
    margin-bottom: 8px;
}

.monoova-stat-value {
    display: block;
    font-size: 28px;
    font-weight: 600;
    line-height: 1.2;
    color: #2271b1;
}

.monoova-stat-label {
    display: block;
    font-size: 14px;
    color: #646970;
}

.monoova-stat-large .monoova-stat-value {
    font-size: 40px;
    margin-bottom: 5px;
    text-align: center;
}

.monoova-stat-large .monoova-stat-label {
    text-align: center;
    font-size: 16px;
}

.monoova-refund-info {
    text-align: center;
    margin-top: 20px;
    color: #646970;
    font-style: italic;
}

.monoova-stat-success {
    color: #46b450;
}

.monoova-stat-pending {
    color: #ffba00;
}

.monoova-stat-failed {
    color: #dc3232;
}

/* Payment Methods */
.monoova-payment-methods-stats {
    margin-bottom: 15px;
}

.monoova-payment-method {
    display: flex;
    align-items: center;
    padding: 15px;
    border-radius: 4px;
    border: 1px solid #ddd;
    margin-bottom: 10px;
    background-color: #f8f9fa;
}

.monoova-payment-method:last-child {
    margin-bottom: 0;
}

.monoova-payment-method-icon {
    width: 40px;
    height: 40px;
    margin-right: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.monoova-payment-method-icon img {
    max-width: 100%;
    max-height: 100%;
}

.monoova-payment-method-info {
    flex-grow: 1;
}

.monoova-payment-method-info h4 {
    margin: 0 0 5px 0;
}

.monoova-payment-method-count {
    font-size: 12px;
    color: #646970;
}

.monoova-payment-method-status {
    margin-left: 15px;
}

.monoova-status {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.monoova-status-active {
    background-color: #ecf7ed;
    color: #2a7d3f;
}

.monoova-status-inactive {
    background-color: #f0f0f1;
    color: #646970;
}

/* Status */
.monoova-dashboard-status {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

@media screen and (min-width: 768px) {
    .monoova-dashboard-status {
        flex-direction: row;
        gap: 20px;
    }
}

.monoova-status-item {
    display: flex;
    align-items: center;
    gap: 10px;
}

.monoova-status-label {
    font-weight: 600;
}

.monoova-status-value {
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: 600;
}

.monoova-status-value.enabled {
    background: #f0f9eb;
    color: #67c23a;
}

.monoova-status-value.disabled {
    background: #f9f0eb;
    color: #e6a23c;
}

/* Badges */
.monoova-badge {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: 600;
}

.monoova-badge.monoova-production {
    background: #f0f9eb;
    color: #67c23a;
}

.monoova-badge.monoova-sandbox {
    background: #f0f9eb;
    color: #e6a23c;
}

.monoova-status-badge {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: 600;
}

.monoova-status-success {
    background: #f0f9eb;
    color: #67c23a;
}

.monoova-status-pending {
    background: #fdf6ec;
    color: #e6a23c;
}

.monoova-status-failed {
    background: #fef0f0;
    color: #f56c6c;
}

/* Tables */
.monoova-orders-table {
    width: 100%;
    border-collapse: collapse;
}

.monoova-orders-table th,
.monoova-orders-table td {
    padding: 10px;
    border-bottom: 1px solid #eee;
}

.monoova-orders-table th {
    text-align: left;
    font-weight: 600;
}

.monoova-transaction-id {
    display: block;
    font-size: 11px;
    color: #888;
    margin-top: 3px;
}

/* Transactions Table */
.monoova-transactions-table {
    width: 100%;
    border-collapse: collapse;
}

.monoova-transactions-table th,
.monoova-transactions-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.monoova-transactions-table th {
    font-weight: 600;
    color: #1d2327;
    background-color: #f8f9fa;
}

.monoova-transactions-table tbody tr:hover {
    background-color: #f6f7f7;
}

.monoova-transactions-table .order-status {
    display: inline-flex;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 12px;
    background: #f0f0f1;
    color: #50575e;
}

.monoova-transactions-table .status-completed {
    background-color: #ecf7ed;
    color: #2a7d3f;
}

.monoova-transactions-table .status-processing {
    background-color: #eaf2fa;
    color: #2271b1;
}

.monoova-transactions-table .status-on-hold {
    background-color: #f6f7f7;
    color: #646970;
}

.monoova-transactions-table .status-failed {
    background-color: #fbeaea;
    color: #b32d2e;
}

.monoova-transactions-table .status-refunded {
    background-color: #f0f6fc;
    color: #2271b1;
}

.monoova-empty-state {
    text-align: center;
    padding: 40px 0;
    color: #646970;
}

/* Support links */
.monoova-support-links {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    gap: 15px;
}

.monoova-support-link {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 15px;
    background-color: #f9f9f9;
    border: 1px solid #eee;
    border-radius: 3px;
    text-decoration: none;
    color: #23282d;
    font-weight: 500;
    flex-basis: 100%;
}

@media screen and (min-width: 768px) {
    .monoova-support-link {
        flex-basis: calc(33.33% - 10px);
    }
}

.monoova-support-link:hover {
    background-color: #f0f0f0;
    border-color: #ddd;
    color: #0073aa;
}

.monoova-support-link .dashicons {
    color: #0073aa;
}

/* Resources */
.monoova-resources-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.monoova-resource-link {
    display: flex;
    align-items: center;
    padding: 12px;
    text-decoration: none;
    background-color: #f0f6fc;
    border-radius: 4px;
    border: 1px solid #c5d9ed;
    color: #2271b1;
    transition: all 0.2s ease;
}

.monoova-resource-link:hover {
    background-color: #e5f0fa;
    color: #135e96;
    border-color: #a3bedc;
}

.monoova-resource-link .dashicons {
    font-size: 20px;
    width: 20px;
    height: 20px;
    margin-right: 10px;
}

/* WooCommerce Settings */
.form-table .monoova-webhook-url {
    margin-top: 20px;
    padding: 15px;
    background-color: #f9f9f9;
    border: 1px solid #eee;
    border-radius: 3px;
}

.form-table .monoova-webhook-url h3 {
    margin-top: 0;
}

.form-table .monoova-webhook-url-value {
    display: block;
    padding: 10px;
    background: #fff;
    border: 1px solid #ddd;
    margin: 10px 0;
    word-break: break-all;
}

/* Webhook URL */
.monoova-webhook-url {
    display: flex;
    margin-top: 15px;
    align-items: center;
}

.monoova-webhook-url code {
    flex-grow: 1;
    padding: 10px;
    background-color: #f6f7f7;
    border: 1px solid #dcdcde;
    border-radius: 4px 0 0 4px;
    font-size: 13px;
    overflow-x: auto;
    white-space: nowrap;
}

.monoova-webhook-url .button {
    margin-left: -1px;
    border-radius: 0 4px 4px 0;
    height: 38px;
}

/* API Key Test Button */
.monoova-api-test-button {
    margin-top: 10px !important;
}

.monoova-test-result {
    margin-top: 10px;
    padding: 10px;
    border-radius: 3px;
    font-weight: 500;
}

.monoova-test-success {
    background-color: #f0f9eb;
    color: #67c23a;
}

.monoova-test-error {
    background-color: #fef0f0;
    color: #f56c6c;
}

/* Settings Section */
.monoova-section-title {
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
    margin-top: 20px;
}

.monoova-setting-description {
    margin-top: 5px;
    color: #777;
    font-style: italic;
}

.monoova-webhook-url {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 4px;
    border-left: 4px solid #2271b1;
    margin-top: 20px;
}

.monoova-webhook-url-value {
    display: block;
    background: #fff;
    padding: 10px;
    margin: 10px 0;
    border: 1px dashed #ddd;
    font-family: monospace;
    word-break: break-all;
}

/* Environment Indicator */
.environment-badge {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 12px;
    margin-left: 10px;
    vertical-align: middle;
}

.environment-badge.test {
    background-color: #ffeeba;
    color: #856404;
}

.environment-badge.live {
    background-color: #d4edda;
    color: #155724;
}

/* API Status Indicator */
.api-status {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 12px;
    margin-left: 10px;
}

.api-status.connected {
    background-color: #d4edda;
    color: #155724;
}

.api-status.not-connected {
    background-color: #f8d7da;
    color: #721c24;
}

/* Toggle Switch */
.monoova-toggle-switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
    margin-right: 10px;
}

.monoova-toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.monoova-toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 34px;
}

.monoova-toggle-slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .monoova-toggle-slider {
    background-color: #2196F3;
}

input:focus + .monoova-toggle-slider {
    box-shadow: 0 0 1px #2196F3;
}

input:checked + .monoova-toggle-slider:before {
    transform: translateX(26px);
}

/* Dashboard Widget */
.monoova-dashboard-widget .inside {
    padding: 0;
    margin: 0;
}

.monoova-stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-gap: 1px;
    background-color: #f0f0f1;
}

.monoova-stat-item {
    padding: 10px;
    background-color: #fff;
    text-align: center;
}

.monoova-stat-value {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 5px;
}

.monoova-stat-label {
    font-size: 13px;
    color: #757575;
    text-transform: uppercase;
}

.monoova-dashboard-footer {
    background: #f8f9fa;
    padding: 10px;
    text-align: center;
    border-top: 1px solid #eee;
}

/* Order Meta Box */
#monoova-payment-info .inside {
    padding: 0;
    margin: 0;
}

.monoova-payment-info-content {
    padding: 12px;
}

.monoova-meta-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

.monoova-meta-table td {
    padding: 8px 5px;
    border-bottom: 1px solid #f0f0f1;
}

.monoova-meta-table tr:last-child td {
    border-bottom: none;
}

.monoova-meta-label {
    color: #777;
    width: 40%;
}

.monoova-meta-value {
    font-weight: 600;
}

.monoova-expired {
    color: #dc3545;
}

.monoova-meta-actions {
    margin-top: 15px;
    padding-top: 12px;
    border-top: 1px solid #eee;
}

.monoova-meta-actions .button {
    margin-right: 5px;
}

/* Help Tips */
.monoova-help-tip {
    display: inline-block;
    cursor: help;
    color: #0073aa;
    margin: 0 3px;
}

.monoova-help-tip:hover {
    color: #0096dd;
}

/* Support Section */
.monoova-support-section {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 15px;
    margin-top: 20px;
}

.monoova-support-section h3 {
    margin-top: 0;
}

/* Copy API Key Field */
.monoova-copy-field-container {
    position: relative;
    display: flex;
}

.monoova-copy-button {
    position: absolute;
    right: 5px;
    top: 5px;
    background: #f1f1f1;
    border: 1px solid #ddd;
    border-radius: 3px;
    padding: 3px 8px;
    font-size: 12px;
    cursor: pointer;
}

.monoova-copy-button:hover {
    background: #e9e9e9;
}

/* Transactions Table */
.transactions-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 15px;
}

.transactions-table th,
.transactions-table td {
    padding: 8px 10px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.transactions-table th {
    background-color: #f8f9fa;
    font-weight: 600;
}

.status-badge {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 12px;
}

.status-badge.succeeded {
    background-color: #d4edda;
    color: #155724;
}

.status-badge.pending {
    background-color: #fff3cd;
    color: #856404;
}

.status-badge.failed {
    background-color: #f8d7da;
    color: #721c24;
}

.status-badge.refunded {
    background-color: #d1ecf1;
    color: #0c5460;
}

/* Settings Form */
.form-table td .monoova-nested-settings {
    margin-left: 25px;
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px dashed #eee;
}

.monoova-field-container {
    margin-bottom: 15px;
}

.monoova-field-container label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

.monoova-field-container input[type=text],
.monoova-field-container input[type=password],
.monoova-field-container select {
    width: 100%;
    max-width: 400px;
}

.monoova-field-container .description {
    margin-top: 5px;
    color: #777;
    font-style: italic;
}

/* Status Indicator */
.monoova-api-status-check {
    margin: 10px 0 20px;
    padding: 15px;
    border-radius: 4px;
    background-color: #f8f9fa;
}

.monoova-api-status {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.monoova-api-status-icon {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    margin-right: 10px;
}

.monoova-api-status-icon.success {
    background-color: #28a745;
}

.monoova-api-status-icon.error {
    background-color: #dc3545;
}

.monoova-api-status-icon.warning {
    background-color: #ffc107;
}

.monoova-api-status-text {
    font-weight: 500;
}

.monoova-api-status-description {
    margin-left: 28px;
    color: #777;
}

/* Documentation Links */
.monoova-documentation-links {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

.monoova-documentation-links ul {
    list-style: disc;
    padding-left: 20px;
}

.monoova-documentation-links ul li {
    margin-bottom: 8px;
}

/* Card Icons */
.card-icons {
    display: flex;
    gap: 5px;
    margin-top: 10px;
}

.card-icon {
    height: 25px;
    width: auto;
    border: 1px solid #eee;
    border-radius: 3px;
    padding: 3px;
}

/* Test Mode Notice */
.monoova-test-mode-notice {
    background: #fff3cd;
    border: 1px solid #ffeeba;
    padding: 10px 15px;
    margin-bottom: 20px;
    border-radius: 4px;
}

/* Responsive */
@media screen and (max-width: 782px) {
    .monoova-dashboard-row {
        flex-direction: column;
    }
    
    .monoova-card {
        min-width: auto;
    }
    
    .monoova-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .monoova-header-actions {
        margin-top: 15px;
    }
    
    .monoova-card-today,
    .monoova-card-month,
    .monoova-card-total {
        margin-right: 0;
        margin-bottom: 20px;
    }
    
    .monoova-transactions-table {
        display: block;
        overflow-x: auto;
    }
}